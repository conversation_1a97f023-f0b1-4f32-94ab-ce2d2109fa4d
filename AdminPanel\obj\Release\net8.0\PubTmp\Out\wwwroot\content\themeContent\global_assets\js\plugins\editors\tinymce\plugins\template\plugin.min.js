/**
 * Copyright (c) Tiny Technologies, Inc. All rights reserved.
 * Licensed under the LGPL or a commercial license.
 * For LGPL see License.txt in the project root for license information.
 * For commercial licenses see https://www.tiny.cloud/
 *
 * Version: 5.3.0 (2020-05-21)
 */
!function(){"use strict";var e=tinymce.util.Tools.resolve("tinymce.PluginManager"),t=function(){},o=function(e){return function(){return e}};var n,r,a,u=o(!1),i=o(!0),p=tinymce.util.Tools.resolve("tinymce.util.Tools"),c=tinymce.util.Tools.resolve("tinymce.util.XHR"),l=function(e){return e.getParam("template_replace_values")},s=function(e){return e.getParam("template_mdate_format",e.translate("%Y-%m-%d"))},f=function(e,t){if((e=""+e).length<t)for(var n=0;n<t-e.length;n++)e="0"+e;return e},m=function(e,t,n){var r="Sun Mon Tue Wed Thu Fri Sat Sun".split(" "),a="Sunday Monday Tuesday Wednesday Thursday Friday Saturday Sunday".split(" "),o="Jan Feb Mar Apr May Jun Jul Aug Sep Oct Nov Dec".split(" "),u="January February March April May June July August September October November December".split(" ");return n=n||new Date,t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=t.replace("%D","%m/%d/%Y")).replace("%r","%I:%M:%S %p")).replace("%Y",""+n.getFullYear())).replace("%y",""+n.getYear())).replace("%m",f(n.getMonth()+1,2))).replace("%d",f(n.getDate(),2))).replace("%H",""+f(n.getHours(),2))).replace("%M",""+f(n.getMinutes(),2))).replace("%S",""+f(n.getSeconds(),2))).replace("%I",""+((n.getHours()+11)%12+1))).replace("%p",n.getHours()<12?"AM":"PM")).replace("%B",""+e.translate(u[n.getMonth()]))).replace("%b",""+e.translate(o[n.getMonth()]))).replace("%A",""+e.translate(a[n.getDay()]))).replace("%a",""+e.translate(r[n.getDay()]))).replace("%%","%")},d=function(t,n){return function(){var e=t.templates;"function"!=typeof e?"string"==typeof e?c.send({url:e,success:function(e){n(JSON.parse(e))}}):n(e):e(n)}},g=function(n,e){return p.each(e,function(e,t){"function"==typeof e&&(e=e(t)),n=n.replace(new RegExp("\\{\\$"+t+"\\}","g"),e)}),n},v=function(e,t){var r=e.dom,a=l(e);p.each(r.select("*",t),function(n){p.each(a,function(e,t){r.hasClass(n,t)&&"function"==typeof a[t]&&a[t](n)})})},h=function(e,t){return new RegExp("\\b"+t+"\\b","g").test(e.className)},y=function(n,e,t){var r,a,o=n.dom,u=n.selection.getContent();t=g(t,l(n)),r=o.create("div",null,t),(a=o.select(".mceTmpl",r))&&0<a.length&&(r=o.create("div",null)).appendChild(a[0].cloneNode(!0)),p.each(o.select("*",r),function(e){var t;h(e,n.getParam("template_cdate_classes","cdate").replace(/\s+/g,"|"))&&(e.innerHTML=m(n,(t=n).getParam("template_cdate_format",t.translate("%Y-%m-%d")))),h(e,n.getParam("template_mdate_classes","mdate").replace(/\s+/g,"|"))&&(e.innerHTML=m(n,s(n))),h(e,n.getParam("template_selected_content_classes","selcontent").replace(/\s+/g,"|"))&&(e.innerHTML=u)}),v(n,r),n.execCommand("mceInsertContent",!1,r.innerHTML),n.addVisual()},b=function(e){e.addCommand("mceInsertTemplate",function t(r){for(var a=[],e=1;e<arguments.length;e++)a[e-1]=arguments[e];return function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var n=a.concat(e);return r.apply(null,n)}}(y,e))},M=function(){return T},T=(n=function(e){return e.isNone()},{fold:function(e,t){return e()},is:u,isSome:u,isNone:i,getOr:a=function(e){return e},getOrThunk:r=function(e){return e()},getOrDie:function(e){throw new Error(e||"error: getOrDie called on none.")},getOrNull:o(null),getOrUndefined:o(undefined),or:a,orThunk:r,map:M,each:t,bind:M,exists:u,forall:i,filter:M,equals:n,equals_:n,toArray:function(){return[]},toString:o("none()")}),_=function(n){var e=o(n),t=function(){return a},r=function(e){return e(n)},a={fold:function(e,t){return t(n)},is:function(e){return n===e},isSome:i,isNone:u,getOr:e,getOrThunk:e,getOrDie:e,getOrNull:e,getOrUndefined:e,or:t,orThunk:t,map:function(e){return _(e(n))},each:function(e){e(n)},bind:r,exists:r,forall:r,filter:function(e){return e(n)?a:T},toArray:function(){return[n]},toString:function(){return"some("+n+")"},equals:function(e){return e.is(n)},equals_:function(e,t){return e.fold(u,function(e){return t(n,e)})}};return a},O={some:_,none:M,from:function(e){return null===e||e===undefined?T:_(e)}},x=function(e,t){return function(e,t,n){for(var r=0,a=e.length;r<a;r++){var o=e[r];if(t(o,r))return O.some(o);if(n(o,r))break}return O.none()}(e,t,u)},S=tinymce.util.Tools.resolve("tinymce.util.Promise"),w=Object.hasOwnProperty,P=function(e,t){return w.call(e,t)},D={'"':"&quot;","<":"&lt;",">":"&gt;","&":"&amp;","'":"&#039;"},C=function(e){return e.replace(/["'<>&]/g,function(e){return(P(t=D,n=e)?O.from(t[n]):O.none()).getOr(e);var t,n})},A=function(l,t){var e=function(e){return function(e,t){for(var n=e.length,r=new Array(n),a=0;a<n;a++){var o=e[a];r[a]=t(o,a)}return r}(e,function(e){return{text:e.text,value:e.text}})},s=function(e,t){return x(e,function(e){return e.text===t})},f=function(e){l.windowManager.alert("Could not load the specified template.",function(){return e.focus("template")})},m=function(e){return new S(function(t,n){e.value.url.fold(function(){return t(e.value.content.getOr(""))},function(e){return c.send({url:e,success:function(e){t(e)},error:function(e){n(e)}})})})};(function(){if(t&&0!==t.length)return O.from(p.map(t,function(e,t){var n=function(e){return e.url!==undefined};return{selected:0===t,text:e.title,value:{url:n(e)?O.from(e.url):O.none(),content:n(e)?O.none():O.from(e.content),description:e.description}}}));var e=l.translate("No templates defined.");return l.notificationManager.open({text:e,type:"info"}),O.none()})().each(function(o){var u=e(o),i=function(e,t){return{title:"Insert Template",size:"large",body:{type:"panel",items:e},initialData:t,buttons:[{type:"cancel",name:"cancel",text:"Cancel"},{type:"submit",name:"save",text:"Save",primary:!0}],onSubmit:function(t){var e=t.getData();s(n,e.template).each(function(e){m(e).then(function(e){y(l,0,e),t.close()})["catch"](function(){t.disable("save"),f(t)})})},onChange:(r=n=o,a=c,function(n,e){if("template"===e.name){var t=n.getData().template;s(r,t).each(function(t){n.block("Loading..."),m(t).then(function(e){a(n,t,e)})["catch"](function(){a(n,t,""),n.disable("save"),f(n)})})}})};var r,a,n},c=function(e,t,n){var r=function(t,e){if(-1===e.indexOf("<html>")){var n="";p.each(t.contentCSS,function(e){n+='<link type="text/css" rel="stylesheet" href="'+t.documentBaseURI.toAbsolute(e)+'">'});var r=t.settings.body_class||"";-1!==r.indexOf("=")&&(r=(r=t.getParam("body_class","","hash"))[t.id]||"");var a=t.dom.encode,o=t.getBody().dir,u=o?' dir="'+a(o)+'"':"";e="<!DOCTYPE html><html><head>"+n+'</head><body class="'+a(r)+'"'+u+">"+e+"</body></html>"}return g(e,t.getParam("template_preview_replace_values"))}(l,n),a=[{type:"selectbox",name:"template",label:"Templates",items:u},{type:"htmlpanel",html:'<p aria-live="polite">'+C(t.value.description)+"</p>"},{label:"Preview",type:"iframe",name:"preview",sandboxed:!1}],o={template:t.text,preview:r};e.unblock(),e.redial(i(a,o)),e.focus("template")},t=l.windowManager.open(i([],{template:"",preview:""}));t.block("Loading..."),m(o[0]).then(function(e){c(t,o[0],e)})["catch"](function(){c(t,o[0],""),t.disable("save"),f(t)})})},N=function(t){return function(e){A(t,e)}};!function H(){e.add("template",function(e){var t,r;(t=e).ui.registry.addButton("template",{icon:"template",tooltip:"Insert template",onAction:d(t.settings,N(t))}),t.ui.registry.addMenuItem("template",{icon:"template",text:"Insert template...",onAction:d(t.settings,N(t))}),b(e),(r=e).on("PreProcess",function(e){var t=r.dom,n=s(r);p.each(t.select("div",e.node),function(e){t.hasClass(e,"mceTmpl")&&(p.each(t.select("*",e),function(e){t.hasClass(e,r.getParam("template_mdate_classes","mdate").replace(/\s+/g,"|"))&&(e.innerHTML=m(r,n))}),v(r,e))})})})}()}();