/**
 * Copyright (c) Tiny Technologies, Inc. All rights reserved.
 * Licensed under the LGPL or a commercial license.
 * For LGPL see License.txt in the project root for license information.
 * For commercial licenses see https://www.tiny.cloud/
 *
 * Version: 5.3.0 (2020-05-21)
 */
!function(m){"use strict";var n,t,e,u,o=tinymce.util.Tools.resolve("tinymce.PluginManager"),i=function(n){return function(){return n}},a=i(!1),c=i(!0),r=function(){return l},l=(n=function(n){return n.isNone()},{fold:function(n,t){return n()},is:a,isSome:a,isNone:c,getOr:e=function(n){return n},getOrThunk:t=function(n){return n()},getOrDie:function(n){throw new Error(n||"error: getOrDie called on none.")},getOrNull:i(null),getOrUndefined:i(undefined),or:e,orThunk:t,map:r,each:function(){},bind:r,exists:a,forall:c,filter:r,equals:n,equals_:n,toArray:function(){return[]},toString:i("none()")}),s=function(e){var n=i(e),t=function(){return r},o=function(n){return n(e)},r={fold:function(n,t){return t(e)},is:function(n){return e===n},isSome:c,isNone:a,getOr:n,getOrThunk:n,getOrDie:n,getOrNull:n,getOrUndefined:n,or:t,orThunk:t,map:function(n){return s(n(e))},each:function(n){n(e)},bind:o,exists:o,forall:o,filter:function(n){return n(e)?r:l},toArray:function(){return[e]},toString:function(){return"some("+e+")"},equals:function(n){return n.is(e)},equals_:function(n,t){return n.fold(a,function(n){return t(e,n)})}};return r},g={some:s,none:r,from:function(n){return null===n||n===undefined?l:s(n)}},f=function(n,t){return-1!==n.indexOf(t)},d=function(n,t){return f(n.title.toLowerCase(),t)||function(n,t){for(var e=0,o=n.length;e<o;e++){if(t(n[e],e))return!0}return!1}(n.keywords,function(n){return f(n.toLowerCase(),t)})},y=function(n,t,e){for(var o=[],r=t.toLowerCase(),i=e.fold(function(){return a},function(t){return function(n){return t<=n}}),u=0;u<n.length&&(0!==t.length&&!d(n[u],r)||(o.push({value:n[u]["char"],text:n[u].title,icon:n[u]["char"]}),!i(o.length)));u++);return o},h=function(n){var t=n;return{get:function(){return t},set:function(n){t=n}}},v=function(){return(v=Object.assign||function(n){for(var t,e=1,o=arguments.length;e<o;e++)for(var r in t=arguments[e])Object.prototype.hasOwnProperty.call(t,r)&&(n[r]=t[r]);return n}).apply(this,arguments)},p=Object.prototype.hasOwnProperty,b=(u=function(n,t){return t},function(){for(var n=new Array(arguments.length),t=0;t<n.length;t++)n[t]=arguments[t];if(0===n.length)throw new Error("Can't merge zero objects");for(var e={},o=0;o<n.length;o++){var r=n[o];for(var i in r)p.call(r,i)&&(e[i]=u(e[i],r[i]))}return e}),w=Object.keys,O=Object.hasOwnProperty,C=function(n,t){for(var e=w(n),o=0,r=e.length;o<r;o++){var i=e[o];t(n[i],i)}},k=function(n,o){var r={};return C(n,function(n,t){var e=o(n,t);r[e.k]=e.v}),r},j=tinymce.util.Tools.resolve("tinymce.Resource"),A=tinymce.util.Tools.resolve("tinymce.util.Delay"),T=tinymce.util.Tools.resolve("tinymce.util.Promise"),_="All",D={symbols:"Symbols",people:"People",animals_and_nature:"Animals and Nature",food_and_drink:"Food and Drink",activity:"Activity",travel_and_places:"Travel and Places",objects:"Objects",flags:"Flags",user:"User Defined"},P=function(n,t){return e=n,o=t,O.call(e,o)?n[t]:t;var e,o},x=function(n){var e,t=n.getParam("emoticons_append",{},"object");return e=function(n){return v({keywords:[],category:"user"},n)},k(t,function(n,t){return{k:t,v:e(n,t)}})},L=function(o,r,n){var u=h(g.none()),a=h(g.none());o.on("init",function(){j.load(n,r).then(function(n){var t,r,i,e=x(o);t=b(n,e),r={},i=[],C(t,function(n,t){var e={title:t,keywords:n.keywords,"char":n["char"],category:P(D,n.category)},o=r[e.category]!==undefined?r[e.category]:[];r[e.category]=o.concat([e]),i.push(e)}),u.set(g.some(r)),a.set(g.some(i))},function(n){m.console.log("Failed to load emoticons: "+n),u.set(g.some({})),a.set(g.some([]))})});var e=function(){return a.get().getOr([])},i=function(){return u.get().isSome()&&a.get().isSome()};return{listCategories:function(){return[_].concat(w(u.get().getOr({})))},hasLoaded:i,waitForLoad:function(){return i()?T.resolve(!0):new T(function(n,t){var e=15,o=A.setInterval(function(){i()?(A.clearInterval(o),n(!0)):--e<0&&(m.console.log("Could not load emojis from url: "+r),A.clearInterval(o),t(!1))},100)})},listAll:e,listCategory:function(t){return t===_?e():u.get().bind(function(n){return g.from(n[t])}).getOr([])}}},S="pattern",N=function(r,u){var e,o,i,n={pattern:"",results:y(u.listAll(),"",g.some(300))},a=h(_),c=(e=function(n){var t,e,o,r,i;e=(t=n).getData(),o=a.get(),r=u.listCategory(o),i=y(r,e[S],o===_?g.some(300):g.none()),t.setData({results:i})},o=200,i=null,{cancel:function(){null!==i&&(m.clearTimeout(i),i=null)},throttle:function(){for(var n=[],t=0;t<arguments.length;t++)n[t]=arguments[t];null!==i&&m.clearTimeout(i),i=m.setTimeout(function(){e.apply(null,n),i=null},o)}}),t={label:"Search",type:"input",name:S},l={type:"collection",name:"results"},s=function(){return{title:"Emoticons",size:"normal",body:{type:"tabpanel",tabs:function(n,t){for(var e=n.length,o=new Array(e),r=0;r<e;r++){var i=n[r];o[r]=t(i,r)}return o}(u.listCategories(),function(n){return{title:n,name:n,items:[t,l]}})},initialData:n,onTabChange:function(n,t){a.set(t.newTabName),c.throttle(n)},onChange:c.throttle,onAction:function(n,t){var e,o;"results"===t.name&&(e=r,o=t.value,e.insertContent(o),n.close())},buttons:[{type:"cancel",text:"Close",primary:!0}]}},f=r.windowManager.open(s());f.focus(S),u.hasLoaded()||(f.block("Loading emoticons..."),u.waitForLoad().then(function(){f.redial(s()),c.throttle(f),f.focus(S),f.unblock()})["catch"](function(n){f.redial({title:"Emoticons",body:{type:"panel",items:[{type:"alertbanner",level:"error",icon:"warning",text:"<p>Could not load emoticons</p>"}]},buttons:[{type:"cancel",text:"Close",primary:!0}],initialData:{pattern:"",results:[]}}),f.focus(S),f.unblock()}))};!function E(){o.add("emoticons",function(n,t){var e,o,r,i,u,a,c,l=(o=t,(e=n).getParam("emoticons_database_url",o+"/js/emojis"+e.suffix+".js")),s=n.getParam("emoticons_database_id","tinymce.plugins.emoticons","string"),f=L(n,l,s);i=f,u=function(){return N(r,i)},(r=n).ui.registry.addButton("emoticons",{tooltip:"Emoticons",icon:"emoji",onAction:u}),r.ui.registry.addMenuItem("emoticons",{text:"Emoticons...",icon:"emoji",onAction:u}),c=f,(a=n).ui.registry.addAutocompleter("emoticons",{ch:":",columns:"auto",minChars:2,fetch:function(t,e){return c.waitForLoad().then(function(){var n=c.listAll();return y(n,t,g.some(e))})},onAction:function(n,t,e){a.selection.setRng(t),a.insertContent(e),n.hide()}})})}()}(window);