"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/orders/page",{

/***/ "(app-pages-browser)/./app/orders/page.tsx":
/*!*****************************!*\
  !*** ./app/orders/page.tsx ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ OrdersPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/breadcrumb */ \"(app-pages-browser)/./components/ui/breadcrumb.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./components/ui/skeleton.tsx\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _contexts_settings_context__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/contexts/settings-context */ \"(app-pages-browser)/./contexts/settings-context.tsx\");\n/* harmony import */ var _contexts_user_context__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/contexts/user-context */ \"(app-pages-browser)/./contexts/user-context.tsx\");\n/* harmony import */ var _lib_config__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/lib/config */ \"(app-pages-browser)/./lib/config.ts\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_ChevronRight_FileText_Package_RefreshCw_Search_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,ChevronRight,FileText,Package,RefreshCw,Search,Truck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_ChevronRight_FileText_Package_RefreshCw_Search_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,ChevronRight,FileText,Package,RefreshCw,Search,Truck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/truck.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_ChevronRight_FileText_Package_RefreshCw_Search_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,ChevronRight,FileText,Package,RefreshCw,Search,Truck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_ChevronRight_FileText_Package_RefreshCw_Search_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,ChevronRight,FileText,Package,RefreshCw,Search,Truck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_ChevronRight_FileText_Package_RefreshCw_Search_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,ChevronRight,FileText,Package,RefreshCw,Search,Truck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_ChevronRight_FileText_Package_RefreshCw_Search_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,ChevronRight,FileText,Package,RefreshCw,Search,Truck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_ChevronRight_FileText_Package_RefreshCw_Search_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,ChevronRight,FileText,Package,RefreshCw,Search,Truck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_ChevronRight_FileText_Package_RefreshCw_Search_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,ChevronRight,FileText,Package,RefreshCw,Search,Truck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _lib_encryption__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/lib/encryption */ \"(app-pages-browser)/./lib/encryption.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction OrdersPage() {\n    _s();\n    const { t, primaryColor, primaryTextColor } = (0,_contexts_settings_context__WEBPACK_IMPORTED_MODULE_8__.useSettings)();\n    const { user, isLoggedIn, isLoading, token } = (0,_contexts_user_context__WEBPACK_IMPORTED_MODULE_9__.useUser)();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    const [orders, setOrders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [selectedOrder, setSelectedOrder] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [orderDetails, setOrderDetails] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loadingDetails, setLoadingDetails] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [sortBy, setSortBy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('date-desc');\n    // Define all useCallback hooks before any conditional returns\n    const fetchOrderHistory = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"OrdersPage.useCallback[fetchOrderHistory]\": async ()=>{\n            setLoading(true);\n            try {\n                // Get JWT token from user context\n                const authHeaders = {\n                    'Content-Type': 'application/json',\n                    'Accept': 'application/json'\n                };\n                // Add JWT token to headers if available\n                if (token) {\n                    authHeaders['Authorization'] = \"Bearer \".concat(token);\n                }\n                const response = await fetch('/api/orders/history', {\n                    method: 'POST',\n                    headers: authHeaders,\n                    body: JSON.stringify({\n                        requestParameters: {\n                            // UserId removed - will be auto-injected from JWT token\n                            recordValueJson: \"[]\"\n                        }\n                    })\n                });\n                const data = await response.json();\n                console.log('Orders API response:', data);\n                if (data && data.data) {\n                    let ordersData;\n                    // Check if data is a string that needs parsing (like login API) or already parsed\n                    if (typeof data.data === 'string') {\n                        ordersData = JSON.parse(data.data);\n                    } else {\n                        ordersData = data.data;\n                    }\n                    console.log('Parsed order data:', ordersData);\n                    if (Array.isArray(ordersData)) {\n                        console.log('First order structure:', ordersData[0]);\n                        // Map the API response to our expected format\n                        const mappedOrders = ordersData.map({\n                            \"OrdersPage.useCallback[fetchOrderHistory].mappedOrders\": (order)=>({\n                                    id: order.OrderId,\n                                    OrderID: order.OrderId,\n                                    OrderNumber: order.OrderNumber,\n                                    date: order.OrderDateUTC ? new Date(order.OrderDateUTC).toLocaleDateString() : 'N/A',\n                                    OrderDate: order.OrderDateUTC,\n                                    total: order.OrderTotal || 0,\n                                    OrderTotal: order.OrderTotal || 0,\n                                    TotalAmount: order.OrderTotal || 0,\n                                    status: order.LatestStatusName || 'Active',\n                                    StatusID: order.LatestStatusName === 'Active' ? 1 : order.LatestStatusName === 'In Progress' ? 2 : order.LatestStatusName === 'Completed' ? 3 : order.LatestStatusName === 'Returned' ? 4 : order.LatestStatusName === 'Refunded' ? 5 : order.LatestStatusName === 'Cancelled' ? 6 : 1,\n                                    ItemCount: order.TotalItems || 0,\n                                    items: order.OrderItems || [],\n                                    OrderItems: order.OrderItems || []\n                                })\n                        }[\"OrdersPage.useCallback[fetchOrderHistory].mappedOrders\"]);\n                        // Sort orders from newest to oldest\n                        const sortedOrders = mappedOrders.sort({\n                            \"OrdersPage.useCallback[fetchOrderHistory].sortedOrders\": (a, b)=>{\n                                return new Date(b.OrderDate || 0).getTime() - new Date(a.OrderDate || 0).getTime();\n                            }\n                        }[\"OrdersPage.useCallback[fetchOrderHistory].sortedOrders\"]);\n                        setOrders(sortedOrders);\n                        console.log('Mapped and sorted orders:', sortedOrders);\n                    } else {\n                        setOrders([]);\n                    }\n                }\n            } catch (error) {\n                console.error('Error fetching order history:', error);\n            } finally{\n                setLoading(false);\n            }\n        }\n    }[\"OrdersPage.useCallback[fetchOrderHistory]\"], [\n        token\n    ]);\n    const fetchOrderDetails = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"OrdersPage.useCallback[fetchOrderDetails]\": async (orderId)=>{\n            setLoadingDetails(true);\n            try {\n                // Get JWT token from user context\n                const authHeaders = {\n                    'Content-Type': 'application/json',\n                    'Accept': 'application/json'\n                };\n                // Add JWT token to headers if available\n                if (token) {\n                    authHeaders['Authorization'] = \"Bearer \".concat(token);\n                }\n                const response = await fetch('/api/orders/details', {\n                    method: 'POST',\n                    headers: authHeaders,\n                    body: JSON.stringify({\n                        requestParameters: {\n                            OrderId: orderId,\n                            // UserId removed - will be auto-injected from JWT token\n                            recordValueJson: \"[]\"\n                        }\n                    })\n                });\n                const data = await response.json();\n                if (data && data.data) {\n                    const parsedData = JSON.parse(data.data);\n                    if (Array.isArray(parsedData)) {\n                        setOrderDetails(parsedData);\n                    }\n                }\n            } catch (error) {\n                console.error('Error fetching order details:', error);\n            } finally{\n                setLoadingDetails(false);\n            }\n        }\n    }[\"OrdersPage.useCallback[fetchOrderDetails]\"], [\n        token\n    ]);\n    // Fetch order history\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OrdersPage.useEffect\": ()=>{\n            const userId = (user === null || user === void 0 ? void 0 : user.UserID) || (user === null || user === void 0 ? void 0 : user.UserId);\n            if (isLoggedIn && userId) {\n                fetchOrderHistory();\n            }\n        }\n    }[\"OrdersPage.useEffect\"], [\n        isLoggedIn,\n        user,\n        fetchOrderHistory\n    ]);\n    // Redirect to login if not authenticated (only after loading is complete)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OrdersPage.useEffect\": ()=>{\n            if (!isLoading && !isLoggedIn) {\n                window.location.href = '/login?redirect=/orders';\n            }\n        }\n    }[\"OrdersPage.useEffect\"], [\n        isLoading,\n        isLoggedIn\n    ]);\n    // Early return for loading state to prevent race conditions\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                        lineNumber: 173,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-lg text-muted-foreground\",\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                        lineNumber: 174,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                lineNumber: 172,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n            lineNumber: 171,\n            columnNumber: 7\n        }, this);\n    }\n    // Early return for unauthenticated users\n    if (!isLoggedIn) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-lg text-muted-foreground\",\n                    children: \"Redirecting to login...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                    lineNumber: 185,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                lineNumber: 184,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n            lineNumber: 183,\n            columnNumber: 7\n        }, this);\n    }\n    // Mock orders data (fallback)\n    const mockOrders = [\n        {\n            id: 'ORD-2023-1001',\n            date: '2023-12-15',\n            total: 129.99,\n            StatusID: 3,\n            items: [\n                {\n                    name: 'Medical Terminology Book',\n                    ProductName: 'Medical Terminology Book',\n                    quantity: 1,\n                    price: 49.99\n                },\n                {\n                    name: 'Anatomy Atlas',\n                    ProductName: 'Anatomy Atlas',\n                    quantity: 1,\n                    price: 79.99\n                }\n            ]\n        },\n        {\n            id: 'ORD-2023-0892',\n            date: '2023-11-28',\n            total: 199.99,\n            StatusID: 2,\n            items: [\n                {\n                    name: 'Clinical Medicine Course',\n                    ProductName: 'Clinical Medicine Course',\n                    quantity: 1,\n                    price: 199.99\n                }\n            ]\n        },\n        {\n            id: 'ORD-2023-0765',\n            date: '2023-10-05',\n            total: 45.99,\n            StatusID: 6,\n            items: [\n                {\n                    name: 'Pharmacology Flashcards',\n                    ProductName: 'Pharmacology Flashcards',\n                    quantity: 1,\n                    price: 45.99\n                }\n            ]\n        }\n    ];\n    // Filter and sort orders\n    const displayOrders = loading ? [] : orders;\n    // Filter by tab\n    const tabFilteredOrders = activeTab === 'all' ? displayOrders : displayOrders.filter((order)=>{\n        const statusId = order.StatusID || order.OrderStatusId || order.StateId || 1;\n        return statusId.toString() === activeTab;\n    });\n    // Filter by search query\n    const searchFilteredOrders = searchQuery.trim() === '' ? tabFilteredOrders : tabFilteredOrders.filter((order)=>{\n        const searchTerm = searchQuery.toLowerCase();\n        const orderNumber = (order.OrderNumber || \"OR#\".concat(String(order.OrderId || order.OrderID).padStart(8, '0'))).toLowerCase();\n        const productNames = (order.items || order.OrderItems || []).map((item)=>(item.name || item.ProductName || item.ItemName || '').toLowerCase()).join(' ');\n        return orderNumber.includes(searchTerm) || productNames.includes(searchTerm);\n    });\n    // Sort orders\n    const filteredOrders = [\n        ...searchFilteredOrders\n    ].sort((a, b)=>{\n        switch(sortBy){\n            case 'date-desc':\n                return new Date(b.OrderDate || 0).getTime() - new Date(a.OrderDate || 0).getTime();\n            case 'date-asc':\n                return new Date(a.OrderDate || 0).getTime() - new Date(b.OrderDate || 0).getTime();\n            case 'total-desc':\n                return (b.total || b.OrderTotal || b.TotalAmount || 0) - (a.total || a.OrderTotal || a.TotalAmount || 0);\n            case 'total-asc':\n                return (a.total || a.OrderTotal || a.TotalAmount || 0) - (b.total || b.OrderTotal || b.TotalAmount || 0);\n            default:\n                return 0;\n        }\n    });\n    // Create secure query parameters for order details\n    const createSecureOrderLink = (order)=>{\n        const orderId = order.OrderId || order.OrderID || order.id;\n        const encryptedOrderId = (0,_lib_encryption__WEBPACK_IMPORTED_MODULE_11__.encryptOrderId)(orderId);\n        // Encrypt sensitive data for query parameters\n        const orderTotal = (order.total || order.OrderTotal || order.TotalAmount || 0).toFixed(2);\n        const orderDate = (()=>{\n            const dateStr = order.OrderDateUTC || order.date || order.OrderDate || order.CreatedOn;\n            if (dateStr) {\n                try {\n                    const date = new Date(dateStr);\n                    return date.toLocaleDateString('en-US', {\n                        year: 'numeric',\n                        month: 'long',\n                        day: 'numeric'\n                    });\n                } catch (e) {\n                    return 'N/A';\n                }\n            }\n            return 'N/A';\n        })();\n        // Encrypt the query parameters\n        const encryptedTotal = (0,_lib_encryption__WEBPACK_IMPORTED_MODULE_11__.encryptValue)(orderTotal);\n        const encryptedDate = (0,_lib_encryption__WEBPACK_IMPORTED_MODULE_11__.encryptValue)(orderDate);\n        return \"/orders/\".concat(encryptedOrderId, \"?t=\").concat(encryptedTotal, \"&d=\").concat(encryptedDate);\n    };\n    // Status mapping based on StatusID\n    const getStatusInfo = (statusId)=>{\n        const id = parseInt((statusId === null || statusId === void 0 ? void 0 : statusId.toString()) || '1');\n        switch(id){\n            case 1:\n                return {\n                    name: 'Active',\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_ChevronRight_FileText_Package_RefreshCw_Search_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        className: \"h-5 w-5 text-blue-500\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                        lineNumber: 303,\n                        columnNumber: 40\n                    }, this),\n                    color: 'bg-blue-100 text-blue-800'\n                };\n            case 2:\n                return {\n                    name: 'In Progress',\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_ChevronRight_FileText_Package_RefreshCw_Search_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                        className: \"h-5 w-5 text-orange-500\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                        lineNumber: 305,\n                        columnNumber: 45\n                    }, this),\n                    color: 'bg-orange-100 text-orange-800'\n                };\n            case 3:\n                return {\n                    name: 'Completed',\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_ChevronRight_FileText_Package_RefreshCw_Search_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                        className: \"h-5 w-5 text-green-500\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                        lineNumber: 307,\n                        columnNumber: 43\n                    }, this),\n                    color: 'bg-green-100 text-green-800'\n                };\n            case 4:\n                return {\n                    name: 'Returned',\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_ChevronRight_FileText_Package_RefreshCw_Search_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                        className: \"h-5 w-5 text-purple-500\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                        lineNumber: 309,\n                        columnNumber: 42\n                    }, this),\n                    color: 'bg-purple-100 text-purple-800'\n                };\n            case 5:\n                return {\n                    name: 'Refunded',\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_ChevronRight_FileText_Package_RefreshCw_Search_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                        className: \"h-5 w-5 text-indigo-500\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                        lineNumber: 311,\n                        columnNumber: 42\n                    }, this),\n                    color: 'bg-indigo-100 text-indigo-800'\n                };\n            case 6:\n                return {\n                    name: 'Cancelled',\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_ChevronRight_FileText_Package_RefreshCw_Search_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                        className: \"h-5 w-5 text-red-500\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                        lineNumber: 313,\n                        columnNumber: 43\n                    }, this),\n                    color: 'bg-red-100 text-red-800'\n                };\n            default:\n                return {\n                    name: 'Unknown',\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_ChevronRight_FileText_Package_RefreshCw_Search_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        className: \"h-5 w-5 text-gray-500\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                        lineNumber: 315,\n                        columnNumber: 41\n                    }, this),\n                    color: 'bg-gray-100 text-gray-800'\n                };\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto py-8 px-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_2__.Breadcrumb, {\n                className: \"mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_2__.BreadcrumbList, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_2__.BreadcrumbItem, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_2__.BreadcrumbLink, {\n                                asChild: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_7___default()), {\n                                    href: \"/\",\n                                    children: t('home')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 328,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                                lineNumber: 327,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                            lineNumber: 326,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_2__.BreadcrumbSeparator, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                            lineNumber: 331,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_2__.BreadcrumbItem, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_2__.BreadcrumbPage, {\n                                children: t('orders')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                                lineNumber: 333,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                            lineNumber: 332,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                    lineNumber: 325,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                lineNumber: 324,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-6xl mx-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold\",\n                                children: t('orders')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                                lineNumber: 341,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"outline\",\n                                size: \"sm\",\n                                onClick: fetchOrderHistory,\n                                disabled: loading,\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_ChevronRight_FileText_Package_RefreshCw_Search_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-4 w-4 \".concat(loading ? 'animate-spin' : '')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 349,\n                                        columnNumber: 13\n                                    }, this),\n                                    loading ? 'Loading...' : 'Refresh'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                                lineNumber: 342,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                        lineNumber: 340,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.Tabs, {\n                        defaultValue: \"all\",\n                        className: \"mb-6\",\n                        onValueChange: setActiveTab,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsList, {\n                            className: \"grid w-full grid-cols-2 sm:grid-cols-3 lg:grid-cols-5 mb-6 gap-2 bg-transparent p-0 h-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsTrigger, {\n                                    value: \"all\",\n                                    className: \"rounded-lg px-4 py-3 text-base font-semibold transition-all duration-300 border-2 border-transparent data-[state=inactive]:bg-gray-300 data-[state=inactive]:text-gray-700 data-[state=inactive]:scale-100 hover:bg-gray-400 hover:text-white hover:scale-102\",\n                                    style: {\n                                        backgroundColor: activeTab === 'all' ? primaryColor : 'rgb(209 213 219)',\n                                        color: activeTab === 'all' ? primaryTextColor : 'rgb(55 65 81)',\n                                        transform: activeTab === 'all' ? 'scale(1.05)' : 'scale(1)',\n                                        boxShadow: activeTab === 'all' ? '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)' : 'none',\n                                        borderColor: activeTab === 'all' ? primaryColor : 'transparent'\n                                    },\n                                    children: \"All Orders\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 357,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsTrigger, {\n                                    value: \"1\",\n                                    className: \"rounded-lg px-4 py-3 text-base font-semibold transition-all duration-300 border-2 border-transparent data-[state=inactive]:bg-gray-300 data-[state=inactive]:text-gray-700 data-[state=inactive]:scale-100 hover:bg-gray-400 hover:text-white hover:scale-102\",\n                                    style: {\n                                        backgroundColor: activeTab === '1' ? primaryColor : 'rgb(209 213 219)',\n                                        color: activeTab === '1' ? primaryTextColor : 'rgb(55 65 81)',\n                                        transform: activeTab === '1' ? 'scale(1.05)' : 'scale(1)',\n                                        boxShadow: activeTab === '1' ? '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)' : 'none',\n                                        borderColor: activeTab === '1' ? primaryColor : 'transparent'\n                                    },\n                                    children: \"Active\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 370,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsTrigger, {\n                                    value: \"2\",\n                                    className: \"rounded-lg px-4 py-3 text-base font-semibold transition-all duration-300 border-2 border-transparent data-[state=inactive]:bg-gray-300 data-[state=inactive]:text-gray-700 data-[state=inactive]:scale-100 hover:bg-gray-400 hover:text-white hover:scale-102\",\n                                    style: {\n                                        backgroundColor: activeTab === '2' ? primaryColor : 'rgb(209 213 219)',\n                                        color: activeTab === '2' ? primaryTextColor : 'rgb(55 65 81)',\n                                        transform: activeTab === '2' ? 'scale(1.05)' : 'scale(1)',\n                                        boxShadow: activeTab === '2' ? '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)' : 'none',\n                                        borderColor: activeTab === '2' ? primaryColor : 'transparent'\n                                    },\n                                    children: \"In Progress\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 383,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsTrigger, {\n                                    value: \"3\",\n                                    className: \"rounded-lg px-4 py-3 text-base font-semibold transition-all duration-300 border-2 border-transparent data-[state=inactive]:bg-gray-300 data-[state=inactive]:text-gray-700 data-[state=inactive]:scale-100 hover:bg-gray-400 hover:text-white hover:scale-102\",\n                                    style: {\n                                        backgroundColor: activeTab === '3' ? primaryColor : 'rgb(209 213 219)',\n                                        color: activeTab === '3' ? primaryTextColor : 'rgb(55 65 81)',\n                                        transform: activeTab === '3' ? 'scale(1.05)' : 'scale(1)',\n                                        boxShadow: activeTab === '3' ? '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)' : 'none',\n                                        borderColor: activeTab === '3' ? primaryColor : 'transparent'\n                                    },\n                                    children: \"Completed\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 396,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsTrigger, {\n                                    value: \"6\",\n                                    className: \"rounded-lg px-4 py-3 text-base font-semibold transition-all duration-300 border-2 border-transparent data-[state=inactive]:bg-gray-300 data-[state=inactive]:text-gray-700 data-[state=inactive]:scale-100 hover:bg-gray-400 hover:text-white hover:scale-102\",\n                                    style: {\n                                        backgroundColor: activeTab === '6' ? primaryColor : 'rgb(209 213 219)',\n                                        color: activeTab === '6' ? primaryTextColor : 'rgb(55 65 81)',\n                                        transform: activeTab === '6' ? 'scale(1.05)' : 'scale(1)',\n                                        boxShadow: activeTab === '6' ? '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)' : 'none',\n                                        borderColor: activeTab === '6' ? primaryColor : 'transparent'\n                                    },\n                                    children: \"Cancelled\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 409,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                            lineNumber: 356,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                        lineNumber: 355,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative w-full md:w-64\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_ChevronRight_FileText_Package_RefreshCw_Search_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 428,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        placeholder: \"Search orders...\",\n                                        value: searchQuery,\n                                        onChange: (e)=>setSearchQuery(e.target.value),\n                                        className: \"w-full pl-10 pr-4 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 429,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                                lineNumber: 427,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-muted-foreground\",\n                                        children: \"Sort by:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 439,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: sortBy,\n                                        onChange: (e)=>setSortBy(e.target.value),\n                                        className: \"border border-input rounded-md px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"date-desc\",\n                                                children: \"Date (Newest)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 445,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"date-asc\",\n                                                children: \"Date (Oldest)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 446,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"total-desc\",\n                                                children: \"Amount (High to Low)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 447,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"total-asc\",\n                                                children: \"Amount (Low to High)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 448,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 440,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                                lineNumber: 438,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                        lineNumber: 426,\n                        columnNumber: 9\n                    }, this),\n                    loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            ...Array(3)\n                        ].map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                className: \"overflow-hidden\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border-b border-border p-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col md:flex-row justify-between items-start md:items-center gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2 mb-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                                                className: \"h-5 w-32\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 463,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                                                className: \"h-6 w-20 rounded-full\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 464,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 462,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-4 text-sm text-muted-foreground mb-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                                                className: \"h-4 w-24\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 467,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                                                className: \"h-4 w-16\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 468,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                                                className: \"h-4 w-20\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 469,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 466,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                                        className: \"h-16 w-full rounded-md\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 471,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 461,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col items-end gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                                        className: \"h-6 w-20\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 474,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                                        className: \"h-8 w-24\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 475,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 473,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 460,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 459,\n                                    columnNumber: 17\n                                }, this)\n                            }, index, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                                lineNumber: 458,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                        lineNumber: 455,\n                        columnNumber: 11\n                    }, this) : filteredOrders.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: filteredOrders.map((order, orderIndex)=>{\n                            var _order_items, _order_OrderItems, _order_items1, _order_OrderItems1, _order_items2, _order_OrderItems2;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                className: \"overflow-hidden\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"border-b border-border p-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col md:flex-row justify-between items-start md:items-center gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2 mb-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"font-medium\",\n                                                                    children: order.OrderNumber || \"OR#\".concat(String(order.OrderId || order.OrderID).padStart(8, '0'))\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                                                                    lineNumber: 490,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                (()=>{\n                                                                    // Use LatestStatusName from API if available, otherwise fall back to StatusID mapping\n                                                                    const statusName = order.LatestStatusName;\n                                                                    if (statusName) {\n                                                                        // Map API status names to our display format\n                                                                        let displayStatus = statusName;\n                                                                        let statusColor = 'bg-blue-100 text-blue-800';\n                                                                        let statusIcon = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_ChevronRight_FileText_Package_RefreshCw_Search_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                            className: \"h-5 w-5 text-blue-500\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                                                                            lineNumber: 500,\n                                                                            columnNumber: 46\n                                                                        }, this);\n                                                                        switch(statusName.toLowerCase()){\n                                                                            case 'active':\n                                                                                statusColor = 'bg-blue-100 text-blue-800';\n                                                                                statusIcon = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_ChevronRight_FileText_Package_RefreshCw_Search_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                                    className: \"h-5 w-5 text-blue-500\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                                                                                    lineNumber: 505,\n                                                                                    columnNumber: 46\n                                                                                }, this);\n                                                                                break;\n                                                                            case 'in progress':\n                                                                            case 'processing':\n                                                                                statusColor = 'bg-orange-100 text-orange-800';\n                                                                                statusIcon = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_ChevronRight_FileText_Package_RefreshCw_Search_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                                    className: \"h-5 w-5 text-orange-500\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                                                                                    lineNumber: 510,\n                                                                                    columnNumber: 46\n                                                                                }, this);\n                                                                                break;\n                                                                            case 'completed':\n                                                                            case 'delivered':\n                                                                                statusColor = 'bg-green-100 text-green-800';\n                                                                                statusIcon = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_ChevronRight_FileText_Package_RefreshCw_Search_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                    className: \"h-5 w-5 text-green-500\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                                                                                    lineNumber: 515,\n                                                                                    columnNumber: 46\n                                                                                }, this);\n                                                                                break;\n                                                                            case 'cancelled':\n                                                                                statusColor = 'bg-red-100 text-red-800';\n                                                                                statusIcon = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_ChevronRight_FileText_Package_RefreshCw_Search_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                    className: \"h-5 w-5 text-red-500\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                                                                                    lineNumber: 519,\n                                                                                    columnNumber: 46\n                                                                                }, this);\n                                                                                break;\n                                                                            default:\n                                                                                statusColor = 'bg-gray-100 text-gray-800';\n                                                                                statusIcon = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_ChevronRight_FileText_Package_RefreshCw_Search_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                                    className: \"h-5 w-5 text-gray-500\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                                                                                    lineNumber: 523,\n                                                                                    columnNumber: 46\n                                                                                }, this);\n                                                                        }\n                                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center gap-1 px-2 py-1 rounded-full text-xs \".concat(statusColor),\n                                                                            children: [\n                                                                                statusIcon,\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: displayStatus\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                                                                                    lineNumber: 529,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                                                                            lineNumber: 527,\n                                                                            columnNumber: 31\n                                                                        }, this);\n                                                                    } else {\n                                                                        // Fallback to StatusID mapping\n                                                                        const statusId = order.StatusID || order.OrderStatusId || order.StateId || 1;\n                                                                        const statusInfo = getStatusInfo(statusId);\n                                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center gap-1 px-2 py-1 rounded-full text-xs \".concat(statusInfo.color),\n                                                                            children: [\n                                                                                statusInfo.icon,\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: statusInfo.name\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                                                                                    lineNumber: 539,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                                                                            lineNumber: 537,\n                                                                            columnNumber: 31\n                                                                        }, this);\n                                                                    }\n                                                                })()\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 489,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-muted-foreground mb-2\",\n                                                            children: [\n                                                                \"Ordered on \",\n                                                                (()=>{\n                                                                    const dateStr = order.OrderDateUTC || order.date || order.OrderDate || order.CreatedOn;\n                                                                    if (dateStr) {\n                                                                        try {\n                                                                            const date = new Date(dateStr);\n                                                                            return date.toLocaleDateString('en-US', {\n                                                                                year: 'numeric',\n                                                                                month: 'long',\n                                                                                day: 'numeric',\n                                                                                hour: '2-digit',\n                                                                                minute: '2-digit'\n                                                                            });\n                                                                        } catch (e) {\n                                                                            return dateStr;\n                                                                        }\n                                                                    }\n                                                                    return 'N/A';\n                                                                })()\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 545,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        (order.items || order.OrderItems || []).length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-blue-50 px-3 py-2 rounded-md\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm font-medium text-blue-900 mb-1\",\n                                                                    children: \"Products in this order:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                                                                    lineNumber: 568,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm text-blue-800 space-y-1\",\n                                                                    children: [\n                                                                        (order.items || order.OrderItems || []).slice(0, 3).map((item, index)=>{\n                                                                            // Get product name from item data\n                                                                            const productName = item.name || item.ProductName || item.ItemName || 'Medical Product';\n                                                                            const productId = item.ProductID || item.ProductId;\n                                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: [\n                                                                                    productId ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_7___default()), {\n                                                                                        href: \"/product/\".concat(productId),\n                                                                                        className: \"text-blue-600 hover:text-blue-800 hover:underline font-medium\",\n                                                                                        target: \"_blank\",\n                                                                                        rel: \"noopener noreferrer\",\n                                                                                        children: productName\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                                                                                        lineNumber: 583,\n                                                                                        columnNumber: 39\n                                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"font-medium\",\n                                                                                        children: productName\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                                                                                        lineNumber: 592,\n                                                                                        columnNumber: 39\n                                                                                    }, this),\n                                                                                    index < Math.min(2, (order.items || order.OrderItems || []).length - 1) && ' • '\n                                                                                ]\n                                                                            }, index, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                                                                                lineNumber: 581,\n                                                                                columnNumber: 35\n                                                                            }, this);\n                                                                        }),\n                                                                        (order.items || order.OrderItems || []).length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-blue-600\",\n                                                                            children: [\n                                                                                \" • +\",\n                                                                                (order.items || order.OrderItems || []).length - 3,\n                                                                                \" more items\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                                                                            lineNumber: 599,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                                                                    lineNumber: 569,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 567,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 488,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-4 mt-4 md:mt-0\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-right\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium\",\n                                                                    children: [\n                                                                        \"$\",\n                                                                        (order.total || order.OrderTotal || order.TotalAmount || 0).toFixed(2)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                                                                    lineNumber: 608,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: [\n                                                                        order.TotalItems || ((_order_items = order.items) === null || _order_items === void 0 ? void 0 : _order_items.length) || ((_order_OrderItems = order.OrderItems) === null || _order_OrderItems === void 0 ? void 0 : _order_OrderItems.length) || order.ItemCount || 0,\n                                                                        \" item(s)\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                                                                    lineNumber: 611,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 607,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            variant: \"outline\",\n                                                            size: \"sm\",\n                                                            className: \"flex items-center gap-1\",\n                                                            asChild: true,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_7___default()), {\n                                                                href: createSecureOrderLink(order),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_ChevronRight_FileText_Package_RefreshCw_Search_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                                                                        lineNumber: 623,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    \"Details\",\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_ChevronRight_FileText_Package_RefreshCw_Search_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                                                                        lineNumber: 625,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 622,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 616,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 606,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 487,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 486,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-4 bg-muted/30\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-sm font-medium mb-2\",\n                                                children: \"Order Items Preview\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 634,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    (order.items || order.OrderItems || []).slice(0, 3).map((item, index)=>{\n                                                        const productImage = item.ProductImageUrl || item.ImageUrl || item.ProductImage;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-12 h-12 bg-muted rounded-md overflow-hidden flex items-center justify-center\",\n                                                                            children: productImage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                                src: productImage.startsWith('http') ? productImage : \"\".concat(_lib_config__WEBPACK_IMPORTED_MODULE_10__.Config.ADMIN_BASE_URL).concat(productImage),\n                                                                                alt: item.name || item.ProductName || 'Product',\n                                                                                className: \"w-full h-full object-cover\",\n                                                                                onError: (e)=>{\n                                                                                    const target = e.target;\n                                                                                    target.style.display = 'none';\n                                                                                    const parent = target.parentElement;\n                                                                                    if (parent) {\n                                                                                        parent.innerHTML = '<svg class=\"w-6 h-6 text-gray-400\" fill=\"currentColor\" viewBox=\"0 0 20 20\"><path fill-rule=\"evenodd\" d=\"M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z\" clip-rule=\"evenodd\"></path></svg>';\n                                                                                    }\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                                                                                lineNumber: 644,\n                                                                                columnNumber: 33\n                                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_ChevronRight_FileText_Package_RefreshCw_Search_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                                className: \"h-6 w-6 text-gray-400\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                                                                                lineNumber: 658,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                                                                            lineNumber: 642,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-sm font-medium text-gray-900\",\n                                                                                    children: item.name || item.ProductName || item.ItemName || 'Medical Product'\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                                                                                    lineNumber: 662,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-xs text-muted-foreground\",\n                                                                                    children: [\n                                                                                        \"Qty: \",\n                                                                                        item.quantity || item.Quantity || item.OrderQuantity || 1\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                                                                                    lineNumber: 665,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                item.ProductDescription && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-xs text-gray-500 mt-1 line-clamp-1\",\n                                                                                    children: item.ProductDescription\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                                                                                    lineNumber: 669,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                                                                            lineNumber: 661,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                                                                    lineNumber: 641,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm font-medium\",\n                                                                    children: [\n                                                                        \"$\",\n                                                                        (item.price || item.Price || item.UnitPrice || item.ItemPrice || 0).toFixed(2)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                                                                    lineNumber: 675,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 640,\n                                                            columnNumber: 25\n                                                        }, this);\n                                                    }),\n                                                    (((_order_items1 = order.items) === null || _order_items1 === void 0 ? void 0 : _order_items1.length) || ((_order_OrderItems1 = order.OrderItems) === null || _order_OrderItems1 === void 0 ? void 0 : _order_OrderItems1.length) || 0) > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-muted-foreground text-center pt-2\",\n                                                        children: [\n                                                            \"+\",\n                                                            (((_order_items2 = order.items) === null || _order_items2 === void 0 ? void 0 : _order_items2.length) || ((_order_OrderItems2 = order.OrderItems) === null || _order_OrderItems2 === void 0 ? void 0 : _order_OrderItems2.length) || 0) - 3,\n                                                            \" more items\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 682,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 635,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 633,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, order.OrderId || order.OrderID || order.id || orderIndex, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                                lineNumber: 485,\n                                columnNumber: 15\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                        lineNumber: 483,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-8 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-16 h-16 bg-muted rounded-full flex items-center justify-center mx-auto mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_ChevronRight_FileText_Package_RefreshCw_Search_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"h-8 w-8 text-muted-foreground\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 695,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 694,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-medium mb-2\",\n                                    children: \"No orders found\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 697,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-muted-foreground mb-4\",\n                                    children: [\n                                        \"You don't have any \",\n                                        activeTab !== 'all' ? activeTab : '',\n                                        \" orders yet.\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 698,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    asChild: true,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_7___default()), {\n                                        href: \"/\",\n                                        children: \"Continue Shopping\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 700,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 699,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                            lineNumber: 693,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                        lineNumber: 692,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n                lineNumber: 339,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\",\n        lineNumber: 322,\n        columnNumber: 5\n    }, this);\n}\n_s(OrdersPage, \"9ci2MmUfI8t+AzfPyv0ZILINOtA=\", false, function() {\n    return [\n        _contexts_settings_context__WEBPACK_IMPORTED_MODULE_8__.useSettings,\n        _contexts_user_context__WEBPACK_IMPORTED_MODULE_9__.useUser\n    ];\n});\n_c = OrdersPage;\nvar _c;\n$RefreshReg$(_c, \"OrdersPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9vcmRlcnMvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUV5RDtBQUNvRjtBQUNqRztBQUNJO0FBQ2dDO0FBQzVCO0FBQ3ZCO0FBQzZCO0FBQ1I7QUFFWjtBQUM2RTtBQUNuRDtBQUVqRCxTQUFTNkI7O0lBQ3RCLE1BQU0sRUFBRUMsQ0FBQyxFQUFFQyxZQUFZLEVBQUVDLGdCQUFnQixFQUFFLEdBQUdoQix1RUFBV0E7SUFDekQsTUFBTSxFQUFFaUIsSUFBSSxFQUFFQyxVQUFVLEVBQUVDLFNBQVMsRUFBRUMsS0FBSyxFQUFFLEdBQUduQiwrREFBT0E7SUFDdEQsTUFBTSxDQUFDb0IsV0FBV0MsYUFBYSxHQUFHdEMsK0NBQVFBLENBQUM7SUFDM0MsTUFBTSxDQUFDdUMsUUFBUUMsVUFBVSxHQUFHeEMsK0NBQVFBLENBQVEsRUFBRTtJQUM5QyxNQUFNLENBQUN5QyxTQUFTQyxXQUFXLEdBQUcxQywrQ0FBUUEsQ0FBQztJQUN2QyxNQUFNLENBQUMyQyxlQUFlQyxpQkFBaUIsR0FBRzVDLCtDQUFRQSxDQUFNO0lBQ3hELE1BQU0sQ0FBQzZDLGNBQWNDLGdCQUFnQixHQUFHOUMsK0NBQVFBLENBQVEsRUFBRTtJQUMxRCxNQUFNLENBQUMrQyxnQkFBZ0JDLGtCQUFrQixHQUFHaEQsK0NBQVFBLENBQUM7SUFDckQsTUFBTSxDQUFDaUQsYUFBYUMsZUFBZSxHQUFHbEQsK0NBQVFBLENBQUM7SUFDL0MsTUFBTSxDQUFDbUQsUUFBUUMsVUFBVSxHQUFHcEQsK0NBQVFBLENBQUM7SUFFckMsOERBQThEO0lBQzlELE1BQU1xRCxvQkFBb0JuRCxrREFBV0E7cURBQUM7WUFDcEN3QyxXQUFXO1lBQ1gsSUFBSTtnQkFDRixrQ0FBa0M7Z0JBQ2xDLE1BQU1ZLGNBQXNDO29CQUMxQyxnQkFBZ0I7b0JBQ2hCLFVBQVU7Z0JBQ1o7Z0JBRUEsd0NBQXdDO2dCQUN4QyxJQUFJbEIsT0FBTztvQkFDVGtCLFdBQVcsQ0FBQyxnQkFBZ0IsR0FBRyxVQUFnQixPQUFObEI7Z0JBQzNDO2dCQUVBLE1BQU1tQixXQUFXLE1BQU1DLE1BQU0sdUJBQXVCO29CQUNsREMsUUFBUTtvQkFDUkMsU0FBU0o7b0JBQ1RLLE1BQU1DLEtBQUtDLFNBQVMsQ0FBQzt3QkFDbkJDLG1CQUFtQjs0QkFDakIsd0RBQXdEOzRCQUN4REMsaUJBQWlCO3dCQUNuQjtvQkFDRjtnQkFDRjtnQkFFQSxNQUFNQyxPQUFPLE1BQU1ULFNBQVNVLElBQUk7Z0JBQ2hDQyxRQUFRQyxHQUFHLENBQUMsd0JBQXdCSDtnQkFFcEMsSUFBSUEsUUFBUUEsS0FBS0EsSUFBSSxFQUFFO29CQUNyQixJQUFJSTtvQkFFSixrRkFBa0Y7b0JBQ2xGLElBQUksT0FBT0osS0FBS0EsSUFBSSxLQUFLLFVBQVU7d0JBQ2pDSSxhQUFhUixLQUFLUyxLQUFLLENBQUNMLEtBQUtBLElBQUk7b0JBQ25DLE9BQU87d0JBQ0xJLGFBQWFKLEtBQUtBLElBQUk7b0JBQ3hCO29CQUVBRSxRQUFRQyxHQUFHLENBQUMsc0JBQXNCQztvQkFFbEMsSUFBSUUsTUFBTUMsT0FBTyxDQUFDSCxhQUFhO3dCQUM3QkYsUUFBUUMsR0FBRyxDQUFDLDBCQUEwQkMsVUFBVSxDQUFDLEVBQUU7d0JBRW5ELDhDQUE4Qzt3QkFDOUMsTUFBTUksZUFBZUosV0FBV0ssR0FBRztzRkFBQ0MsQ0FBQUEsUUFBVTtvQ0FDNUNDLElBQUlELE1BQU1FLE9BQU87b0NBQ2pCQyxTQUFTSCxNQUFNRSxPQUFPO29DQUN0QkUsYUFBYUosTUFBTUksV0FBVztvQ0FDOUJDLE1BQU1MLE1BQU1NLFlBQVksR0FBRyxJQUFJQyxLQUFLUCxNQUFNTSxZQUFZLEVBQUVFLGtCQUFrQixLQUFLO29DQUMvRUMsV0FBV1QsTUFBTU0sWUFBWTtvQ0FDN0JJLE9BQU9WLE1BQU1XLFVBQVUsSUFBSTtvQ0FDM0JBLFlBQVlYLE1BQU1XLFVBQVUsSUFBSTtvQ0FDaENDLGFBQWFaLE1BQU1XLFVBQVUsSUFBSTtvQ0FDakNFLFFBQVFiLE1BQU1jLGdCQUFnQixJQUFJO29DQUNsQ0MsVUFBVWYsTUFBTWMsZ0JBQWdCLEtBQUssV0FBVyxJQUN2Q2QsTUFBTWMsZ0JBQWdCLEtBQUssZ0JBQWdCLElBQzNDZCxNQUFNYyxnQkFBZ0IsS0FBSyxjQUFjLElBQ3pDZCxNQUFNYyxnQkFBZ0IsS0FBSyxhQUFhLElBQ3hDZCxNQUFNYyxnQkFBZ0IsS0FBSyxhQUFhLElBQ3hDZCxNQUFNYyxnQkFBZ0IsS0FBSyxjQUFjLElBQUk7b0NBQ3RERSxXQUFXaEIsTUFBTWlCLFVBQVUsSUFBSTtvQ0FDL0JDLE9BQU9sQixNQUFNbUIsVUFBVSxJQUFJLEVBQUU7b0NBQzdCQSxZQUFZbkIsTUFBTW1CLFVBQVUsSUFBSSxFQUFFO2dDQUNwQzs7d0JBRUEsb0NBQW9DO3dCQUNwQyxNQUFNQyxlQUFldEIsYUFBYXVCLElBQUk7c0ZBQUMsQ0FBQ0MsR0FBR0M7Z0NBQ3pDLE9BQU8sSUFBSWhCLEtBQUtnQixFQUFFZCxTQUFTLElBQUksR0FBR2UsT0FBTyxLQUFLLElBQUlqQixLQUFLZSxFQUFFYixTQUFTLElBQUksR0FBR2UsT0FBTzs0QkFDbEY7O3dCQUVBMUQsVUFBVXNEO3dCQUNWNUIsUUFBUUMsR0FBRyxDQUFDLDZCQUE2QjJCO29CQUMzQyxPQUFPO3dCQUNMdEQsVUFBVSxFQUFFO29CQUNkO2dCQUNGO1lBQ0YsRUFBRSxPQUFPMkQsT0FBTztnQkFDZGpDLFFBQVFpQyxLQUFLLENBQUMsaUNBQWlDQTtZQUNqRCxTQUFVO2dCQUNSekQsV0FBVztZQUNiO1FBQ0Y7b0RBQUc7UUFBQ047S0FBTTtJQUVWLE1BQU1nRSxvQkFBb0JsRyxrREFBV0E7cURBQUMsT0FBT21HO1lBQzNDckQsa0JBQWtCO1lBQ2xCLElBQUk7Z0JBQ0Ysa0NBQWtDO2dCQUNsQyxNQUFNTSxjQUFzQztvQkFDMUMsZ0JBQWdCO29CQUNoQixVQUFVO2dCQUNaO2dCQUVBLHdDQUF3QztnQkFDeEMsSUFBSWxCLE9BQU87b0JBQ1RrQixXQUFXLENBQUMsZ0JBQWdCLEdBQUcsVUFBZ0IsT0FBTmxCO2dCQUMzQztnQkFFQSxNQUFNbUIsV0FBVyxNQUFNQyxNQUFNLHVCQUF1QjtvQkFDbERDLFFBQVE7b0JBQ1JDLFNBQVNKO29CQUNUSyxNQUFNQyxLQUFLQyxTQUFTLENBQUM7d0JBQ25CQyxtQkFBbUI7NEJBQ2pCYyxTQUFTeUI7NEJBQ1Qsd0RBQXdEOzRCQUN4RHRDLGlCQUFpQjt3QkFDbkI7b0JBQ0Y7Z0JBQ0Y7Z0JBRUEsTUFBTUMsT0FBTyxNQUFNVCxTQUFTVSxJQUFJO2dCQUNoQyxJQUFJRCxRQUFRQSxLQUFLQSxJQUFJLEVBQUU7b0JBQ3JCLE1BQU1zQyxhQUFhMUMsS0FBS1MsS0FBSyxDQUFDTCxLQUFLQSxJQUFJO29CQUN2QyxJQUFJTSxNQUFNQyxPQUFPLENBQUMrQixhQUFhO3dCQUM3QnhELGdCQUFnQndEO29CQUNsQjtnQkFDRjtZQUNGLEVBQUUsT0FBT0gsT0FBTztnQkFDZGpDLFFBQVFpQyxLQUFLLENBQUMsaUNBQWlDQTtZQUNqRCxTQUFVO2dCQUNSbkQsa0JBQWtCO1lBQ3BCO1FBQ0Y7b0RBQUc7UUFBQ1o7S0FBTTtJQUVWLHNCQUFzQjtJQUN0Qm5DLGdEQUFTQTtnQ0FBQztZQUNSLE1BQU1zRyxTQUFTdEUsQ0FBQUEsaUJBQUFBLDJCQUFBQSxLQUFNdUUsTUFBTSxNQUFJdkUsaUJBQUFBLDJCQUFBQSxLQUFNd0UsTUFBTTtZQUMzQyxJQUFJdkUsY0FBY3FFLFFBQVE7Z0JBQ3hCbEQ7WUFDRjtRQUNGOytCQUFHO1FBQUNuQjtRQUFZRDtRQUFNb0I7S0FBa0I7SUFFeEMsMEVBQTBFO0lBQzFFcEQsZ0RBQVNBO2dDQUFDO1lBQ1IsSUFBSSxDQUFDa0MsYUFBYSxDQUFDRCxZQUFZO2dCQUM3QndFLE9BQU9DLFFBQVEsQ0FBQ0MsSUFBSSxHQUFHO1lBQ3pCO1FBQ0Y7K0JBQUc7UUFBQ3pFO1FBQVdEO0tBQVc7SUFFMUIsNERBQTREO0lBQzVELElBQUlDLFdBQVc7UUFDYixxQkFDRSw4REFBQzBFO1lBQUlDLFdBQVU7c0JBQ2IsNEVBQUNEO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ0Q7d0JBQUlDLFdBQVU7Ozs7OztrQ0FDZiw4REFBQ0M7d0JBQUVELFdBQVU7a0NBQWdDOzs7Ozs7Ozs7Ozs7Ozs7OztJQUlyRDtJQUVBLHlDQUF5QztJQUN6QyxJQUFJLENBQUM1RSxZQUFZO1FBQ2YscUJBQ0UsOERBQUMyRTtZQUFJQyxXQUFVO3NCQUNiLDRFQUFDRDtnQkFBSUMsV0FBVTswQkFDYiw0RUFBQ0M7b0JBQUVELFdBQVU7OEJBQWdDOzs7Ozs7Ozs7Ozs7Ozs7O0lBTXJEO0lBSUEsOEJBQThCO0lBQzlCLE1BQU1FLGFBQWE7UUFDakI7WUFDRXJDLElBQUk7WUFDSkksTUFBTTtZQUNOSyxPQUFPO1lBQ1BLLFVBQVU7WUFDVkcsT0FBTztnQkFDTDtvQkFBRXFCLE1BQU07b0JBQTRCQyxhQUFhO29CQUE0QkMsVUFBVTtvQkFBR0MsT0FBTztnQkFBTTtnQkFDdkc7b0JBQUVILE1BQU07b0JBQWlCQyxhQUFhO29CQUFpQkMsVUFBVTtvQkFBR0MsT0FBTztnQkFBTTthQUNsRjtRQUNIO1FBQ0E7WUFDRXpDLElBQUk7WUFDSkksTUFBTTtZQUNOSyxPQUFPO1lBQ1BLLFVBQVU7WUFDVkcsT0FBTztnQkFDTDtvQkFBRXFCLE1BQU07b0JBQTRCQyxhQUFhO29CQUE0QkMsVUFBVTtvQkFBR0MsT0FBTztnQkFBTzthQUN6RztRQUNIO1FBQ0E7WUFDRXpDLElBQUk7WUFDSkksTUFBTTtZQUNOSyxPQUFPO1lBQ1BLLFVBQVU7WUFDVkcsT0FBTztnQkFDTDtvQkFBRXFCLE1BQU07b0JBQTJCQyxhQUFhO29CQUEyQkMsVUFBVTtvQkFBR0MsT0FBTztnQkFBTTthQUN0RztRQUNIO0tBQ0Q7SUFFRCx5QkFBeUI7SUFDekIsTUFBTUMsZ0JBQWdCNUUsVUFBVSxFQUFFLEdBQUdGO0lBRXJDLGdCQUFnQjtJQUNoQixNQUFNK0Usb0JBQW9CakYsY0FBYyxRQUNwQ2dGLGdCQUNBQSxjQUFjRSxNQUFNLENBQUM3QyxDQUFBQTtRQUNuQixNQUFNOEMsV0FBVzlDLE1BQU1lLFFBQVEsSUFBSWYsTUFBTStDLGFBQWEsSUFBSS9DLE1BQU1nRCxPQUFPLElBQUk7UUFDM0UsT0FBT0YsU0FBU0csUUFBUSxPQUFPdEY7SUFDakM7SUFFSix5QkFBeUI7SUFDekIsTUFBTXVGLHVCQUF1QjNFLFlBQVk0RSxJQUFJLE9BQU8sS0FDaERQLG9CQUNBQSxrQkFBa0JDLE1BQU0sQ0FBQzdDLENBQUFBO1FBQ3ZCLE1BQU1vRCxhQUFhN0UsWUFBWThFLFdBQVc7UUFDMUMsTUFBTUMsY0FBYyxDQUFDdEQsTUFBTUksV0FBVyxJQUFJLE1BQThELE9BQXhEbUQsT0FBT3ZELE1BQU1FLE9BQU8sSUFBSUYsTUFBTUcsT0FBTyxFQUFFcUQsUUFBUSxDQUFDLEdBQUcsS0FBSyxFQUFHSCxXQUFXO1FBQ3RILE1BQU1JLGVBQWUsQ0FBQ3pELE1BQU1rQixLQUFLLElBQUlsQixNQUFNbUIsVUFBVSxJQUFJLEVBQUUsRUFDeERwQixHQUFHLENBQUMsQ0FBQzJELE9BQWMsQ0FBQ0EsS0FBS25CLElBQUksSUFBSW1CLEtBQUtsQixXQUFXLElBQUlrQixLQUFLQyxRQUFRLElBQUksRUFBQyxFQUFHTixXQUFXLElBQ3JGTyxJQUFJLENBQUM7UUFFUixPQUFPTixZQUFZTyxRQUFRLENBQUNULGVBQWVLLGFBQWFJLFFBQVEsQ0FBQ1Q7SUFDbkU7SUFFSixjQUFjO0lBQ2QsTUFBTVUsaUJBQWlCO1dBQUlaO0tBQXFCLENBQUM3QixJQUFJLENBQUMsQ0FBQ0MsR0FBR0M7UUFDeEQsT0FBUTlDO1lBQ04sS0FBSztnQkFDSCxPQUFPLElBQUk4QixLQUFLZ0IsRUFBRWQsU0FBUyxJQUFJLEdBQUdlLE9BQU8sS0FBSyxJQUFJakIsS0FBS2UsRUFBRWIsU0FBUyxJQUFJLEdBQUdlLE9BQU87WUFDbEYsS0FBSztnQkFDSCxPQUFPLElBQUlqQixLQUFLZSxFQUFFYixTQUFTLElBQUksR0FBR2UsT0FBTyxLQUFLLElBQUlqQixLQUFLZ0IsRUFBRWQsU0FBUyxJQUFJLEdBQUdlLE9BQU87WUFDbEYsS0FBSztnQkFDSCxPQUFPLENBQUNELEVBQUViLEtBQUssSUFBSWEsRUFBRVosVUFBVSxJQUFJWSxFQUFFWCxXQUFXLElBQUksS0FBTVUsQ0FBQUEsRUFBRVosS0FBSyxJQUFJWSxFQUFFWCxVQUFVLElBQUlXLEVBQUVWLFdBQVcsSUFBSTtZQUN4RyxLQUFLO2dCQUNILE9BQU8sQ0FBQ1UsRUFBRVosS0FBSyxJQUFJWSxFQUFFWCxVQUFVLElBQUlXLEVBQUVWLFdBQVcsSUFBSSxLQUFNVyxDQUFBQSxFQUFFYixLQUFLLElBQUlhLEVBQUVaLFVBQVUsSUFBSVksRUFBRVgsV0FBVyxJQUFJO1lBQ3hHO2dCQUNFLE9BQU87UUFDWDtJQUNGO0lBRUEsbURBQW1EO0lBQ25ELE1BQU1tRCx3QkFBd0IsQ0FBQy9EO1FBQzdCLE1BQU0yQixVQUFVM0IsTUFBTUUsT0FBTyxJQUFJRixNQUFNRyxPQUFPLElBQUlILE1BQU1DLEVBQUU7UUFDMUQsTUFBTStELG1CQUFtQi9HLGdFQUFjQSxDQUFDMEU7UUFFeEMsOENBQThDO1FBQzlDLE1BQU1zQyxhQUFhLENBQUNqRSxNQUFNVSxLQUFLLElBQUlWLE1BQU1XLFVBQVUsSUFBSVgsTUFBTVksV0FBVyxJQUFJLEdBQUdzRCxPQUFPLENBQUM7UUFDdkYsTUFBTUMsWUFBWSxDQUFDO1lBQ2pCLE1BQU1DLFVBQVVwRSxNQUFNTSxZQUFZLElBQUlOLE1BQU1LLElBQUksSUFBSUwsTUFBTVMsU0FBUyxJQUFJVCxNQUFNcUUsU0FBUztZQUN0RixJQUFJRCxTQUFTO2dCQUNYLElBQUk7b0JBQ0YsTUFBTS9ELE9BQU8sSUFBSUUsS0FBSzZEO29CQUN0QixPQUFPL0QsS0FBS0csa0JBQWtCLENBQUMsU0FBUzt3QkFDdEM4RCxNQUFNO3dCQUNOQyxPQUFPO3dCQUNQQyxLQUFLO29CQUNQO2dCQUNGLEVBQUUsT0FBT0MsR0FBRztvQkFDVixPQUFPO2dCQUNUO1lBQ0Y7WUFDQSxPQUFPO1FBQ1Q7UUFFQSwrQkFBK0I7UUFDL0IsTUFBTUMsaUJBQWlCeEgsOERBQVlBLENBQUMrRztRQUNwQyxNQUFNVSxnQkFBZ0J6SCw4REFBWUEsQ0FBQ2lIO1FBRW5DLE9BQU8sV0FBaUNPLE9BQXRCVixrQkFBaUIsT0FBeUJXLE9BQXBCRCxnQkFBZSxPQUFtQixPQUFkQztJQUM5RDtJQUVBLG1DQUFtQztJQUNuQyxNQUFNQyxnQkFBZ0IsQ0FBQzlCO1FBQ3JCLE1BQU03QyxLQUFLNEUsU0FBUy9CLENBQUFBLHFCQUFBQSwrQkFBQUEsU0FBVUcsUUFBUSxPQUFNO1FBQzVDLE9BQU9oRDtZQUNMLEtBQUs7Z0JBQ0gsT0FBTztvQkFBRXNDLE1BQU07b0JBQVV1QyxvQkFBTSw4REFBQ3JJLHlKQUFPQTt3QkFBQzJGLFdBQVU7Ozs7OztvQkFBNEIyQyxPQUFPO2dCQUE0QjtZQUNuSCxLQUFLO2dCQUNILE9BQU87b0JBQUV4QyxNQUFNO29CQUFldUMsb0JBQU0sOERBQUNqSSx5SkFBS0E7d0JBQUN1RixXQUFVOzs7Ozs7b0JBQThCMkMsT0FBTztnQkFBZ0M7WUFDNUgsS0FBSztnQkFDSCxPQUFPO29CQUFFeEMsTUFBTTtvQkFBYXVDLG9CQUFNLDhEQUFDaEkseUpBQVdBO3dCQUFDc0YsV0FBVTs7Ozs7O29CQUE2QjJDLE9BQU87Z0JBQThCO1lBQzdILEtBQUs7Z0JBQ0gsT0FBTztvQkFBRXhDLE1BQU07b0JBQVl1QyxvQkFBTSw4REFBQzlILHlKQUFTQTt3QkFBQ29GLFdBQVU7Ozs7OztvQkFBOEIyQyxPQUFPO2dCQUFnQztZQUM3SCxLQUFLO2dCQUNILE9BQU87b0JBQUV4QyxNQUFNO29CQUFZdUMsb0JBQU0sOERBQUM5SCx5SkFBU0E7d0JBQUNvRixXQUFVOzs7Ozs7b0JBQThCMkMsT0FBTztnQkFBZ0M7WUFDN0gsS0FBSztnQkFDSCxPQUFPO29CQUFFeEMsTUFBTTtvQkFBYXVDLG9CQUFNLDhEQUFDL0gseUpBQVdBO3dCQUFDcUYsV0FBVTs7Ozs7O29CQUEyQjJDLE9BQU87Z0JBQTBCO1lBQ3ZIO2dCQUNFLE9BQU87b0JBQUV4QyxNQUFNO29CQUFXdUMsb0JBQU0sOERBQUNySSx5SkFBT0E7d0JBQUMyRixXQUFVOzs7Ozs7b0JBQTRCMkMsT0FBTztnQkFBNEI7UUFDdEg7SUFDRjtJQUlBLHFCQUNFLDhEQUFDNUM7UUFBSUMsV0FBVTs7MEJBRWIsOERBQUMzRyxpRUFBVUE7Z0JBQUMyRyxXQUFVOzBCQUNwQiw0RUFBQ3hHLHFFQUFjQTs7c0NBQ2IsOERBQUNGLHFFQUFjQTtzQ0FDYiw0RUFBQ0MscUVBQWNBO2dDQUFDcUosT0FBTzswQ0FDckIsNEVBQUMzSSxrREFBSUE7b0NBQUM2RixNQUFLOzhDQUFLOUUsRUFBRTs7Ozs7Ozs7Ozs7Ozs7OztzQ0FHdEIsOERBQUN0QiwwRUFBbUJBOzs7OztzQ0FDcEIsOERBQUNKLHFFQUFjQTtzQ0FDYiw0RUFBQ0cscUVBQWNBOzBDQUFFdUIsRUFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFNekIsOERBQUMrRTtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQzZDO2dDQUFHN0MsV0FBVTswQ0FBc0JoRixFQUFFOzs7Ozs7MENBQ3RDLDhEQUFDcEIseURBQU1BO2dDQUNMa0osU0FBUTtnQ0FDUkMsTUFBSztnQ0FDTEMsU0FBU3pHO2dDQUNUMEcsVUFBVXRIO2dDQUNWcUUsV0FBVTs7a0RBRVYsOERBQUNwRix5SkFBU0E7d0NBQUNvRixXQUFXLFdBQXlDLE9BQTlCckUsVUFBVSxpQkFBaUI7Ozs7OztvQ0FDM0RBLFVBQVUsZUFBZTs7Ozs7Ozs7Ozs7OztrQ0FLOUIsOERBQUM5QixxREFBSUE7d0JBQUNxSixjQUFhO3dCQUFNbEQsV0FBVTt3QkFBT21ELGVBQWUzSDtrQ0FDdkQsNEVBQUMxQix5REFBUUE7NEJBQUNrRyxXQUFVOzs4Q0FDbEIsOERBQUNqRyw0REFBV0E7b0NBQ1ZxSixPQUFNO29DQUNOcEQsV0FBVTtvQ0FDVnFELE9BQU87d0NBQ0xDLGlCQUFpQi9ILGNBQWMsUUFBUU4sZUFBZTt3Q0FDdEQwSCxPQUFPcEgsY0FBYyxRQUFRTCxtQkFBbUI7d0NBQ2hEcUksV0FBV2hJLGNBQWMsUUFBUSxnQkFBZ0I7d0NBQ2pEaUksV0FBV2pJLGNBQWMsUUFBUSw0RUFBNEU7d0NBQzdHa0ksYUFBYWxJLGNBQWMsUUFBUU4sZUFBZTtvQ0FDcEQ7OENBQ0Q7Ozs7Ozs4Q0FHRCw4REFBQ2xCLDREQUFXQTtvQ0FDVnFKLE9BQU07b0NBQ05wRCxXQUFVO29DQUNWcUQsT0FBTzt3Q0FDTEMsaUJBQWlCL0gsY0FBYyxNQUFNTixlQUFlO3dDQUNwRDBILE9BQU9wSCxjQUFjLE1BQU1MLG1CQUFtQjt3Q0FDOUNxSSxXQUFXaEksY0FBYyxNQUFNLGdCQUFnQjt3Q0FDL0NpSSxXQUFXakksY0FBYyxNQUFNLDRFQUE0RTt3Q0FDM0drSSxhQUFhbEksY0FBYyxNQUFNTixlQUFlO29DQUNsRDs4Q0FDRDs7Ozs7OzhDQUdELDhEQUFDbEIsNERBQVdBO29DQUNWcUosT0FBTTtvQ0FDTnBELFdBQVU7b0NBQ1ZxRCxPQUFPO3dDQUNMQyxpQkFBaUIvSCxjQUFjLE1BQU1OLGVBQWU7d0NBQ3BEMEgsT0FBT3BILGNBQWMsTUFBTUwsbUJBQW1CO3dDQUM5Q3FJLFdBQVdoSSxjQUFjLE1BQU0sZ0JBQWdCO3dDQUMvQ2lJLFdBQVdqSSxjQUFjLE1BQU0sNEVBQTRFO3dDQUMzR2tJLGFBQWFsSSxjQUFjLE1BQU1OLGVBQWU7b0NBQ2xEOzhDQUNEOzs7Ozs7OENBR0QsOERBQUNsQiw0REFBV0E7b0NBQ1ZxSixPQUFNO29DQUNOcEQsV0FBVTtvQ0FDVnFELE9BQU87d0NBQ0xDLGlCQUFpQi9ILGNBQWMsTUFBTU4sZUFBZTt3Q0FDcEQwSCxPQUFPcEgsY0FBYyxNQUFNTCxtQkFBbUI7d0NBQzlDcUksV0FBV2hJLGNBQWMsTUFBTSxnQkFBZ0I7d0NBQy9DaUksV0FBV2pJLGNBQWMsTUFBTSw0RUFBNEU7d0NBQzNHa0ksYUFBYWxJLGNBQWMsTUFBTU4sZUFBZTtvQ0FDbEQ7OENBQ0Q7Ozs7Ozs4Q0FHRCw4REFBQ2xCLDREQUFXQTtvQ0FDVnFKLE9BQU07b0NBQ05wRCxXQUFVO29DQUNWcUQsT0FBTzt3Q0FDTEMsaUJBQWlCL0gsY0FBYyxNQUFNTixlQUFlO3dDQUNwRDBILE9BQU9wSCxjQUFjLE1BQU1MLG1CQUFtQjt3Q0FDOUNxSSxXQUFXaEksY0FBYyxNQUFNLGdCQUFnQjt3Q0FDL0NpSSxXQUFXakksY0FBYyxNQUFNLDRFQUE0RTt3Q0FDM0drSSxhQUFhbEksY0FBYyxNQUFNTixlQUFlO29DQUNsRDs4Q0FDRDs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBT0wsOERBQUM4RTt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQzFGLHlKQUFNQTt3Q0FBQzBGLFdBQVU7Ozs7OztrREFDbEIsOERBQUMwRDt3Q0FDQ0MsTUFBSzt3Q0FDTEMsYUFBWTt3Q0FDWlIsT0FBT2pIO3dDQUNQMEgsVUFBVSxDQUFDeEIsSUFBTWpHLGVBQWVpRyxFQUFFeUIsTUFBTSxDQUFDVixLQUFLO3dDQUM5Q3BELFdBQVU7Ozs7Ozs7Ozs7OzswQ0FJZCw4REFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDK0Q7d0NBQUsvRCxXQUFVO2tEQUFnQzs7Ozs7O2tEQUNoRCw4REFBQ2dFO3dDQUNDWixPQUFPL0c7d0NBQ1B3SCxVQUFVLENBQUN4QixJQUFNL0YsVUFBVStGLEVBQUV5QixNQUFNLENBQUNWLEtBQUs7d0NBQ3pDcEQsV0FBVTs7MERBRVYsOERBQUNpRTtnREFBT2IsT0FBTTswREFBWTs7Ozs7OzBEQUMxQiw4REFBQ2E7Z0RBQU9iLE9BQU07MERBQVc7Ozs7OzswREFDekIsOERBQUNhO2dEQUFPYixPQUFNOzBEQUFhOzs7Ozs7MERBQzNCLDhEQUFDYTtnREFBT2IsT0FBTTswREFBWTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O29CQU0vQnpILHdCQUNDLDhEQUFDb0U7d0JBQUlDLFdBQVU7a0NBRVo7K0JBQUl4QyxNQUFNO3lCQUFHLENBQUNHLEdBQUcsQ0FBQyxDQUFDdUcsR0FBR0Msc0JBQ3JCLDhEQUFDeEsscURBQUlBO2dDQUFhcUcsV0FBVTswQ0FDMUIsNEVBQUNEO29DQUFJQyxXQUFVOzhDQUNiLDRFQUFDRDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNEO2dEQUFJQyxXQUFVOztrRUFDYiw4REFBQ0Q7d0RBQUlDLFdBQVU7OzBFQUNiLDhEQUFDaEcsNkRBQVFBO2dFQUFDZ0csV0FBVTs7Ozs7OzBFQUNwQiw4REFBQ2hHLDZEQUFRQTtnRUFBQ2dHLFdBQVU7Ozs7Ozs7Ozs7OztrRUFFdEIsOERBQUNEO3dEQUFJQyxXQUFVOzswRUFDYiw4REFBQ2hHLDZEQUFRQTtnRUFBQ2dHLFdBQVU7Ozs7OzswRUFDcEIsOERBQUNoRyw2REFBUUE7Z0VBQUNnRyxXQUFVOzs7Ozs7MEVBQ3BCLDhEQUFDaEcsNkRBQVFBO2dFQUFDZ0csV0FBVTs7Ozs7Ozs7Ozs7O2tFQUV0Qiw4REFBQ2hHLDZEQUFRQTt3REFBQ2dHLFdBQVU7Ozs7Ozs7Ozs7OzswREFFdEIsOERBQUNEO2dEQUFJQyxXQUFVOztrRUFDYiw4REFBQ2hHLDZEQUFRQTt3REFBQ2dHLFdBQVU7Ozs7OztrRUFDcEIsOERBQUNoRyw2REFBUUE7d0RBQUNnRyxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzsrQkFqQmpCbUU7Ozs7Ozs7OzsrQkF3QmJ6QyxlQUFlMEMsTUFBTSxHQUFHLGtCQUMxQiw4REFBQ3JFO3dCQUFJQyxXQUFVO2tDQUNaMEIsZUFBZS9ELEdBQUcsQ0FBQyxDQUFDQyxPQUFPeUc7Z0NBZ0lRekcsY0FBdUJBLG1CQXFFakRBLGVBQXVCQSxvQkFFbEJBLGVBQXVCQTtpREF0TXBDLDhEQUFDakUscURBQUlBO2dDQUFnRXFHLFdBQVU7O2tEQUM3RSw4REFBQ0Q7d0NBQUlDLFdBQVU7a0RBQ2IsNEVBQUNEOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ0Q7b0RBQUlDLFdBQVU7O3NFQUNiLDhEQUFDRDs0REFBSUMsV0FBVTs7OEVBQ2IsOERBQUNzRTtvRUFBR3RFLFdBQVU7OEVBQ1hwQyxNQUFNSSxXQUFXLElBQUksTUFBOEQsT0FBeERtRCxPQUFPdkQsTUFBTUUsT0FBTyxJQUFJRixNQUFNRyxPQUFPLEVBQUVxRCxRQUFRLENBQUMsR0FBRzs7Ozs7O2dFQUUvRTtvRUFDQSxzRkFBc0Y7b0VBQ3RGLE1BQU1tRCxhQUFhM0csTUFBTWMsZ0JBQWdCO29FQUN6QyxJQUFJNkYsWUFBWTt3RUFDZCw2Q0FBNkM7d0VBQzdDLElBQUlDLGdCQUFnQkQ7d0VBQ3BCLElBQUlFLGNBQWM7d0VBQ2xCLElBQUlDLDJCQUFhLDhEQUFDcksseUpBQU9BOzRFQUFDMkYsV0FBVTs7Ozs7O3dFQUVwQyxPQUFRdUUsV0FBV3RELFdBQVc7NEVBQzVCLEtBQUs7Z0ZBQ0h3RCxjQUFjO2dGQUNkQywyQkFBYSw4REFBQ3JLLHlKQUFPQTtvRkFBQzJGLFdBQVU7Ozs7OztnRkFDaEM7NEVBQ0YsS0FBSzs0RUFDTCxLQUFLO2dGQUNIeUUsY0FBYztnRkFDZEMsMkJBQWEsOERBQUNqSyx5SkFBS0E7b0ZBQUN1RixXQUFVOzs7Ozs7Z0ZBQzlCOzRFQUNGLEtBQUs7NEVBQ0wsS0FBSztnRkFDSHlFLGNBQWM7Z0ZBQ2RDLDJCQUFhLDhEQUFDaEsseUpBQVdBO29GQUFDc0YsV0FBVTs7Ozs7O2dGQUNwQzs0RUFDRixLQUFLO2dGQUNIeUUsY0FBYztnRkFDZEMsMkJBQWEsOERBQUMvSix5SkFBV0E7b0ZBQUNxRixXQUFVOzs7Ozs7Z0ZBQ3BDOzRFQUNGO2dGQUNFeUUsY0FBYztnRkFDZEMsMkJBQWEsOERBQUNySyx5SkFBT0E7b0ZBQUMyRixXQUFVOzs7Ozs7d0VBQ3BDO3dFQUVBLHFCQUNFLDhEQUFDRDs0RUFBSUMsV0FBVywwREFBc0UsT0FBWnlFOztnRkFDdkVDOzhGQUNELDhEQUFDWDs4RkFBTVM7Ozs7Ozs7Ozs7OztvRUFHYixPQUFPO3dFQUNMLCtCQUErQjt3RUFDL0IsTUFBTTlELFdBQVc5QyxNQUFNZSxRQUFRLElBQUlmLE1BQU0rQyxhQUFhLElBQUkvQyxNQUFNZ0QsT0FBTyxJQUFJO3dFQUMzRSxNQUFNK0QsYUFBYW5DLGNBQWM5Qjt3RUFDakMscUJBQ0UsOERBQUNYOzRFQUFJQyxXQUFXLDBEQUEyRSxPQUFqQjJFLFdBQVdoQyxLQUFLOztnRkFDdkZnQyxXQUFXakMsSUFBSTs4RkFDaEIsOERBQUNxQjs4RkFBTVksV0FBV3hFLElBQUk7Ozs7Ozs7Ozs7OztvRUFHNUI7Z0VBQ0Y7Ozs7Ozs7c0VBRUYsOERBQUNGOzREQUFFRCxXQUFVOztnRUFBcUM7Z0VBQ25DO29FQUNYLE1BQU1nQyxVQUFVcEUsTUFBTU0sWUFBWSxJQUFJTixNQUFNSyxJQUFJLElBQUlMLE1BQU1TLFNBQVMsSUFBSVQsTUFBTXFFLFNBQVM7b0VBQ3RGLElBQUlELFNBQVM7d0VBQ1gsSUFBSTs0RUFDRixNQUFNL0QsT0FBTyxJQUFJRSxLQUFLNkQ7NEVBQ3RCLE9BQU8vRCxLQUFLRyxrQkFBa0IsQ0FBQyxTQUFTO2dGQUN0QzhELE1BQU07Z0ZBQ05DLE9BQU87Z0ZBQ1BDLEtBQUs7Z0ZBQ0x3QyxNQUFNO2dGQUNOQyxRQUFROzRFQUNWO3dFQUNGLEVBQUUsT0FBT3hDLEdBQUc7NEVBQ1YsT0FBT0w7d0VBQ1Q7b0VBQ0Y7b0VBQ0EsT0FBTztnRUFDVDs7Ozs7Ozt3REFHQXBFLENBQUFBLE1BQU1rQixLQUFLLElBQUlsQixNQUFNbUIsVUFBVSxJQUFJLEVBQUUsRUFBRXFGLE1BQU0sR0FBRyxtQkFDaEQsOERBQUNyRTs0REFBSUMsV0FBVTs7OEVBQ2IsOERBQUNDO29FQUFFRCxXQUFVOzhFQUF5Qzs7Ozs7OzhFQUN0RCw4REFBQ0Q7b0VBQUlDLFdBQVU7O3dFQUNYcEMsQ0FBQUEsTUFBTWtCLEtBQUssSUFBSWxCLE1BQU1tQixVQUFVLElBQUksRUFBRSxFQUNwQytGLEtBQUssQ0FBQyxHQUFHLEdBQ1RuSCxHQUFHLENBQUMsQ0FBQzJELE1BQVc2Qzs0RUFDZixrQ0FBa0M7NEVBQ2xDLE1BQU1ZLGNBQWN6RCxLQUFLbkIsSUFBSSxJQUNYbUIsS0FBS2xCLFdBQVcsSUFDaEJrQixLQUFLQyxRQUFRLElBQ2I7NEVBQ2xCLE1BQU15RCxZQUFZMUQsS0FBSzJELFNBQVMsSUFBSTNELEtBQUs0RCxTQUFTOzRFQUVsRCxxQkFDRSw4REFBQ25COztvRkFDRWlCLDBCQUNDLDhEQUFDL0ssa0RBQUlBO3dGQUNINkYsTUFBTSxZQUFzQixPQUFWa0Y7d0ZBQ2xCaEYsV0FBVTt3RkFDVjhELFFBQU87d0ZBQ1BxQixLQUFJO2tHQUVISjs7Ozs7NkdBR0gsOERBQUNoQjt3RkFBSy9ELFdBQVU7a0dBQWUrRTs7Ozs7O29GQUVoQ1osUUFBUWlCLEtBQUtDLEdBQUcsQ0FBQyxHQUFHLENBQUN6SCxNQUFNa0IsS0FBSyxJQUFJbEIsTUFBTW1CLFVBQVUsSUFBSSxFQUFFLEVBQUVxRixNQUFNLEdBQUcsTUFBTTs7K0VBYm5FRDs7Ozs7d0VBZ0JmO3dFQUNBdkcsQ0FBQUEsTUFBTWtCLEtBQUssSUFBSWxCLE1BQU1tQixVQUFVLElBQUksRUFBRSxFQUFFcUYsTUFBTSxHQUFHLG1CQUNoRCw4REFBQ0w7NEVBQUsvRCxXQUFVOztnRkFBZ0I7Z0ZBQU1wQyxDQUFBQSxNQUFNa0IsS0FBSyxJQUFJbEIsTUFBTW1CLFVBQVUsSUFBSSxFQUFFLEVBQUVxRixNQUFNLEdBQUc7Z0ZBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OERBT2xHLDhEQUFDckU7b0RBQUlDLFdBQVU7O3NFQUNiLDhEQUFDRDs0REFBSUMsV0FBVTs7OEVBQ2IsOERBQUNDO29FQUFFRCxXQUFVOzt3RUFBYzt3RUFDdEJwQyxDQUFBQSxNQUFNVSxLQUFLLElBQUlWLE1BQU1XLFVBQVUsSUFBSVgsTUFBTVksV0FBVyxJQUFJLEdBQUdzRCxPQUFPLENBQUM7Ozs7Ozs7OEVBRXhFLDhEQUFDN0I7b0VBQUVELFdBQVU7O3dFQUNWcEMsTUFBTWlCLFVBQVUsSUFBS2pCLEVBQUFBLGVBQUFBLE1BQU1rQixLQUFLLGNBQVhsQixtQ0FBQUEsYUFBYXdHLE1BQU0sT0FBSXhHLG9CQUFBQSxNQUFNbUIsVUFBVSxjQUFoQm5CLHdDQUFBQSxrQkFBa0J3RyxNQUFNLEtBQUl4RyxNQUFNZ0IsU0FBUyxJQUFJO3dFQUFHOzs7Ozs7Ozs7Ozs7O3NFQUluRyw4REFBQ2hGLHlEQUFNQTs0REFDTGtKLFNBQVE7NERBQ1JDLE1BQUs7NERBQ0wvQyxXQUFVOzREQUNWNEMsT0FBTztzRUFFUCw0RUFBQzNJLGtEQUFJQTtnRUFBQzZGLE1BQU02QixzQkFBc0IvRDs7a0ZBQ2hDLDhEQUFDcEQseUpBQVFBO3dFQUFDd0YsV0FBVTs7Ozs7O29FQUFZO2tGQUVoQyw4REFBQ3pGLHlKQUFZQTt3RUFBQ3lGLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBUWxDLDhEQUFDRDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNzRjtnREFBR3RGLFdBQVU7MERBQTJCOzs7Ozs7MERBQ3pDLDhEQUFDRDtnREFBSUMsV0FBVTs7b0RBQ1hwQyxDQUFBQSxNQUFNa0IsS0FBSyxJQUFJbEIsTUFBTW1CLFVBQVUsSUFBSSxFQUFFLEVBQUUrRixLQUFLLENBQUMsR0FBRyxHQUFHbkgsR0FBRyxDQUFDLENBQUMyRCxNQUFXNkM7d0RBQ25FLE1BQU1vQixlQUFlakUsS0FBS2tFLGVBQWUsSUFBSWxFLEtBQUttRSxRQUFRLElBQUluRSxLQUFLb0UsWUFBWTt3REFFL0UscUJBQ0UsOERBQUMzRjs0REFBZ0JDLFdBQVU7OzhFQUN6Qiw4REFBQ0Q7b0VBQUlDLFdBQVU7O3NGQUNiLDhEQUFDRDs0RUFBSUMsV0FBVTtzRkFDWnVGLDZCQUNDLDhEQUFDSTtnRkFDQ0MsS0FBS0wsYUFBYU0sVUFBVSxDQUFDLFVBQVVOLGVBQWUsR0FBMkJBLE9BQXhCbkwsZ0RBQU1BLENBQUMwTCxjQUFjLEVBQWdCLE9BQWJQO2dGQUNqRlEsS0FBS3pFLEtBQUtuQixJQUFJLElBQUltQixLQUFLbEIsV0FBVyxJQUFJO2dGQUN0Q0osV0FBVTtnRkFDVmdHLFNBQVMsQ0FBQzNEO29GQUNSLE1BQU15QixTQUFTekIsRUFBRXlCLE1BQU07b0ZBQ3ZCQSxPQUFPVCxLQUFLLENBQUM0QyxPQUFPLEdBQUc7b0ZBQ3ZCLE1BQU1DLFNBQVNwQyxPQUFPcUMsYUFBYTtvRkFDbkMsSUFBSUQsUUFBUTt3RkFDVkEsT0FBT0UsU0FBUyxHQUFHO29GQUNyQjtnRkFDRjs7Ozs7cUdBR0YsOERBQUMvTCx5SkFBT0E7Z0ZBQUMyRixXQUFVOzs7Ozs7Ozs7OztzRkFHdkIsOERBQUNEOzRFQUFJQyxXQUFVOzs4RkFDYiw4REFBQ0M7b0ZBQUVELFdBQVU7OEZBQ1ZzQixLQUFLbkIsSUFBSSxJQUFJbUIsS0FBS2xCLFdBQVcsSUFBSWtCLEtBQUtDLFFBQVEsSUFBSTs7Ozs7OzhGQUVyRCw4REFBQ3RCO29GQUFFRCxXQUFVOzt3RkFBZ0M7d0ZBQ3JDc0IsS0FBS2pCLFFBQVEsSUFBSWlCLEtBQUsrRSxRQUFRLElBQUkvRSxLQUFLZ0YsYUFBYSxJQUFJOzs7Ozs7O2dGQUUvRGhGLEtBQUtpRixrQkFBa0Isa0JBQ3RCLDhEQUFDdEc7b0ZBQUVELFdBQVU7OEZBQ1ZzQixLQUFLaUYsa0JBQWtCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OEVBS2hDLDhEQUFDdEc7b0VBQUVELFdBQVU7O3dFQUFzQjt3RUFDOUJzQixDQUFBQSxLQUFLaEIsS0FBSyxJQUFJZ0IsS0FBS2tGLEtBQUssSUFBSWxGLEtBQUttRixTQUFTLElBQUluRixLQUFLb0YsU0FBUyxJQUFJLEdBQUc1RSxPQUFPLENBQUM7Ozs7Ozs7OzJEQXBDeEVxQzs7Ozs7b0RBd0NkO29EQUNFdkcsQ0FBQUEsRUFBQUEsZ0JBQUFBLE1BQU1rQixLQUFLLGNBQVhsQixvQ0FBQUEsY0FBYXdHLE1BQU0sT0FBSXhHLHFCQUFBQSxNQUFNbUIsVUFBVSxjQUFoQm5CLHlDQUFBQSxtQkFBa0J3RyxNQUFNLEtBQUksS0FBSyxtQkFDeEQsOERBQUNyRTt3REFBSUMsV0FBVTs7NERBQWlEOzREQUMzRHBDLENBQUFBLEVBQUFBLGdCQUFBQSxNQUFNa0IsS0FBSyxjQUFYbEIsb0NBQUFBLGNBQWF3RyxNQUFNLE9BQUl4RyxxQkFBQUEsTUFBTW1CLFVBQVUsY0FBaEJuQix5Q0FBQUEsbUJBQWtCd0csTUFBTSxLQUFJLEtBQUs7NERBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OytCQXRNNUR4RyxNQUFNRSxPQUFPLElBQUlGLE1BQU1HLE9BQU8sSUFBSUgsTUFBTUMsRUFBRSxJQUFJd0c7Ozs7Ozs7Ozs7NkNBK003RCw4REFBQzFLLHFEQUFJQTtrQ0FDSCw0RUFBQ29HOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ0Q7b0NBQUlDLFdBQVU7OENBQ2IsNEVBQUMzRix5SkFBT0E7d0NBQUMyRixXQUFVOzs7Ozs7Ozs7Ozs4Q0FFckIsOERBQUNzRTtvQ0FBR3RFLFdBQVU7OENBQTJCOzs7Ozs7OENBQ3pDLDhEQUFDQztvQ0FBRUQsV0FBVTs7d0NBQTZCO3dDQUFvQnpFLGNBQWMsUUFBUUEsWUFBWTt3Q0FBRzs7Ozs7Ozs4Q0FDbkcsOERBQUMzQix5REFBTUE7b0NBQUNnSixPQUFPOzhDQUNiLDRFQUFDM0ksa0RBQUlBO3dDQUFDNkYsTUFBSztrREFBSTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQVUvQjtHQXJyQndCL0U7O1FBQ3dCYixtRUFBV0E7UUFDVkMsMkRBQU9BOzs7S0FGaENZIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHloeWFzb2Z0XFxEb3dubG9hZHNcXGVjXFwuTkVUIDggVmVyc2lvbiAtIExhdGVzdFxccHJvamVjdFxcY29kZW1lZGljYWxcXHByb2plY3QzXFxhcHBcXG9yZGVyc1xccGFnZS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xyXG5cclxuaW1wb3J0IHsgdXNlU3RhdGUsIHVzZUVmZmVjdCwgdXNlQ2FsbGJhY2sgfSBmcm9tICdyZWFjdCc7XHJcbmltcG9ydCB7IEJyZWFkY3J1bWIsIEJyZWFkY3J1bWJJdGVtLCBCcmVhZGNydW1iTGluaywgQnJlYWRjcnVtYkxpc3QsIEJyZWFkY3J1bWJQYWdlLCBCcmVhZGNydW1iU2VwYXJhdG9yIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2JyZWFkY3J1bWInO1xyXG5pbXBvcnQgeyBDYXJkIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2NhcmQnO1xyXG5pbXBvcnQgeyBCdXR0b24gfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvYnV0dG9uJztcclxuaW1wb3J0IHsgVGFicywgVGFic0NvbnRlbnQsIFRhYnNMaXN0LCBUYWJzVHJpZ2dlciB9IGZyb20gJ0AvY29tcG9uZW50cy91aS90YWJzJztcclxuaW1wb3J0IHsgU2tlbGV0b24gfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvc2tlbGV0b24nO1xyXG5pbXBvcnQgTGluayBmcm9tICduZXh0L2xpbmsnO1xyXG5pbXBvcnQgeyB1c2VTZXR0aW5ncyB9IGZyb20gJ0AvY29udGV4dHMvc2V0dGluZ3MtY29udGV4dCc7XHJcbmltcG9ydCB7IHVzZVVzZXIgfSBmcm9tICdAL2NvbnRleHRzL3VzZXItY29udGV4dCc7XHJcblxyXG5pbXBvcnQgeyBDb25maWcgfSBmcm9tICdAL2xpYi9jb25maWcnO1xyXG5pbXBvcnQgeyBQYWNrYWdlLCBTZWFyY2gsIENoZXZyb25SaWdodCwgRmlsZVRleHQsIFRydWNrLCBDaGVja0NpcmNsZSwgQWxlcnRDaXJjbGUsIFJlZnJlc2hDdyB9IGZyb20gJ2x1Y2lkZS1yZWFjdCc7XHJcbmltcG9ydCB7IGVuY3J5cHRPcmRlcklkLCBlbmNyeXB0VmFsdWUgfSBmcm9tICdAL2xpYi9lbmNyeXB0aW9uJztcclxuXHJcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIE9yZGVyc1BhZ2UoKSB7XHJcbiAgY29uc3QgeyB0LCBwcmltYXJ5Q29sb3IsIHByaW1hcnlUZXh0Q29sb3IgfSA9IHVzZVNldHRpbmdzKCk7XHJcbiAgY29uc3QgeyB1c2VyLCBpc0xvZ2dlZEluLCBpc0xvYWRpbmcsIHRva2VuIH0gPSB1c2VVc2VyKCk7XHJcbiAgY29uc3QgW2FjdGl2ZVRhYiwgc2V0QWN0aXZlVGFiXSA9IHVzZVN0YXRlKCdhbGwnKTtcclxuICBjb25zdCBbb3JkZXJzLCBzZXRPcmRlcnNdID0gdXNlU3RhdGU8YW55W10+KFtdKTtcclxuICBjb25zdCBbbG9hZGluZywgc2V0TG9hZGluZ10gPSB1c2VTdGF0ZSh0cnVlKTtcclxuICBjb25zdCBbc2VsZWN0ZWRPcmRlciwgc2V0U2VsZWN0ZWRPcmRlcl0gPSB1c2VTdGF0ZTxhbnk+KG51bGwpO1xyXG4gIGNvbnN0IFtvcmRlckRldGFpbHMsIHNldE9yZGVyRGV0YWlsc10gPSB1c2VTdGF0ZTxhbnlbXT4oW10pO1xyXG4gIGNvbnN0IFtsb2FkaW5nRGV0YWlscywgc2V0TG9hZGluZ0RldGFpbHNdID0gdXNlU3RhdGUoZmFsc2UpO1xyXG4gIGNvbnN0IFtzZWFyY2hRdWVyeSwgc2V0U2VhcmNoUXVlcnldID0gdXNlU3RhdGUoJycpO1xyXG4gIGNvbnN0IFtzb3J0QnksIHNldFNvcnRCeV0gPSB1c2VTdGF0ZSgnZGF0ZS1kZXNjJyk7XHJcblxyXG4gIC8vIERlZmluZSBhbGwgdXNlQ2FsbGJhY2sgaG9va3MgYmVmb3JlIGFueSBjb25kaXRpb25hbCByZXR1cm5zXHJcbiAgY29uc3QgZmV0Y2hPcmRlckhpc3RvcnkgPSB1c2VDYWxsYmFjayhhc3luYyAoKSA9PiB7XHJcbiAgICBzZXRMb2FkaW5nKHRydWUpO1xyXG4gICAgdHJ5IHtcclxuICAgICAgLy8gR2V0IEpXVCB0b2tlbiBmcm9tIHVzZXIgY29udGV4dFxyXG4gICAgICBjb25zdCBhdXRoSGVhZGVyczogUmVjb3JkPHN0cmluZywgc3RyaW5nPiA9IHtcclxuICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxyXG4gICAgICAgICdBY2NlcHQnOiAnYXBwbGljYXRpb24vanNvbidcclxuICAgICAgfTtcclxuXHJcbiAgICAgIC8vIEFkZCBKV1QgdG9rZW4gdG8gaGVhZGVycyBpZiBhdmFpbGFibGVcclxuICAgICAgaWYgKHRva2VuKSB7XHJcbiAgICAgICAgYXV0aEhlYWRlcnNbJ0F1dGhvcml6YXRpb24nXSA9IGBCZWFyZXIgJHt0b2tlbn1gO1xyXG4gICAgICB9XHJcblxyXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKCcvYXBpL29yZGVycy9oaXN0b3J5Jywge1xyXG4gICAgICAgIG1ldGhvZDogJ1BPU1QnLFxyXG4gICAgICAgIGhlYWRlcnM6IGF1dGhIZWFkZXJzLFxyXG4gICAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KHtcclxuICAgICAgICAgIHJlcXVlc3RQYXJhbWV0ZXJzOiB7XHJcbiAgICAgICAgICAgIC8vIFVzZXJJZCByZW1vdmVkIC0gd2lsbCBiZSBhdXRvLWluamVjdGVkIGZyb20gSldUIHRva2VuXHJcbiAgICAgICAgICAgIHJlY29yZFZhbHVlSnNvbjogXCJbXVwiXHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfSlcclxuICAgICAgfSk7XHJcblxyXG4gICAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xyXG4gICAgICBjb25zb2xlLmxvZygnT3JkZXJzIEFQSSByZXNwb25zZTonLCBkYXRhKTtcclxuXHJcbiAgICAgIGlmIChkYXRhICYmIGRhdGEuZGF0YSkge1xyXG4gICAgICAgIGxldCBvcmRlcnNEYXRhO1xyXG4gICAgICAgIFxyXG4gICAgICAgIC8vIENoZWNrIGlmIGRhdGEgaXMgYSBzdHJpbmcgdGhhdCBuZWVkcyBwYXJzaW5nIChsaWtlIGxvZ2luIEFQSSkgb3IgYWxyZWFkeSBwYXJzZWRcclxuICAgICAgICBpZiAodHlwZW9mIGRhdGEuZGF0YSA9PT0gJ3N0cmluZycpIHtcclxuICAgICAgICAgIG9yZGVyc0RhdGEgPSBKU09OLnBhcnNlKGRhdGEuZGF0YSk7XHJcbiAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgIG9yZGVyc0RhdGEgPSBkYXRhLmRhdGE7XHJcbiAgICAgICAgfVxyXG4gICAgICAgIFxyXG4gICAgICAgIGNvbnNvbGUubG9nKCdQYXJzZWQgb3JkZXIgZGF0YTonLCBvcmRlcnNEYXRhKTtcclxuICAgICAgICBcclxuICAgICAgICBpZiAoQXJyYXkuaXNBcnJheShvcmRlcnNEYXRhKSkge1xyXG4gICAgICAgICAgY29uc29sZS5sb2coJ0ZpcnN0IG9yZGVyIHN0cnVjdHVyZTonLCBvcmRlcnNEYXRhWzBdKTtcclxuICAgICAgICAgIFxyXG4gICAgICAgICAgLy8gTWFwIHRoZSBBUEkgcmVzcG9uc2UgdG8gb3VyIGV4cGVjdGVkIGZvcm1hdFxyXG4gICAgICAgICAgY29uc3QgbWFwcGVkT3JkZXJzID0gb3JkZXJzRGF0YS5tYXAob3JkZXIgPT4gKHtcclxuICAgICAgICAgICAgaWQ6IG9yZGVyLk9yZGVySWQsXHJcbiAgICAgICAgICAgIE9yZGVySUQ6IG9yZGVyLk9yZGVySWQsXHJcbiAgICAgICAgICAgIE9yZGVyTnVtYmVyOiBvcmRlci5PcmRlck51bWJlcixcclxuICAgICAgICAgICAgZGF0ZTogb3JkZXIuT3JkZXJEYXRlVVRDID8gbmV3IERhdGUob3JkZXIuT3JkZXJEYXRlVVRDKS50b0xvY2FsZURhdGVTdHJpbmcoKSA6ICdOL0EnLFxyXG4gICAgICAgICAgICBPcmRlckRhdGU6IG9yZGVyLk9yZGVyRGF0ZVVUQyxcclxuICAgICAgICAgICAgdG90YWw6IG9yZGVyLk9yZGVyVG90YWwgfHwgMCxcclxuICAgICAgICAgICAgT3JkZXJUb3RhbDogb3JkZXIuT3JkZXJUb3RhbCB8fCAwLFxyXG4gICAgICAgICAgICBUb3RhbEFtb3VudDogb3JkZXIuT3JkZXJUb3RhbCB8fCAwLFxyXG4gICAgICAgICAgICBzdGF0dXM6IG9yZGVyLkxhdGVzdFN0YXR1c05hbWUgfHwgJ0FjdGl2ZScsXHJcbiAgICAgICAgICAgIFN0YXR1c0lEOiBvcmRlci5MYXRlc3RTdGF0dXNOYW1lID09PSAnQWN0aXZlJyA/IDEgOiBcclxuICAgICAgICAgICAgICAgICAgICAgb3JkZXIuTGF0ZXN0U3RhdHVzTmFtZSA9PT0gJ0luIFByb2dyZXNzJyA/IDIgOlxyXG4gICAgICAgICAgICAgICAgICAgICBvcmRlci5MYXRlc3RTdGF0dXNOYW1lID09PSAnQ29tcGxldGVkJyA/IDMgOlxyXG4gICAgICAgICAgICAgICAgICAgICBvcmRlci5MYXRlc3RTdGF0dXNOYW1lID09PSAnUmV0dXJuZWQnID8gNCA6XHJcbiAgICAgICAgICAgICAgICAgICAgIG9yZGVyLkxhdGVzdFN0YXR1c05hbWUgPT09ICdSZWZ1bmRlZCcgPyA1IDpcclxuICAgICAgICAgICAgICAgICAgICAgb3JkZXIuTGF0ZXN0U3RhdHVzTmFtZSA9PT0gJ0NhbmNlbGxlZCcgPyA2IDogMSxcclxuICAgICAgICAgICAgSXRlbUNvdW50OiBvcmRlci5Ub3RhbEl0ZW1zIHx8IDAsXHJcbiAgICAgICAgICAgIGl0ZW1zOiBvcmRlci5PcmRlckl0ZW1zIHx8IFtdLFxyXG4gICAgICAgICAgICBPcmRlckl0ZW1zOiBvcmRlci5PcmRlckl0ZW1zIHx8IFtdXHJcbiAgICAgICAgICB9KSk7XHJcbiAgICAgICAgICBcclxuICAgICAgICAgIC8vIFNvcnQgb3JkZXJzIGZyb20gbmV3ZXN0IHRvIG9sZGVzdFxyXG4gICAgICAgICAgY29uc3Qgc29ydGVkT3JkZXJzID0gbWFwcGVkT3JkZXJzLnNvcnQoKGEsIGIpID0+IHtcclxuICAgICAgICAgICAgcmV0dXJuIG5ldyBEYXRlKGIuT3JkZXJEYXRlIHx8IDApLmdldFRpbWUoKSAtIG5ldyBEYXRlKGEuT3JkZXJEYXRlIHx8IDApLmdldFRpbWUoKTtcclxuICAgICAgICAgIH0pO1xyXG4gICAgICAgICAgXHJcbiAgICAgICAgICBzZXRPcmRlcnMoc29ydGVkT3JkZXJzKTtcclxuICAgICAgICAgIGNvbnNvbGUubG9nKCdNYXBwZWQgYW5kIHNvcnRlZCBvcmRlcnM6Jywgc29ydGVkT3JkZXJzKTtcclxuICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgc2V0T3JkZXJzKFtdKTtcclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGZldGNoaW5nIG9yZGVyIGhpc3Rvcnk6JywgZXJyb3IpO1xyXG4gICAgfSBmaW5hbGx5IHtcclxuICAgICAgc2V0TG9hZGluZyhmYWxzZSk7XHJcbiAgICB9XHJcbiAgfSwgW3Rva2VuXSk7XHJcblxyXG4gIGNvbnN0IGZldGNoT3JkZXJEZXRhaWxzID0gdXNlQ2FsbGJhY2soYXN5bmMgKG9yZGVySWQ6IG51bWJlcikgPT4ge1xyXG4gICAgc2V0TG9hZGluZ0RldGFpbHModHJ1ZSk7XHJcbiAgICB0cnkge1xyXG4gICAgICAvLyBHZXQgSldUIHRva2VuIGZyb20gdXNlciBjb250ZXh0XHJcbiAgICAgIGNvbnN0IGF1dGhIZWFkZXJzOiBSZWNvcmQ8c3RyaW5nLCBzdHJpbmc+ID0ge1xyXG4gICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXHJcbiAgICAgICAgJ0FjY2VwdCc6ICdhcHBsaWNhdGlvbi9qc29uJ1xyXG4gICAgICB9O1xyXG5cclxuICAgICAgLy8gQWRkIEpXVCB0b2tlbiB0byBoZWFkZXJzIGlmIGF2YWlsYWJsZVxyXG4gICAgICBpZiAodG9rZW4pIHtcclxuICAgICAgICBhdXRoSGVhZGVyc1snQXV0aG9yaXphdGlvbiddID0gYEJlYXJlciAke3Rva2VufWA7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goJy9hcGkvb3JkZXJzL2RldGFpbHMnLCB7XHJcbiAgICAgICAgbWV0aG9kOiAnUE9TVCcsXHJcbiAgICAgICAgaGVhZGVyczogYXV0aEhlYWRlcnMsXHJcbiAgICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkoe1xyXG4gICAgICAgICAgcmVxdWVzdFBhcmFtZXRlcnM6IHtcclxuICAgICAgICAgICAgT3JkZXJJZDogb3JkZXJJZCxcclxuICAgICAgICAgICAgLy8gVXNlcklkIHJlbW92ZWQgLSB3aWxsIGJlIGF1dG8taW5qZWN0ZWQgZnJvbSBKV1QgdG9rZW5cclxuICAgICAgICAgICAgcmVjb3JkVmFsdWVKc29uOiBcIltdXCJcclxuICAgICAgICAgIH1cclxuICAgICAgICB9KVxyXG4gICAgICB9KTtcclxuXHJcbiAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XHJcbiAgICAgIGlmIChkYXRhICYmIGRhdGEuZGF0YSkge1xyXG4gICAgICAgIGNvbnN0IHBhcnNlZERhdGEgPSBKU09OLnBhcnNlKGRhdGEuZGF0YSk7XHJcbiAgICAgICAgaWYgKEFycmF5LmlzQXJyYXkocGFyc2VkRGF0YSkpIHtcclxuICAgICAgICAgIHNldE9yZGVyRGV0YWlscyhwYXJzZWREYXRhKTtcclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGZldGNoaW5nIG9yZGVyIGRldGFpbHM6JywgZXJyb3IpO1xyXG4gICAgfSBmaW5hbGx5IHtcclxuICAgICAgc2V0TG9hZGluZ0RldGFpbHMoZmFsc2UpO1xyXG4gICAgfVxyXG4gIH0sIFt0b2tlbl0pO1xyXG5cclxuICAvLyBGZXRjaCBvcmRlciBoaXN0b3J5XHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIGNvbnN0IHVzZXJJZCA9IHVzZXI/LlVzZXJJRCB8fCB1c2VyPy5Vc2VySWQ7XHJcbiAgICBpZiAoaXNMb2dnZWRJbiAmJiB1c2VySWQpIHtcclxuICAgICAgZmV0Y2hPcmRlckhpc3RvcnkoKTtcclxuICAgIH1cclxuICB9LCBbaXNMb2dnZWRJbiwgdXNlciwgZmV0Y2hPcmRlckhpc3RvcnldKTtcclxuXHJcbiAgLy8gUmVkaXJlY3QgdG8gbG9naW4gaWYgbm90IGF1dGhlbnRpY2F0ZWQgKG9ubHkgYWZ0ZXIgbG9hZGluZyBpcyBjb21wbGV0ZSlcclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgaWYgKCFpc0xvYWRpbmcgJiYgIWlzTG9nZ2VkSW4pIHtcclxuICAgICAgd2luZG93LmxvY2F0aW9uLmhyZWYgPSAnL2xvZ2luP3JlZGlyZWN0PS9vcmRlcnMnO1xyXG4gICAgfVxyXG4gIH0sIFtpc0xvYWRpbmcsIGlzTG9nZ2VkSW5dKTtcclxuXHJcbiAgLy8gRWFybHkgcmV0dXJuIGZvciBsb2FkaW5nIHN0YXRlIHRvIHByZXZlbnQgcmFjZSBjb25kaXRpb25zXHJcbiAgaWYgKGlzTG9hZGluZykge1xyXG4gICAgcmV0dXJuIChcclxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJtaW4taC1zY3JlZW4gZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFuaW1hdGUtc3BpbiByb3VuZGVkLWZ1bGwgaC04IHctOCBib3JkZXItYi0yIGJvcmRlci1wcmltYXJ5IG14LWF1dG8gbWItNFwiPjwvZGl2PlxyXG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1sZyB0ZXh0LW11dGVkLWZvcmVncm91bmRcIj5Mb2FkaW5nLi4uPC9wPlxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICA8L2Rpdj5cclxuICAgICk7XHJcbiAgfVxyXG5cclxuICAvLyBFYXJseSByZXR1cm4gZm9yIHVuYXV0aGVudGljYXRlZCB1c2Vyc1xyXG4gIGlmICghaXNMb2dnZWRJbikge1xyXG4gICAgcmV0dXJuIChcclxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJtaW4taC1zY3JlZW4gZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XHJcbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWxnIHRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiPlxyXG4gICAgICAgICAgICBSZWRpcmVjdGluZyB0byBsb2dpbi4uLlxyXG4gICAgICAgICAgPC9wPlxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICA8L2Rpdj5cclxuICAgICk7XHJcbiAgfVxyXG5cclxuXHJcblxyXG4gIC8vIE1vY2sgb3JkZXJzIGRhdGEgKGZhbGxiYWNrKVxyXG4gIGNvbnN0IG1vY2tPcmRlcnMgPSBbXHJcbiAgICB7XHJcbiAgICAgIGlkOiAnT1JELTIwMjMtMTAwMScsXHJcbiAgICAgIGRhdGU6ICcyMDIzLTEyLTE1JyxcclxuICAgICAgdG90YWw6IDEyOS45OSxcclxuICAgICAgU3RhdHVzSUQ6IDMsXHJcbiAgICAgIGl0ZW1zOiBbXHJcbiAgICAgICAgeyBuYW1lOiAnTWVkaWNhbCBUZXJtaW5vbG9neSBCb29rJywgUHJvZHVjdE5hbWU6ICdNZWRpY2FsIFRlcm1pbm9sb2d5IEJvb2snLCBxdWFudGl0eTogMSwgcHJpY2U6IDQ5Ljk5IH0sXHJcbiAgICAgICAgeyBuYW1lOiAnQW5hdG9teSBBdGxhcycsIFByb2R1Y3ROYW1lOiAnQW5hdG9teSBBdGxhcycsIHF1YW50aXR5OiAxLCBwcmljZTogNzkuOTkgfVxyXG4gICAgICBdXHJcbiAgICB9LFxyXG4gICAge1xyXG4gICAgICBpZDogJ09SRC0yMDIzLTA4OTInLFxyXG4gICAgICBkYXRlOiAnMjAyMy0xMS0yOCcsXHJcbiAgICAgIHRvdGFsOiAxOTkuOTksXHJcbiAgICAgIFN0YXR1c0lEOiAyLFxyXG4gICAgICBpdGVtczogW1xyXG4gICAgICAgIHsgbmFtZTogJ0NsaW5pY2FsIE1lZGljaW5lIENvdXJzZScsIFByb2R1Y3ROYW1lOiAnQ2xpbmljYWwgTWVkaWNpbmUgQ291cnNlJywgcXVhbnRpdHk6IDEsIHByaWNlOiAxOTkuOTkgfVxyXG4gICAgICBdXHJcbiAgICB9LFxyXG4gICAge1xyXG4gICAgICBpZDogJ09SRC0yMDIzLTA3NjUnLFxyXG4gICAgICBkYXRlOiAnMjAyMy0xMC0wNScsXHJcbiAgICAgIHRvdGFsOiA0NS45OSxcclxuICAgICAgU3RhdHVzSUQ6IDYsXHJcbiAgICAgIGl0ZW1zOiBbXHJcbiAgICAgICAgeyBuYW1lOiAnUGhhcm1hY29sb2d5IEZsYXNoY2FyZHMnLCBQcm9kdWN0TmFtZTogJ1BoYXJtYWNvbG9neSBGbGFzaGNhcmRzJywgcXVhbnRpdHk6IDEsIHByaWNlOiA0NS45OSB9XHJcbiAgICAgIF1cclxuICAgIH1cclxuICBdO1xyXG5cclxuICAvLyBGaWx0ZXIgYW5kIHNvcnQgb3JkZXJzXHJcbiAgY29uc3QgZGlzcGxheU9yZGVycyA9IGxvYWRpbmcgPyBbXSA6IG9yZGVycztcclxuICBcclxuICAvLyBGaWx0ZXIgYnkgdGFiXHJcbiAgY29uc3QgdGFiRmlsdGVyZWRPcmRlcnMgPSBhY3RpdmVUYWIgPT09ICdhbGwnIFxyXG4gICAgPyBkaXNwbGF5T3JkZXJzIFxyXG4gICAgOiBkaXNwbGF5T3JkZXJzLmZpbHRlcihvcmRlciA9PiB7XHJcbiAgICAgICAgY29uc3Qgc3RhdHVzSWQgPSBvcmRlci5TdGF0dXNJRCB8fCBvcmRlci5PcmRlclN0YXR1c0lkIHx8IG9yZGVyLlN0YXRlSWQgfHwgMTtcclxuICAgICAgICByZXR1cm4gc3RhdHVzSWQudG9TdHJpbmcoKSA9PT0gYWN0aXZlVGFiO1xyXG4gICAgICB9KTtcclxuXHJcbiAgLy8gRmlsdGVyIGJ5IHNlYXJjaCBxdWVyeVxyXG4gIGNvbnN0IHNlYXJjaEZpbHRlcmVkT3JkZXJzID0gc2VhcmNoUXVlcnkudHJpbSgpID09PSAnJyBcclxuICAgID8gdGFiRmlsdGVyZWRPcmRlcnMgXHJcbiAgICA6IHRhYkZpbHRlcmVkT3JkZXJzLmZpbHRlcihvcmRlciA9PiB7XHJcbiAgICAgICAgY29uc3Qgc2VhcmNoVGVybSA9IHNlYXJjaFF1ZXJ5LnRvTG93ZXJDYXNlKCk7XHJcbiAgICAgICAgY29uc3Qgb3JkZXJOdW1iZXIgPSAob3JkZXIuT3JkZXJOdW1iZXIgfHwgYE9SIyR7U3RyaW5nKG9yZGVyLk9yZGVySWQgfHwgb3JkZXIuT3JkZXJJRCkucGFkU3RhcnQoOCwgJzAnKX1gKS50b0xvd2VyQ2FzZSgpO1xyXG4gICAgICAgIGNvbnN0IHByb2R1Y3ROYW1lcyA9IChvcmRlci5pdGVtcyB8fCBvcmRlci5PcmRlckl0ZW1zIHx8IFtdKVxyXG4gICAgICAgICAgLm1hcCgoaXRlbTogYW55KSA9PiAoaXRlbS5uYW1lIHx8IGl0ZW0uUHJvZHVjdE5hbWUgfHwgaXRlbS5JdGVtTmFtZSB8fCAnJykudG9Mb3dlckNhc2UoKSlcclxuICAgICAgICAgIC5qb2luKCcgJyk7XHJcbiAgICAgICAgXHJcbiAgICAgICAgcmV0dXJuIG9yZGVyTnVtYmVyLmluY2x1ZGVzKHNlYXJjaFRlcm0pIHx8IHByb2R1Y3ROYW1lcy5pbmNsdWRlcyhzZWFyY2hUZXJtKTtcclxuICAgICAgfSk7XHJcblxyXG4gIC8vIFNvcnQgb3JkZXJzXHJcbiAgY29uc3QgZmlsdGVyZWRPcmRlcnMgPSBbLi4uc2VhcmNoRmlsdGVyZWRPcmRlcnNdLnNvcnQoKGEsIGIpID0+IHtcclxuICAgIHN3aXRjaCAoc29ydEJ5KSB7XHJcbiAgICAgIGNhc2UgJ2RhdGUtZGVzYyc6XHJcbiAgICAgICAgcmV0dXJuIG5ldyBEYXRlKGIuT3JkZXJEYXRlIHx8IDApLmdldFRpbWUoKSAtIG5ldyBEYXRlKGEuT3JkZXJEYXRlIHx8IDApLmdldFRpbWUoKTtcclxuICAgICAgY2FzZSAnZGF0ZS1hc2MnOlxyXG4gICAgICAgIHJldHVybiBuZXcgRGF0ZShhLk9yZGVyRGF0ZSB8fCAwKS5nZXRUaW1lKCkgLSBuZXcgRGF0ZShiLk9yZGVyRGF0ZSB8fCAwKS5nZXRUaW1lKCk7XHJcbiAgICAgIGNhc2UgJ3RvdGFsLWRlc2MnOlxyXG4gICAgICAgIHJldHVybiAoYi50b3RhbCB8fCBiLk9yZGVyVG90YWwgfHwgYi5Ub3RhbEFtb3VudCB8fCAwKSAtIChhLnRvdGFsIHx8IGEuT3JkZXJUb3RhbCB8fCBhLlRvdGFsQW1vdW50IHx8IDApO1xyXG4gICAgICBjYXNlICd0b3RhbC1hc2MnOlxyXG4gICAgICAgIHJldHVybiAoYS50b3RhbCB8fCBhLk9yZGVyVG90YWwgfHwgYS5Ub3RhbEFtb3VudCB8fCAwKSAtIChiLnRvdGFsIHx8IGIuT3JkZXJUb3RhbCB8fCBiLlRvdGFsQW1vdW50IHx8IDApO1xyXG4gICAgICBkZWZhdWx0OlxyXG4gICAgICAgIHJldHVybiAwO1xyXG4gICAgfVxyXG4gIH0pO1xyXG5cclxuICAvLyBDcmVhdGUgc2VjdXJlIHF1ZXJ5IHBhcmFtZXRlcnMgZm9yIG9yZGVyIGRldGFpbHNcclxuICBjb25zdCBjcmVhdGVTZWN1cmVPcmRlckxpbmsgPSAob3JkZXI6IGFueSkgPT4ge1xyXG4gICAgY29uc3Qgb3JkZXJJZCA9IG9yZGVyLk9yZGVySWQgfHwgb3JkZXIuT3JkZXJJRCB8fCBvcmRlci5pZDtcclxuICAgIGNvbnN0IGVuY3J5cHRlZE9yZGVySWQgPSBlbmNyeXB0T3JkZXJJZChvcmRlcklkKTtcclxuICAgIFxyXG4gICAgLy8gRW5jcnlwdCBzZW5zaXRpdmUgZGF0YSBmb3IgcXVlcnkgcGFyYW1ldGVyc1xyXG4gICAgY29uc3Qgb3JkZXJUb3RhbCA9IChvcmRlci50b3RhbCB8fCBvcmRlci5PcmRlclRvdGFsIHx8IG9yZGVyLlRvdGFsQW1vdW50IHx8IDApLnRvRml4ZWQoMik7XHJcbiAgICBjb25zdCBvcmRlckRhdGUgPSAoKCkgPT4ge1xyXG4gICAgICBjb25zdCBkYXRlU3RyID0gb3JkZXIuT3JkZXJEYXRlVVRDIHx8IG9yZGVyLmRhdGUgfHwgb3JkZXIuT3JkZXJEYXRlIHx8IG9yZGVyLkNyZWF0ZWRPbjtcclxuICAgICAgaWYgKGRhdGVTdHIpIHtcclxuICAgICAgICB0cnkge1xyXG4gICAgICAgICAgY29uc3QgZGF0ZSA9IG5ldyBEYXRlKGRhdGVTdHIpO1xyXG4gICAgICAgICAgcmV0dXJuIGRhdGUudG9Mb2NhbGVEYXRlU3RyaW5nKCdlbi1VUycsIHtcclxuICAgICAgICAgICAgeWVhcjogJ251bWVyaWMnLFxyXG4gICAgICAgICAgICBtb250aDogJ2xvbmcnLFxyXG4gICAgICAgICAgICBkYXk6ICdudW1lcmljJ1xyXG4gICAgICAgICAgfSk7XHJcbiAgICAgICAgfSBjYXRjaCAoZSkge1xyXG4gICAgICAgICAgcmV0dXJuICdOL0EnO1xyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG4gICAgICByZXR1cm4gJ04vQSc7XHJcbiAgICB9KSgpO1xyXG4gICAgXHJcbiAgICAvLyBFbmNyeXB0IHRoZSBxdWVyeSBwYXJhbWV0ZXJzXHJcbiAgICBjb25zdCBlbmNyeXB0ZWRUb3RhbCA9IGVuY3J5cHRWYWx1ZShvcmRlclRvdGFsKTtcclxuICAgIGNvbnN0IGVuY3J5cHRlZERhdGUgPSBlbmNyeXB0VmFsdWUob3JkZXJEYXRlKTtcclxuICAgIFxyXG4gICAgcmV0dXJuIGAvb3JkZXJzLyR7ZW5jcnlwdGVkT3JkZXJJZH0/dD0ke2VuY3J5cHRlZFRvdGFsfSZkPSR7ZW5jcnlwdGVkRGF0ZX1gO1xyXG4gIH07XHJcblxyXG4gIC8vIFN0YXR1cyBtYXBwaW5nIGJhc2VkIG9uIFN0YXR1c0lEXHJcbiAgY29uc3QgZ2V0U3RhdHVzSW5mbyA9IChzdGF0dXNJZDogbnVtYmVyIHwgc3RyaW5nKSA9PiB7XHJcbiAgICBjb25zdCBpZCA9IHBhcnNlSW50KHN0YXR1c0lkPy50b1N0cmluZygpIHx8ICcxJyk7XHJcbiAgICBzd2l0Y2goaWQpIHtcclxuICAgICAgY2FzZSAxOlxyXG4gICAgICAgIHJldHVybiB7IG5hbWU6ICdBY3RpdmUnLCBpY29uOiA8UGFja2FnZSBjbGFzc05hbWU9XCJoLTUgdy01IHRleHQtYmx1ZS01MDBcIiAvPiwgY29sb3I6ICdiZy1ibHVlLTEwMCB0ZXh0LWJsdWUtODAwJyB9O1xyXG4gICAgICBjYXNlIDI6XHJcbiAgICAgICAgcmV0dXJuIHsgbmFtZTogJ0luIFByb2dyZXNzJywgaWNvbjogPFRydWNrIGNsYXNzTmFtZT1cImgtNSB3LTUgdGV4dC1vcmFuZ2UtNTAwXCIgLz4sIGNvbG9yOiAnYmctb3JhbmdlLTEwMCB0ZXh0LW9yYW5nZS04MDAnIH07XHJcbiAgICAgIGNhc2UgMzpcclxuICAgICAgICByZXR1cm4geyBuYW1lOiAnQ29tcGxldGVkJywgaWNvbjogPENoZWNrQ2lyY2xlIGNsYXNzTmFtZT1cImgtNSB3LTUgdGV4dC1ncmVlbi01MDBcIiAvPiwgY29sb3I6ICdiZy1ncmVlbi0xMDAgdGV4dC1ncmVlbi04MDAnIH07XHJcbiAgICAgIGNhc2UgNDpcclxuICAgICAgICByZXR1cm4geyBuYW1lOiAnUmV0dXJuZWQnLCBpY29uOiA8UmVmcmVzaEN3IGNsYXNzTmFtZT1cImgtNSB3LTUgdGV4dC1wdXJwbGUtNTAwXCIgLz4sIGNvbG9yOiAnYmctcHVycGxlLTEwMCB0ZXh0LXB1cnBsZS04MDAnIH07XHJcbiAgICAgIGNhc2UgNTpcclxuICAgICAgICByZXR1cm4geyBuYW1lOiAnUmVmdW5kZWQnLCBpY29uOiA8UmVmcmVzaEN3IGNsYXNzTmFtZT1cImgtNSB3LTUgdGV4dC1pbmRpZ28tNTAwXCIgLz4sIGNvbG9yOiAnYmctaW5kaWdvLTEwMCB0ZXh0LWluZGlnby04MDAnIH07XHJcbiAgICAgIGNhc2UgNjpcclxuICAgICAgICByZXR1cm4geyBuYW1lOiAnQ2FuY2VsbGVkJywgaWNvbjogPEFsZXJ0Q2lyY2xlIGNsYXNzTmFtZT1cImgtNSB3LTUgdGV4dC1yZWQtNTAwXCIgLz4sIGNvbG9yOiAnYmctcmVkLTEwMCB0ZXh0LXJlZC04MDAnIH07XHJcbiAgICAgIGRlZmF1bHQ6XHJcbiAgICAgICAgcmV0dXJuIHsgbmFtZTogJ1Vua25vd24nLCBpY29uOiA8UGFja2FnZSBjbGFzc05hbWU9XCJoLTUgdy01IHRleHQtZ3JheS01MDBcIiAvPiwgY29sb3I6ICdiZy1ncmF5LTEwMCB0ZXh0LWdyYXktODAwJyB9O1xyXG4gICAgfVxyXG4gIH07XHJcblxyXG5cclxuXHJcbiAgcmV0dXJuIChcclxuICAgIDxkaXYgY2xhc3NOYW1lPVwiY29udGFpbmVyIG14LWF1dG8gcHktOCBweC00XCI+XHJcbiAgICAgIHsvKiBCcmVhZGNydW1iICovfVxyXG4gICAgICA8QnJlYWRjcnVtYiBjbGFzc05hbWU9XCJtYi02XCI+XHJcbiAgICAgICAgPEJyZWFkY3J1bWJMaXN0PlxyXG4gICAgICAgICAgPEJyZWFkY3J1bWJJdGVtPlxyXG4gICAgICAgICAgICA8QnJlYWRjcnVtYkxpbmsgYXNDaGlsZD5cclxuICAgICAgICAgICAgICA8TGluayBocmVmPVwiL1wiPnt0KCdob21lJyl9PC9MaW5rPlxyXG4gICAgICAgICAgICA8L0JyZWFkY3J1bWJMaW5rPlxyXG4gICAgICAgICAgPC9CcmVhZGNydW1iSXRlbT5cclxuICAgICAgICAgIDxCcmVhZGNydW1iU2VwYXJhdG9yIC8+XHJcbiAgICAgICAgICA8QnJlYWRjcnVtYkl0ZW0+XHJcbiAgICAgICAgICAgIDxCcmVhZGNydW1iUGFnZT57dCgnb3JkZXJzJyl9PC9CcmVhZGNydW1iUGFnZT5cclxuICAgICAgICAgIDwvQnJlYWRjcnVtYkl0ZW0+XHJcbiAgICAgICAgPC9CcmVhZGNydW1iTGlzdD5cclxuICAgICAgPC9CcmVhZGNydW1iPlxyXG5cclxuICAgICAgey8qIFBhZ2UgQ29udGVudCAqL31cclxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYXgtdy02eGwgbXgtYXV0b1wiPlxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW4gaXRlbXMtY2VudGVyIG1iLTZcIj5cclxuICAgICAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LTN4bCBmb250LWJvbGRcIj57dCgnb3JkZXJzJyl9PC9oMT5cclxuICAgICAgICAgIDxCdXR0b25cclxuICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVcIlxyXG4gICAgICAgICAgICBzaXplPVwic21cIlxyXG4gICAgICAgICAgICBvbkNsaWNrPXtmZXRjaE9yZGVySGlzdG9yeX1cclxuICAgICAgICAgICAgZGlzYWJsZWQ9e2xvYWRpbmd9XHJcbiAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCJcclxuICAgICAgICAgID5cclxuICAgICAgICAgICAgPFJlZnJlc2hDdyBjbGFzc05hbWU9e2BoLTQgdy00ICR7bG9hZGluZyA/ICdhbmltYXRlLXNwaW4nIDogJyd9YH0gLz5cclxuICAgICAgICAgICAge2xvYWRpbmcgPyAnTG9hZGluZy4uLicgOiAnUmVmcmVzaCd9XHJcbiAgICAgICAgICA8L0J1dHRvbj5cclxuICAgICAgICA8L2Rpdj5cclxuICAgICAgICBcclxuICAgICAgICB7LyogT3JkZXIgVGFicyAqL31cclxuICAgICAgICA8VGFicyBkZWZhdWx0VmFsdWU9XCJhbGxcIiBjbGFzc05hbWU9XCJtYi02XCIgb25WYWx1ZUNoYW5nZT17c2V0QWN0aXZlVGFifT5cclxuICAgICAgICAgIDxUYWJzTGlzdCBjbGFzc05hbWU9XCJncmlkIHctZnVsbCBncmlkLWNvbHMtMiBzbTpncmlkLWNvbHMtMyBsZzpncmlkLWNvbHMtNSBtYi02IGdhcC0yIGJnLXRyYW5zcGFyZW50IHAtMCBoLWF1dG9cIj5cclxuICAgICAgICAgICAgPFRhYnNUcmlnZ2VyIFxyXG4gICAgICAgICAgICAgIHZhbHVlPVwiYWxsXCJcclxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJyb3VuZGVkLWxnIHB4LTQgcHktMyB0ZXh0LWJhc2UgZm9udC1zZW1pYm9sZCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDAgYm9yZGVyLTIgYm9yZGVyLXRyYW5zcGFyZW50IGRhdGEtW3N0YXRlPWluYWN0aXZlXTpiZy1ncmF5LTMwMCBkYXRhLVtzdGF0ZT1pbmFjdGl2ZV06dGV4dC1ncmF5LTcwMCBkYXRhLVtzdGF0ZT1pbmFjdGl2ZV06c2NhbGUtMTAwIGhvdmVyOmJnLWdyYXktNDAwIGhvdmVyOnRleHQtd2hpdGUgaG92ZXI6c2NhbGUtMTAyXCJcclxuICAgICAgICAgICAgICBzdHlsZT17e1xyXG4gICAgICAgICAgICAgICAgYmFja2dyb3VuZENvbG9yOiBhY3RpdmVUYWIgPT09ICdhbGwnID8gcHJpbWFyeUNvbG9yIDogJ3JnYigyMDkgMjEzIDIxOSknLFxyXG4gICAgICAgICAgICAgICAgY29sb3I6IGFjdGl2ZVRhYiA9PT0gJ2FsbCcgPyBwcmltYXJ5VGV4dENvbG9yIDogJ3JnYig1NSA2NSA4MSknLFxyXG4gICAgICAgICAgICAgICAgdHJhbnNmb3JtOiBhY3RpdmVUYWIgPT09ICdhbGwnID8gJ3NjYWxlKDEuMDUpJyA6ICdzY2FsZSgxKScsXHJcbiAgICAgICAgICAgICAgICBib3hTaGFkb3c6IGFjdGl2ZVRhYiA9PT0gJ2FsbCcgPyAnMCAxMHB4IDE1cHggLTNweCByZ2JhKDAsIDAsIDAsIDAuMSksIDAgNHB4IDZweCAtMnB4IHJnYmEoMCwgMCwgMCwgMC4wNSknIDogJ25vbmUnLFxyXG4gICAgICAgICAgICAgICAgYm9yZGVyQ29sb3I6IGFjdGl2ZVRhYiA9PT0gJ2FsbCcgPyBwcmltYXJ5Q29sb3IgOiAndHJhbnNwYXJlbnQnXHJcbiAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgIEFsbCBPcmRlcnNcclxuICAgICAgICAgICAgPC9UYWJzVHJpZ2dlcj5cclxuICAgICAgICAgICAgPFRhYnNUcmlnZ2VyIFxyXG4gICAgICAgICAgICAgIHZhbHVlPVwiMVwiXHJcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicm91bmRlZC1sZyBweC00IHB5LTMgdGV4dC1iYXNlIGZvbnQtc2VtaWJvbGQgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwIGJvcmRlci0yIGJvcmRlci10cmFuc3BhcmVudCBkYXRhLVtzdGF0ZT1pbmFjdGl2ZV06YmctZ3JheS0zMDAgZGF0YS1bc3RhdGU9aW5hY3RpdmVdOnRleHQtZ3JheS03MDAgZGF0YS1bc3RhdGU9aW5hY3RpdmVdOnNjYWxlLTEwMCBob3ZlcjpiZy1ncmF5LTQwMCBob3Zlcjp0ZXh0LXdoaXRlIGhvdmVyOnNjYWxlLTEwMlwiXHJcbiAgICAgICAgICAgICAgc3R5bGU9e3tcclxuICAgICAgICAgICAgICAgIGJhY2tncm91bmRDb2xvcjogYWN0aXZlVGFiID09PSAnMScgPyBwcmltYXJ5Q29sb3IgOiAncmdiKDIwOSAyMTMgMjE5KScsXHJcbiAgICAgICAgICAgICAgICBjb2xvcjogYWN0aXZlVGFiID09PSAnMScgPyBwcmltYXJ5VGV4dENvbG9yIDogJ3JnYig1NSA2NSA4MSknLFxyXG4gICAgICAgICAgICAgICAgdHJhbnNmb3JtOiBhY3RpdmVUYWIgPT09ICcxJyA/ICdzY2FsZSgxLjA1KScgOiAnc2NhbGUoMSknLFxyXG4gICAgICAgICAgICAgICAgYm94U2hhZG93OiBhY3RpdmVUYWIgPT09ICcxJyA/ICcwIDEwcHggMTVweCAtM3B4IHJnYmEoMCwgMCwgMCwgMC4xKSwgMCA0cHggNnB4IC0ycHggcmdiYSgwLCAwLCAwLCAwLjA1KScgOiAnbm9uZScsXHJcbiAgICAgICAgICAgICAgICBib3JkZXJDb2xvcjogYWN0aXZlVGFiID09PSAnMScgPyBwcmltYXJ5Q29sb3IgOiAndHJhbnNwYXJlbnQnXHJcbiAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgIEFjdGl2ZVxyXG4gICAgICAgICAgICA8L1RhYnNUcmlnZ2VyPlxyXG4gICAgICAgICAgICA8VGFic1RyaWdnZXIgXHJcbiAgICAgICAgICAgICAgdmFsdWU9XCIyXCJcclxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJyb3VuZGVkLWxnIHB4LTQgcHktMyB0ZXh0LWJhc2UgZm9udC1zZW1pYm9sZCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDAgYm9yZGVyLTIgYm9yZGVyLXRyYW5zcGFyZW50IGRhdGEtW3N0YXRlPWluYWN0aXZlXTpiZy1ncmF5LTMwMCBkYXRhLVtzdGF0ZT1pbmFjdGl2ZV06dGV4dC1ncmF5LTcwMCBkYXRhLVtzdGF0ZT1pbmFjdGl2ZV06c2NhbGUtMTAwIGhvdmVyOmJnLWdyYXktNDAwIGhvdmVyOnRleHQtd2hpdGUgaG92ZXI6c2NhbGUtMTAyXCJcclxuICAgICAgICAgICAgICBzdHlsZT17e1xyXG4gICAgICAgICAgICAgICAgYmFja2dyb3VuZENvbG9yOiBhY3RpdmVUYWIgPT09ICcyJyA/IHByaW1hcnlDb2xvciA6ICdyZ2IoMjA5IDIxMyAyMTkpJyxcclxuICAgICAgICAgICAgICAgIGNvbG9yOiBhY3RpdmVUYWIgPT09ICcyJyA/IHByaW1hcnlUZXh0Q29sb3IgOiAncmdiKDU1IDY1IDgxKScsXHJcbiAgICAgICAgICAgICAgICB0cmFuc2Zvcm06IGFjdGl2ZVRhYiA9PT0gJzInID8gJ3NjYWxlKDEuMDUpJyA6ICdzY2FsZSgxKScsXHJcbiAgICAgICAgICAgICAgICBib3hTaGFkb3c6IGFjdGl2ZVRhYiA9PT0gJzInID8gJzAgMTBweCAxNXB4IC0zcHggcmdiYSgwLCAwLCAwLCAwLjEpLCAwIDRweCA2cHggLTJweCByZ2JhKDAsIDAsIDAsIDAuMDUpJyA6ICdub25lJyxcclxuICAgICAgICAgICAgICAgIGJvcmRlckNvbG9yOiBhY3RpdmVUYWIgPT09ICcyJyA/IHByaW1hcnlDb2xvciA6ICd0cmFuc3BhcmVudCdcclxuICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgSW4gUHJvZ3Jlc3NcclxuICAgICAgICAgICAgPC9UYWJzVHJpZ2dlcj5cclxuICAgICAgICAgICAgPFRhYnNUcmlnZ2VyIFxyXG4gICAgICAgICAgICAgIHZhbHVlPVwiM1wiXHJcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicm91bmRlZC1sZyBweC00IHB5LTMgdGV4dC1iYXNlIGZvbnQtc2VtaWJvbGQgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwIGJvcmRlci0yIGJvcmRlci10cmFuc3BhcmVudCBkYXRhLVtzdGF0ZT1pbmFjdGl2ZV06YmctZ3JheS0zMDAgZGF0YS1bc3RhdGU9aW5hY3RpdmVdOnRleHQtZ3JheS03MDAgZGF0YS1bc3RhdGU9aW5hY3RpdmVdOnNjYWxlLTEwMCBob3ZlcjpiZy1ncmF5LTQwMCBob3Zlcjp0ZXh0LXdoaXRlIGhvdmVyOnNjYWxlLTEwMlwiXHJcbiAgICAgICAgICAgICAgc3R5bGU9e3tcclxuICAgICAgICAgICAgICAgIGJhY2tncm91bmRDb2xvcjogYWN0aXZlVGFiID09PSAnMycgPyBwcmltYXJ5Q29sb3IgOiAncmdiKDIwOSAyMTMgMjE5KScsXHJcbiAgICAgICAgICAgICAgICBjb2xvcjogYWN0aXZlVGFiID09PSAnMycgPyBwcmltYXJ5VGV4dENvbG9yIDogJ3JnYig1NSA2NSA4MSknLFxyXG4gICAgICAgICAgICAgICAgdHJhbnNmb3JtOiBhY3RpdmVUYWIgPT09ICczJyA/ICdzY2FsZSgxLjA1KScgOiAnc2NhbGUoMSknLFxyXG4gICAgICAgICAgICAgICAgYm94U2hhZG93OiBhY3RpdmVUYWIgPT09ICczJyA/ICcwIDEwcHggMTVweCAtM3B4IHJnYmEoMCwgMCwgMCwgMC4xKSwgMCA0cHggNnB4IC0ycHggcmdiYSgwLCAwLCAwLCAwLjA1KScgOiAnbm9uZScsXHJcbiAgICAgICAgICAgICAgICBib3JkZXJDb2xvcjogYWN0aXZlVGFiID09PSAnMycgPyBwcmltYXJ5Q29sb3IgOiAndHJhbnNwYXJlbnQnXHJcbiAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgIENvbXBsZXRlZFxyXG4gICAgICAgICAgICA8L1RhYnNUcmlnZ2VyPlxyXG4gICAgICAgICAgICA8VGFic1RyaWdnZXIgXHJcbiAgICAgICAgICAgICAgdmFsdWU9XCI2XCJcclxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJyb3VuZGVkLWxnIHB4LTQgcHktMyB0ZXh0LWJhc2UgZm9udC1zZW1pYm9sZCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDAgYm9yZGVyLTIgYm9yZGVyLXRyYW5zcGFyZW50IGRhdGEtW3N0YXRlPWluYWN0aXZlXTpiZy1ncmF5LTMwMCBkYXRhLVtzdGF0ZT1pbmFjdGl2ZV06dGV4dC1ncmF5LTcwMCBkYXRhLVtzdGF0ZT1pbmFjdGl2ZV06c2NhbGUtMTAwIGhvdmVyOmJnLWdyYXktNDAwIGhvdmVyOnRleHQtd2hpdGUgaG92ZXI6c2NhbGUtMTAyXCJcclxuICAgICAgICAgICAgICBzdHlsZT17e1xyXG4gICAgICAgICAgICAgICAgYmFja2dyb3VuZENvbG9yOiBhY3RpdmVUYWIgPT09ICc2JyA/IHByaW1hcnlDb2xvciA6ICdyZ2IoMjA5IDIxMyAyMTkpJyxcclxuICAgICAgICAgICAgICAgIGNvbG9yOiBhY3RpdmVUYWIgPT09ICc2JyA/IHByaW1hcnlUZXh0Q29sb3IgOiAncmdiKDU1IDY1IDgxKScsXHJcbiAgICAgICAgICAgICAgICB0cmFuc2Zvcm06IGFjdGl2ZVRhYiA9PT0gJzYnID8gJ3NjYWxlKDEuMDUpJyA6ICdzY2FsZSgxKScsXHJcbiAgICAgICAgICAgICAgICBib3hTaGFkb3c6IGFjdGl2ZVRhYiA9PT0gJzYnID8gJzAgMTBweCAxNXB4IC0zcHggcmdiYSgwLCAwLCAwLCAwLjEpLCAwIDRweCA2cHggLTJweCByZ2JhKDAsIDAsIDAsIDAuMDUpJyA6ICdub25lJyxcclxuICAgICAgICAgICAgICAgIGJvcmRlckNvbG9yOiBhY3RpdmVUYWIgPT09ICc2JyA/IHByaW1hcnlDb2xvciA6ICd0cmFuc3BhcmVudCdcclxuICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgQ2FuY2VsbGVkXHJcbiAgICAgICAgICAgIDwvVGFic1RyaWdnZXI+XHJcbiAgICAgICAgICA8L1RhYnNMaXN0PlxyXG4gICAgICAgIDwvVGFicz5cclxuICAgICAgICBcclxuICAgICAgICB7LyogU2VhcmNoIGFuZCBGaWx0ZXIgKi99XHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIG1kOmZsZXgtcm93IGp1c3RpZnktYmV0d2VlbiBpdGVtcy1zdGFydCBtZDppdGVtcy1jZW50ZXIgZ2FwLTQgbWItNlwiPlxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZSB3LWZ1bGwgbWQ6dy02NFwiPlxyXG4gICAgICAgICAgICA8U2VhcmNoIGNsYXNzTmFtZT1cImFic29sdXRlIGxlZnQtMyB0b3AtMS8yIHRyYW5zZm9ybSAtdHJhbnNsYXRlLXktMS8yIGgtNCB3LTQgdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCIgLz5cclxuICAgICAgICAgICAgPGlucHV0IFxyXG4gICAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCIgXHJcbiAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJTZWFyY2ggb3JkZXJzLi4uXCIgXHJcbiAgICAgICAgICAgICAgdmFsdWU9e3NlYXJjaFF1ZXJ5fVxyXG4gICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0U2VhcmNoUXVlcnkoZS50YXJnZXQudmFsdWUpfVxyXG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBwbC0xMCBwci00IHB5LTIgYm9yZGVyIGJvcmRlci1pbnB1dCByb3VuZGVkLW1kIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1wcmltYXJ5IGZvY3VzOmJvcmRlci10cmFuc3BhcmVudFwiXHJcbiAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIFxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxyXG4gICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiPlNvcnQgYnk6PC9zcGFuPlxyXG4gICAgICAgICAgICA8c2VsZWN0IFxyXG4gICAgICAgICAgICAgIHZhbHVlPXtzb3J0Qnl9XHJcbiAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRTb3J0QnkoZS50YXJnZXQudmFsdWUpfVxyXG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJvcmRlciBib3JkZXItaW5wdXQgcm91bmRlZC1tZCBweC0zIHB5LTEgdGV4dC1zbSBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctcHJpbWFyeSBmb2N1czpib3JkZXItdHJhbnNwYXJlbnRcIlxyXG4gICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cImRhdGUtZGVzY1wiPkRhdGUgKE5ld2VzdCk8L29wdGlvbj5cclxuICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiZGF0ZS1hc2NcIj5EYXRlIChPbGRlc3QpPC9vcHRpb24+XHJcbiAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cInRvdGFsLWRlc2NcIj5BbW91bnQgKEhpZ2ggdG8gTG93KTwvb3B0aW9uPlxyXG4gICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJ0b3RhbC1hc2NcIj5BbW91bnQgKExvdyB0byBIaWdoKTwvb3B0aW9uPlxyXG4gICAgICAgICAgICA8L3NlbGVjdD5cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICAgIFxyXG4gICAgICAgIHsvKiBPcmRlcnMgTGlzdCAqL31cclxuICAgICAgICB7bG9hZGluZyA/IChcclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XHJcbiAgICAgICAgICAgIHsvKiBMb2FkaW5nIFNrZWxldG9uICovfVxyXG4gICAgICAgICAgICB7Wy4uLkFycmF5KDMpXS5tYXAoKF8sIGluZGV4KSA9PiAoXHJcbiAgICAgICAgICAgICAgPENhcmQga2V5PXtpbmRleH0gY2xhc3NOYW1lPVwib3ZlcmZsb3ctaGlkZGVuXCI+XHJcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJvcmRlci1iIGJvcmRlci1ib3JkZXIgcC00XCI+XHJcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBtZDpmbGV4LXJvdyBqdXN0aWZ5LWJldHdlZW4gaXRlbXMtc3RhcnQgbWQ6aXRlbXMtY2VudGVyIGdhcC00XCI+XHJcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTFcIj5cclxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTIgbWItMlwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8U2tlbGV0b24gY2xhc3NOYW1lPVwiaC01IHctMzJcIiAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8U2tlbGV0b24gY2xhc3NOYW1lPVwiaC02IHctMjAgcm91bmRlZC1mdWxsXCIgLz5cclxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtNCB0ZXh0LXNtIHRleHQtbXV0ZWQtZm9yZWdyb3VuZCBtYi0zXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxTa2VsZXRvbiBjbGFzc05hbWU9XCJoLTQgdy0yNFwiIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxTa2VsZXRvbiBjbGFzc05hbWU9XCJoLTQgdy0xNlwiIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxTa2VsZXRvbiBjbGFzc05hbWU9XCJoLTQgdy0yMFwiIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgIDxTa2VsZXRvbiBjbGFzc05hbWU9XCJoLTE2IHctZnVsbCByb3VuZGVkLW1kXCIgLz5cclxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgaXRlbXMtZW5kIGdhcC0yXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8U2tlbGV0b24gY2xhc3NOYW1lPVwiaC02IHctMjBcIiAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPFNrZWxldG9uIGNsYXNzTmFtZT1cImgtOCB3LTI0XCIgLz5cclxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICA8L0NhcmQ+XHJcbiAgICAgICAgICAgICkpfVxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgKSA6IGZpbHRlcmVkT3JkZXJzLmxlbmd0aCA+IDAgPyAoXHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxyXG4gICAgICAgICAgICB7ZmlsdGVyZWRPcmRlcnMubWFwKChvcmRlciwgb3JkZXJJbmRleCkgPT4gKFxyXG4gICAgICAgICAgICAgIDxDYXJkIGtleT17b3JkZXIuT3JkZXJJZCB8fCBvcmRlci5PcmRlcklEIHx8IG9yZGVyLmlkIHx8IG9yZGVySW5kZXh9IGNsYXNzTmFtZT1cIm92ZXJmbG93LWhpZGRlblwiPlxyXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJib3JkZXItYiBib3JkZXItYm9yZGVyIHAtNFwiPlxyXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgbWQ6ZmxleC1yb3cganVzdGlmeS1iZXR3ZWVuIGl0ZW1zLXN0YXJ0IG1kOml0ZW1zLWNlbnRlciBnYXAtNFwiPlxyXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yIG1iLTJcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAge29yZGVyLk9yZGVyTnVtYmVyIHx8IGBPUiMke1N0cmluZyhvcmRlci5PcmRlcklkIHx8IG9yZGVyLk9yZGVySUQpLnBhZFN0YXJ0KDgsICcwJyl9YH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9oMz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgeygoKSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgLy8gVXNlIExhdGVzdFN0YXR1c05hbWUgZnJvbSBBUEkgaWYgYXZhaWxhYmxlLCBvdGhlcndpc2UgZmFsbCBiYWNrIHRvIFN0YXR1c0lEIG1hcHBpbmdcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBzdGF0dXNOYW1lID0gb3JkZXIuTGF0ZXN0U3RhdHVzTmFtZTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAoc3RhdHVzTmFtZSkge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgLy8gTWFwIEFQSSBzdGF0dXMgbmFtZXMgdG8gb3VyIGRpc3BsYXkgZm9ybWF0XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBsZXQgZGlzcGxheVN0YXR1cyA9IHN0YXR1c05hbWU7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBsZXQgc3RhdHVzQ29sb3IgPSAnYmctYmx1ZS0xMDAgdGV4dC1ibHVlLTgwMCc7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBsZXQgc3RhdHVzSWNvbiA9IDxQYWNrYWdlIGNsYXNzTmFtZT1cImgtNSB3LTUgdGV4dC1ibHVlLTUwMFwiIC8+O1xyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN3aXRjaCAoc3RhdHVzTmFtZS50b0xvd2VyQ2FzZSgpKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNhc2UgJ2FjdGl2ZSc6XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc3RhdHVzQ29sb3IgPSAnYmctYmx1ZS0xMDAgdGV4dC1ibHVlLTgwMCc7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc3RhdHVzSWNvbiA9IDxQYWNrYWdlIGNsYXNzTmFtZT1cImgtNSB3LTUgdGV4dC1ibHVlLTUwMFwiIC8+O1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGJyZWFrO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjYXNlICdpbiBwcm9ncmVzcyc6XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNhc2UgJ3Byb2Nlc3NpbmcnOlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN0YXR1c0NvbG9yID0gJ2JnLW9yYW5nZS0xMDAgdGV4dC1vcmFuZ2UtODAwJztcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzdGF0dXNJY29uID0gPFRydWNrIGNsYXNzTmFtZT1cImgtNSB3LTUgdGV4dC1vcmFuZ2UtNTAwXCIgLz47XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYnJlYWs7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNhc2UgJ2NvbXBsZXRlZCc6XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNhc2UgJ2RlbGl2ZXJlZCc6XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc3RhdHVzQ29sb3IgPSAnYmctZ3JlZW4tMTAwIHRleHQtZ3JlZW4tODAwJztcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzdGF0dXNJY29uID0gPENoZWNrQ2lyY2xlIGNsYXNzTmFtZT1cImgtNSB3LTUgdGV4dC1ncmVlbi01MDBcIiAvPjtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBicmVhaztcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2FzZSAnY2FuY2VsbGVkJzpcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzdGF0dXNDb2xvciA9ICdiZy1yZWQtMTAwIHRleHQtcmVkLTgwMCc7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc3RhdHVzSWNvbiA9IDxBbGVydENpcmNsZSBjbGFzc05hbWU9XCJoLTUgdy01IHRleHQtcmVkLTUwMFwiIC8+O1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGJyZWFrO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkZWZhdWx0OlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN0YXR1c0NvbG9yID0gJ2JnLWdyYXktMTAwIHRleHQtZ3JheS04MDAnO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN0YXR1c0ljb24gPSA8UGFja2FnZSBjbGFzc05hbWU9XCJoLTUgdy01IHRleHQtZ3JheS01MDBcIiAvPjtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YGZsZXggaXRlbXMtY2VudGVyIGdhcC0xIHB4LTIgcHktMSByb3VuZGVkLWZ1bGwgdGV4dC14cyAke3N0YXR1c0NvbG9yfWB9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtzdGF0dXNJY29ufVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPntkaXNwbGF5U3RhdHVzfTwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICApO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAvLyBGYWxsYmFjayB0byBTdGF0dXNJRCBtYXBwaW5nXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBzdGF0dXNJZCA9IG9yZGVyLlN0YXR1c0lEIHx8IG9yZGVyLk9yZGVyU3RhdHVzSWQgfHwgb3JkZXIuU3RhdGVJZCB8fCAxO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3Qgc3RhdHVzSW5mbyA9IGdldFN0YXR1c0luZm8oc3RhdHVzSWQpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9e2BmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMSBweC0yIHB5LTEgcm91bmRlZC1mdWxsIHRleHQteHMgJHtzdGF0dXNJbmZvLmNvbG9yfWB9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtzdGF0dXNJbmZvLmljb259XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4+e3N0YXR1c0luZm8ubmFtZX08L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgKTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH0pKCl9XHJcbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1tdXRlZC1mb3JlZ3JvdW5kIG1iLTJcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgT3JkZXJlZCBvbiB7KCgpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBkYXRlU3RyID0gb3JkZXIuT3JkZXJEYXRlVVRDIHx8IG9yZGVyLmRhdGUgfHwgb3JkZXIuT3JkZXJEYXRlIHx8IG9yZGVyLkNyZWF0ZWRPbjtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAoZGF0ZVN0cikge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdHJ5IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgZGF0ZSA9IG5ldyBEYXRlKGRhdGVTdHIpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gZGF0ZS50b0xvY2FsZURhdGVTdHJpbmcoJ2VuLVVTJywge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHllYXI6ICdudW1lcmljJyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBtb250aDogJ2xvbmcnLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRheTogJ251bWVyaWMnLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGhvdXI6ICcyLWRpZ2l0JyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBtaW51dGU6ICcyLWRpZ2l0J1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9KTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0gY2F0Y2ggKGUpIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIGRhdGVTdHI7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiAnTi9BJztcclxuICAgICAgICAgICAgICAgICAgICAgICAgfSkoKX1cclxuICAgICAgICAgICAgICAgICAgICAgIDwvcD5cclxuICAgICAgICAgICAgICAgICAgICAgIHsvKiBTaG93IG1haW4gcHJvZHVjdCBuYW1lcyBwcm9taW5lbnRseSAqL31cclxuICAgICAgICAgICAgICAgICAgICAgIHsob3JkZXIuaXRlbXMgfHwgb3JkZXIuT3JkZXJJdGVtcyB8fCBbXSkubGVuZ3RoID4gMCAmJiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctYmx1ZS01MCBweC0zIHB5LTIgcm91bmRlZC1tZFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ibHVlLTkwMCBtYi0xXCI+UHJvZHVjdHMgaW4gdGhpcyBvcmRlcjo8L3A+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtYmx1ZS04MDAgc3BhY2UteS0xXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7KG9yZGVyLml0ZW1zIHx8IG9yZGVyLk9yZGVySXRlbXMgfHwgW10pXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC5zbGljZSgwLCAzKVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAubWFwKChpdGVtOiBhbnksIGluZGV4OiBudW1iZXIpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvLyBHZXQgcHJvZHVjdCBuYW1lIGZyb20gaXRlbSBkYXRhXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgcHJvZHVjdE5hbWUgPSBpdGVtLm5hbWUgfHxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpdGVtLlByb2R1Y3ROYW1lIHx8XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaXRlbS5JdGVtTmFtZSB8fFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICdNZWRpY2FsIFByb2R1Y3QnO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IHByb2R1Y3RJZCA9IGl0ZW0uUHJvZHVjdElEIHx8IGl0ZW0uUHJvZHVjdElkO1xyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4ga2V5PXtpbmRleH0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtwcm9kdWN0SWQgPyAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPExpbmtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGhyZWY9e2AvcHJvZHVjdC8ke3Byb2R1Y3RJZH1gfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1ibHVlLTYwMCBob3Zlcjp0ZXh0LWJsdWUtODAwIGhvdmVyOnVuZGVybGluZSBmb250LW1lZGl1bVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0YXJnZXQ9XCJfYmxhbmtcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcmVsPVwibm9vcGVuZXIgbm9yZWZlcnJlclwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3Byb2R1Y3ROYW1lfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvTGluaz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKSA6IChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJmb250LW1lZGl1bVwiPntwcm9kdWN0TmFtZX08L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtpbmRleCA8IE1hdGgubWluKDIsIChvcmRlci5pdGVtcyB8fCBvcmRlci5PcmRlckl0ZW1zIHx8IFtdKS5sZW5ndGggLSAxKSAmJiAnIOKAoiAnfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICk7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0pfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgeyhvcmRlci5pdGVtcyB8fCBvcmRlci5PcmRlckl0ZW1zIHx8IFtdKS5sZW5ndGggPiAzICYmIChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ibHVlLTYwMFwiPiDigKIgK3sob3JkZXIuaXRlbXMgfHwgb3JkZXIuT3JkZXJJdGVtcyB8fCBbXSkubGVuZ3RoIC0gM30gbW9yZSBpdGVtczwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgXHJcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtNCBtdC00IG1kOm10LTBcIj5cclxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1yaWdodFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJmb250LW1lZGl1bVwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICR7KG9yZGVyLnRvdGFsIHx8IG9yZGVyLk9yZGVyVG90YWwgfHwgb3JkZXIuVG90YWxBbW91bnQgfHwgMCkudG9GaXhlZCgyKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9wPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHtvcmRlci5Ub3RhbEl0ZW1zIHx8IChvcmRlci5pdGVtcz8ubGVuZ3RoIHx8IG9yZGVyLk9yZGVySXRlbXM/Lmxlbmd0aCB8fCBvcmRlci5JdGVtQ291bnQgfHwgMCl9IGl0ZW0ocylcclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9wPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICBcclxuICAgICAgICAgICAgICAgICAgICAgIDxCdXR0b24gXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lXCIgXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHNpemU9XCJzbVwiIFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGFzQ2hpbGRcclxuICAgICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPExpbmsgaHJlZj17Y3JlYXRlU2VjdXJlT3JkZXJMaW5rKG9yZGVyKX0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPEZpbGVUZXh0IGNsYXNzTmFtZT1cImgtNCB3LTRcIiAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIERldGFpbHNcclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8Q2hldnJvblJpZ2h0IGNsYXNzTmFtZT1cImgtNCB3LTRcIiAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L0xpbms+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cclxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgIFxyXG4gICAgICAgICAgICAgICAgey8qIFNob3cgb3JkZXIgaXRlbXMgcHJldmlldyAtIGxpbWl0IHRvIDMgaXRlbXMgKi99XHJcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtNCBiZy1tdXRlZC8zMFwiPlxyXG4gICAgICAgICAgICAgICAgICA8aDQgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSBtYi0yXCI+T3JkZXIgSXRlbXMgUHJldmlldzwvaDQ+XHJcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgeyhvcmRlci5pdGVtcyB8fCBvcmRlci5PcmRlckl0ZW1zIHx8IFtdKS5zbGljZSgwLCAzKS5tYXAoKGl0ZW06IGFueSwgaW5kZXg6IG51bWJlcikgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgY29uc3QgcHJvZHVjdEltYWdlID0gaXRlbS5Qcm9kdWN0SW1hZ2VVcmwgfHwgaXRlbS5JbWFnZVVybCB8fCBpdGVtLlByb2R1Y3RJbWFnZTtcclxuXHJcbiAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGtleT17aW5kZXh9IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuIGl0ZW1zLWNlbnRlclwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTNcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0xMiBoLTEyIGJnLW11dGVkIHJvdW5kZWQtbWQgb3ZlcmZsb3ctaGlkZGVuIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtwcm9kdWN0SW1hZ2UgPyAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGltZ1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc3JjPXtwcm9kdWN0SW1hZ2Uuc3RhcnRzV2l0aCgnaHR0cCcpID8gcHJvZHVjdEltYWdlIDogYCR7Q29uZmlnLkFETUlOX0JBU0VfVVJMfSR7cHJvZHVjdEltYWdlfWB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBhbHQ9e2l0ZW0ubmFtZSB8fCBpdGVtLlByb2R1Y3ROYW1lIHx8ICdQcm9kdWN0J31cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBoLWZ1bGwgb2JqZWN0LWNvdmVyXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uRXJyb3I9eyhlKSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IHRhcmdldCA9IGUudGFyZ2V0IGFzIEhUTUxJbWFnZUVsZW1lbnQ7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRhcmdldC5zdHlsZS5kaXNwbGF5ID0gJ25vbmUnO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBwYXJlbnQgPSB0YXJnZXQucGFyZW50RWxlbWVudDtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKHBhcmVudCkge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHBhcmVudC5pbm5lckhUTUwgPSAnPHN2ZyBjbGFzcz1cInctNiBoLTYgdGV4dC1ncmF5LTQwMFwiIGZpbGw9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDIwIDIwXCI+PHBhdGggZmlsbC1ydWxlPVwiZXZlbm9kZFwiIGQ9XCJNNCAzYTIgMiAwIDAwLTIgMnYxMGEyIDIgMCAwMDIgMmgxMmEyIDIgMCAwMDItMlY1YTIgMiAwIDAwLTItMkg0em0xMiAxMkg0bDQtOCAzIDYgMi00IDMgNnpcIiBjbGlwLXJ1bGU9XCJldmVub2RkXCI+PC9wYXRoPjwvc3ZnPic7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKSA6IChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8UGFja2FnZSBjbGFzc05hbWU9XCJoLTYgdy02IHRleHQtZ3JheS00MDBcIiAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMVwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS05MDBcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7aXRlbS5uYW1lIHx8IGl0ZW0uUHJvZHVjdE5hbWUgfHwgaXRlbS5JdGVtTmFtZSB8fCAnTWVkaWNhbCBQcm9kdWN0J31cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9wPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFF0eToge2l0ZW0ucXVhbnRpdHkgfHwgaXRlbS5RdWFudGl0eSB8fCBpdGVtLk9yZGVyUXVhbnRpdHkgfHwgMX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9wPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7aXRlbS5Qcm9kdWN0RGVzY3JpcHRpb24gJiYgKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTUwMCBtdC0xIGxpbmUtY2xhbXAtMVwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2l0ZW0uUHJvZHVjdERlc2NyaXB0aW9ufVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvcD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW1cIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICR7KGl0ZW0ucHJpY2UgfHwgaXRlbS5QcmljZSB8fCBpdGVtLlVuaXRQcmljZSB8fCBpdGVtLkl0ZW1QcmljZSB8fCAwKS50b0ZpeGVkKDIpfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvcD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICApO1xyXG4gICAgICAgICAgICAgICAgICAgIH0pfVxyXG4gICAgICAgICAgICAgICAgICAgIHsob3JkZXIuaXRlbXM/Lmxlbmd0aCB8fCBvcmRlci5PcmRlckl0ZW1zPy5sZW5ndGggfHwgMCkgPiAzICYmIChcclxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LW11dGVkLWZvcmVncm91bmQgdGV4dC1jZW50ZXIgcHQtMlwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAreyhvcmRlci5pdGVtcz8ubGVuZ3RoIHx8IG9yZGVyLk9yZGVySXRlbXM/Lmxlbmd0aCB8fCAwKSAtIDN9IG1vcmUgaXRlbXNcclxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgPC9DYXJkPlxyXG4gICAgICAgICAgICApKX1cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICkgOiAoXHJcbiAgICAgICAgICA8Q2FyZD5cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTggdGV4dC1jZW50ZXJcIj5cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMTYgaC0xNiBiZy1tdXRlZCByb3VuZGVkLWZ1bGwgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgbXgtYXV0byBtYi00XCI+XHJcbiAgICAgICAgICAgICAgICA8UGFja2FnZSBjbGFzc05hbWU9XCJoLTggdy04IHRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiIC8+XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1tZWRpdW0gbWItMlwiPk5vIG9yZGVycyBmb3VuZDwvaDM+XHJcbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1tdXRlZC1mb3JlZ3JvdW5kIG1iLTRcIj5Zb3UgZG9uJ3QgaGF2ZSBhbnkge2FjdGl2ZVRhYiAhPT0gJ2FsbCcgPyBhY3RpdmVUYWIgOiAnJ30gb3JkZXJzIHlldC48L3A+XHJcbiAgICAgICAgICAgICAgPEJ1dHRvbiBhc0NoaWxkPlxyXG4gICAgICAgICAgICAgICAgPExpbmsgaHJlZj1cIi9cIj5cclxuICAgICAgICAgICAgICAgICAgQ29udGludWUgU2hvcHBpbmdcclxuICAgICAgICAgICAgICAgIDwvTGluaz5cclxuICAgICAgICAgICAgICA8L0J1dHRvbj5cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICA8L0NhcmQ+XHJcbiAgICAgICAgKX1cclxuICAgICAgPC9kaXY+XHJcbiAgICA8L2Rpdj5cclxuICApO1xyXG59Il0sIm5hbWVzIjpbInVzZVN0YXRlIiwidXNlRWZmZWN0IiwidXNlQ2FsbGJhY2siLCJCcmVhZGNydW1iIiwiQnJlYWRjcnVtYkl0ZW0iLCJCcmVhZGNydW1iTGluayIsIkJyZWFkY3J1bWJMaXN0IiwiQnJlYWRjcnVtYlBhZ2UiLCJCcmVhZGNydW1iU2VwYXJhdG9yIiwiQ2FyZCIsIkJ1dHRvbiIsIlRhYnMiLCJUYWJzTGlzdCIsIlRhYnNUcmlnZ2VyIiwiU2tlbGV0b24iLCJMaW5rIiwidXNlU2V0dGluZ3MiLCJ1c2VVc2VyIiwiQ29uZmlnIiwiUGFja2FnZSIsIlNlYXJjaCIsIkNoZXZyb25SaWdodCIsIkZpbGVUZXh0IiwiVHJ1Y2siLCJDaGVja0NpcmNsZSIsIkFsZXJ0Q2lyY2xlIiwiUmVmcmVzaEN3IiwiZW5jcnlwdE9yZGVySWQiLCJlbmNyeXB0VmFsdWUiLCJPcmRlcnNQYWdlIiwidCIsInByaW1hcnlDb2xvciIsInByaW1hcnlUZXh0Q29sb3IiLCJ1c2VyIiwiaXNMb2dnZWRJbiIsImlzTG9hZGluZyIsInRva2VuIiwiYWN0aXZlVGFiIiwic2V0QWN0aXZlVGFiIiwib3JkZXJzIiwic2V0T3JkZXJzIiwibG9hZGluZyIsInNldExvYWRpbmciLCJzZWxlY3RlZE9yZGVyIiwic2V0U2VsZWN0ZWRPcmRlciIsIm9yZGVyRGV0YWlscyIsInNldE9yZGVyRGV0YWlscyIsImxvYWRpbmdEZXRhaWxzIiwic2V0TG9hZGluZ0RldGFpbHMiLCJzZWFyY2hRdWVyeSIsInNldFNlYXJjaFF1ZXJ5Iiwic29ydEJ5Iiwic2V0U29ydEJ5IiwiZmV0Y2hPcmRlckhpc3RvcnkiLCJhdXRoSGVhZGVycyIsInJlc3BvbnNlIiwiZmV0Y2giLCJtZXRob2QiLCJoZWFkZXJzIiwiYm9keSIsIkpTT04iLCJzdHJpbmdpZnkiLCJyZXF1ZXN0UGFyYW1ldGVycyIsInJlY29yZFZhbHVlSnNvbiIsImRhdGEiLCJqc29uIiwiY29uc29sZSIsImxvZyIsIm9yZGVyc0RhdGEiLCJwYXJzZSIsIkFycmF5IiwiaXNBcnJheSIsIm1hcHBlZE9yZGVycyIsIm1hcCIsIm9yZGVyIiwiaWQiLCJPcmRlcklkIiwiT3JkZXJJRCIsIk9yZGVyTnVtYmVyIiwiZGF0ZSIsIk9yZGVyRGF0ZVVUQyIsIkRhdGUiLCJ0b0xvY2FsZURhdGVTdHJpbmciLCJPcmRlckRhdGUiLCJ0b3RhbCIsIk9yZGVyVG90YWwiLCJUb3RhbEFtb3VudCIsInN0YXR1cyIsIkxhdGVzdFN0YXR1c05hbWUiLCJTdGF0dXNJRCIsIkl0ZW1Db3VudCIsIlRvdGFsSXRlbXMiLCJpdGVtcyIsIk9yZGVySXRlbXMiLCJzb3J0ZWRPcmRlcnMiLCJzb3J0IiwiYSIsImIiLCJnZXRUaW1lIiwiZXJyb3IiLCJmZXRjaE9yZGVyRGV0YWlscyIsIm9yZGVySWQiLCJwYXJzZWREYXRhIiwidXNlcklkIiwiVXNlcklEIiwiVXNlcklkIiwid2luZG93IiwibG9jYXRpb24iLCJocmVmIiwiZGl2IiwiY2xhc3NOYW1lIiwicCIsIm1vY2tPcmRlcnMiLCJuYW1lIiwiUHJvZHVjdE5hbWUiLCJxdWFudGl0eSIsInByaWNlIiwiZGlzcGxheU9yZGVycyIsInRhYkZpbHRlcmVkT3JkZXJzIiwiZmlsdGVyIiwic3RhdHVzSWQiLCJPcmRlclN0YXR1c0lkIiwiU3RhdGVJZCIsInRvU3RyaW5nIiwic2VhcmNoRmlsdGVyZWRPcmRlcnMiLCJ0cmltIiwic2VhcmNoVGVybSIsInRvTG93ZXJDYXNlIiwib3JkZXJOdW1iZXIiLCJTdHJpbmciLCJwYWRTdGFydCIsInByb2R1Y3ROYW1lcyIsIml0ZW0iLCJJdGVtTmFtZSIsImpvaW4iLCJpbmNsdWRlcyIsImZpbHRlcmVkT3JkZXJzIiwiY3JlYXRlU2VjdXJlT3JkZXJMaW5rIiwiZW5jcnlwdGVkT3JkZXJJZCIsIm9yZGVyVG90YWwiLCJ0b0ZpeGVkIiwib3JkZXJEYXRlIiwiZGF0ZVN0ciIsIkNyZWF0ZWRPbiIsInllYXIiLCJtb250aCIsImRheSIsImUiLCJlbmNyeXB0ZWRUb3RhbCIsImVuY3J5cHRlZERhdGUiLCJnZXRTdGF0dXNJbmZvIiwicGFyc2VJbnQiLCJpY29uIiwiY29sb3IiLCJhc0NoaWxkIiwiaDEiLCJ2YXJpYW50Iiwic2l6ZSIsIm9uQ2xpY2siLCJkaXNhYmxlZCIsImRlZmF1bHRWYWx1ZSIsIm9uVmFsdWVDaGFuZ2UiLCJ2YWx1ZSIsInN0eWxlIiwiYmFja2dyb3VuZENvbG9yIiwidHJhbnNmb3JtIiwiYm94U2hhZG93IiwiYm9yZGVyQ29sb3IiLCJpbnB1dCIsInR5cGUiLCJwbGFjZWhvbGRlciIsIm9uQ2hhbmdlIiwidGFyZ2V0Iiwic3BhbiIsInNlbGVjdCIsIm9wdGlvbiIsIl8iLCJpbmRleCIsImxlbmd0aCIsIm9yZGVySW5kZXgiLCJoMyIsInN0YXR1c05hbWUiLCJkaXNwbGF5U3RhdHVzIiwic3RhdHVzQ29sb3IiLCJzdGF0dXNJY29uIiwic3RhdHVzSW5mbyIsImhvdXIiLCJtaW51dGUiLCJzbGljZSIsInByb2R1Y3ROYW1lIiwicHJvZHVjdElkIiwiUHJvZHVjdElEIiwiUHJvZHVjdElkIiwicmVsIiwiTWF0aCIsIm1pbiIsImg0IiwicHJvZHVjdEltYWdlIiwiUHJvZHVjdEltYWdlVXJsIiwiSW1hZ2VVcmwiLCJQcm9kdWN0SW1hZ2UiLCJpbWciLCJzcmMiLCJzdGFydHNXaXRoIiwiQURNSU5fQkFTRV9VUkwiLCJhbHQiLCJvbkVycm9yIiwiZGlzcGxheSIsInBhcmVudCIsInBhcmVudEVsZW1lbnQiLCJpbm5lckhUTUwiLCJRdWFudGl0eSIsIk9yZGVyUXVhbnRpdHkiLCJQcm9kdWN0RGVzY3JpcHRpb24iLCJQcmljZSIsIlVuaXRQcmljZSIsIkl0ZW1QcmljZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/orders/page.tsx\n"));

/***/ })

});