/*
 * Globalize Culture mi-NZ
 *
 * http://github.com/jquery/globalize
 *
 * Copyright Software Freedom Conservancy, Inc.
 * Dual licensed under the MIT or GPL Version 2 licenses.
 * http://jquery.org/license
 *
 * This file was generated by the Globalize Culture Generator
 * Translation: bugs found in this file need to be fixed in the generator
 */

(function( window, undefined ) {

var Globalize;

if ( typeof require !== "undefined" &&
	typeof exports !== "undefined" &&
	typeof module !== "undefined" ) {
	// Assume CommonJS
	Globalize = require( "globalize" );
} else {
	// Global variable
	Globalize = window.Globalize;
}

Globalize.addCultureInfo( "mi-NZ", "default", {
	name: "mi-NZ",
	englishName: "<PERSON><PERSON> (New Zealand)",
	nativeName: "<PERSON>o <PERSON> (Aotearoa)",
	language: "mi",
	numberFormat: {
		percent: {
			pattern: ["-%n","%n"]
		},
		currency: {
			pattern: ["-$n","$n"]
		}
	},
	calendars: {
		standard: {
			firstDay: 1,
			days: {
				names: ["<PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON>"],
				namesAbbr: ["Ta","<PERSON>","<PERSON><PERSON>","Apa","Pa","Me","Ho"],
				namesShort: ["Ta","Hi","Tū","Aa","Pa","Me","Ho"]
			},
			months: {
				names: ["Kohi-tātea","Hui-tanguru","Poutū-te-rangi","Paenga-whāwhā","Haratua","Pipiri","Hōngongoi","Here-turi-kōkā","Mahuru","Whiringa-ā-nuku","Whiringa-ā-rangi","Hakihea",""],
				namesAbbr: ["Kohi","Hui","Pou","Pae","Hara","Pipi","Hōngo","Here","Mahu","Nuku","Rangi","Haki",""]
			},
			AM: ["a.m.","a.m.","A.M."],
			PM: ["p.m.","p.m.","P.M."],
			patterns: {
				d: "dd/MM/yyyy",
				D: "dddd, dd MMMM, yyyy",
				f: "dddd, dd MMMM, yyyy h:mm tt",
				F: "dddd, dd MMMM, yyyy h:mm:ss tt",
				M: "dd MMMM",
				Y: "MMMM, yy"
			}
		}
	}
});

}( this ));
