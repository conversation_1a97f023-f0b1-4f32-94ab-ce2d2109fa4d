@echo off
echo ========================================
echo Enhanced Order Processing Local Tests
echo ========================================
echo.
echo Testing against: http://localhost:5005
echo.

REM Check if Node.js is available
where node >nul 2>nul
if %ERRORLEVEL% EQU 0 (
    echo Running Node.js test script (no external dependencies)...
    echo.
    node test-local-order-processing.js
    if %ERRORLEVEL% NEQ 0 (
        echo.
        echo Node.js test failed, trying PowerShell fallback...
        echo.
        powershell -ExecutionPolicy Bypass -File test-local-order-processing.ps1
    )
    echo.
    echo ========================================
    echo Tests completed
    echo ========================================
) else (
    echo Node.js not found, using PowerShell...
    echo.
    powershell -ExecutionPolicy Bypass -File test-local-order-processing.ps1
    echo.
    echo ========================================
    echo PowerShell tests completed
    echo ========================================
)

echo.
echo Press any key to exit...
pause >nul
