import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import { MakeApiCallAsync, Config } from '@/lib/api-helper';
import { parseBodyLinks } from '@/lib/parse-body-links';
import Image from 'next/image';
import Link from 'next/link';
import { Button } from '@/components/ui/button';

type CampaignDetailProps = {
  params: {
    id: string;
    title: string;
  };
};

interface CampaignDetail {
  CampaignId: string;
  MainTitle: string;
  DiscountTitle: string;
  CoverPictureUrl: string;
  Body: string;
}

export async function generateMetadata(
  { params }: CampaignDetailProps
): Promise<Metadata> {
  try {
    const campaign = await getCampaignDetail(params.id);
    return {
      title: campaign?.MainTitle || 'Campaign Detail',
      description: campaign?.DiscountTitle || 'Campaign information',
    };
  } catch (error) {
    return {
      title: 'Campaign Detail',
      description: 'Campaign information',
    };
  }
}

async function getCampaignDetail(id: string): Promise<CampaignDetail | null> {
  try {
    const param = {
      requestParameters: {
        CampaignId: id,
      },
    };

    const headers = {
      'Content-Type': 'application/json',
      Accept: 'application/json',
    };

    const response = await MakeApiCallAsync(
      'get-web-campaign-detail',
      null,
      param,
      headers,
      'POST',
      true
    );

    if (response?.data?.data) {
      const parsedData = JSON.parse(response.data.data);
      return parsedData;
    }

    return null;
  } catch (error) {
    console.error('Error fetching campaign detail:', error);
    return null;
  }
}

export default async function CampaignDetailPage({ params }: CampaignDetailProps) {
  const campaign = await getCampaignDetail(params.id);

  if (!campaign) {
    notFound();
  }

  const adminPanelBaseURL = process.env.NEXT_PUBLIC_ADMIN_BASE_URL || '';

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <Link href="/" className="text-primary hover:underline">
          Home
        </Link>{' '}
        &gt;{' '}
        <span className="text-gray-500">Campaign</span>{' '}
        &gt;{' '}
        <span className="text-gray-700">{campaign.MainTitle}</span>
      </div>

      <div className="bg-white rounded-lg shadow-md overflow-hidden">
        <div className="relative w-full h-[300px] md:h-[400px]">
          <Image
            src={adminPanelBaseURL + campaign.CoverPictureUrl}
            alt={campaign.MainTitle}
            fill
            className="object-cover"
            priority
            onError={(e) => {
              // If image fails to load, replace with a placeholder
              const target = e.target as HTMLImageElement;
              target.src = 'https://placehold.co/1200x400/cccccc/666666?text=Campaign+Image';
            }}
          />
        </div>

        <div className="p-6">
          <h1 className="text-3xl font-bold mb-2">{campaign.MainTitle}</h1>
          <h2 className="text-xl text-gray-600 mb-6">{campaign.DiscountTitle}</h2>

          {campaign.Body && (
            <div 
              className="prose max-w-none mt-6" 
              dangerouslySetInnerHTML={{ __html: parseBodyLinks(campaign.Body) }}
            />
          )}
        </div>
      </div>
    </div>
  );
}