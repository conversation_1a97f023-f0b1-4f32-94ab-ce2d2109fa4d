import { notFound } from 'next/navigation';
import { MakeApiCallAsync } from '@/lib/api-helper';
import CampaignDetailClient from './client-page';

interface CampaignDetail {
  CampaignId: string;
  MainTitle: string;
  DiscountTitle: string;
  CoverPictureUrl: string;
  Body: string;
}

async function getCampaignDetail(id: string): Promise<CampaignDetail | null> {
  try {
    const param = {
      requestParameters: {
        CampaignId: id,
      },
    };

    const headers = {
      'Content-Type': 'application/json',
      Accept: 'application/json',
    };

    const response = await MakeApiCallAsync(
      'get-web-campaign-detail',
      null,
      param,
      headers,
      'POST',
      true
    );

    if (response?.data?.data) {
      try {
        const parsedData = JSON.parse(response.data.data);
        if (parsedData) {
          return {
            CampaignId: parsedData.CampaignId?.toString() || '',
            MainTitle: parsedData.MainTitle || parsedData.Title || '',
            DiscountTitle: parsedData.DiscountTitle || parsedData.SubTitle || '',
            CoverPictureUrl: parsedData.CoverPictureUrl || parsedData.ImageUrl || '',
            Body: parsedData.Body || '',
          };
        }
      } catch (parseError) {
        console.error('Error parsing campaign data:', parseError);
      }
    }
    return null;
  } catch (error) {
    console.error('Error fetching campaign detail:', error);
    return null;
  }
}

type Props = {
  params: Promise<{ id: string; title: string }>;
  searchParams: Promise<Record<string, string | string[] | undefined>>;
};

export default async function CampaignDetailPage({ params }: Props) {
  const resolvedParams = await params;
  const campaign = await getCampaignDetail(resolvedParams.id);
  const adminPanelBaseURL = process.env.NEXT_PUBLIC_ADMIN_BASE_URL || '';
  
  if (!campaign) {
    notFound();
  }

  return <CampaignDetailClient initialCampaign={campaign} adminPanelBaseURL={adminPanelBaseURL} />;
}