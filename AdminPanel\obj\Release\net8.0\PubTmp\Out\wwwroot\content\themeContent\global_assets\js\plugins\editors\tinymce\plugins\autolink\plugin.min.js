/**
 * Copyright (c) Tiny Technologies, Inc. All rights reserved.
 * Licensed under the LGPL or a commercial license.
 * For LGPL see License.txt in the project root for license information.
 * For commercial licenses see https://www.tiny.cloud/
 *
 * Version: 5.3.0 (2020-05-21)
 */
!function(){"use strict";var e=tinymce.util.Tools.resolve("tinymce.PluginManager"),i=tinymce.util.Tools.resolve("tinymce.Env"),o=function(e,t){if(t<0&&(t=0),3===e.nodeType){var n=e.data.length;n<t&&(t=n)}return t},y=function(e,t,n){1!==t.nodeType||t.hasChildNodes()?e.setStart(t,o(t,n)):e.setStartBefore(t)},k=function(e,t,n){1!==t.nodeType||t.hasChildNodes()?e.setEnd(t,o(t,n)):e.setEndAfter(t)},r=function(e,t,n){var i,o,r,a,s,f,l,d,c,g,u=e.getParam("autolink_pattern",/^(https?:\/\/|ssh:\/\/|ftp:\/\/|file:\/|www\.|(?:mailto:)?[A-Z0-9._%+\-]+@)(.+)$/i),h=e.getParam("default_link_target",!1);if("A"!==e.selection.getNode().tagName){if((i=e.selection.getRng(!0).cloneRange()).startOffset<5){if(!(d=i.endContainer.previousSibling)){if(!i.endContainer.firstChild||!i.endContainer.firstChild.nextSibling)return;d=i.endContainer.firstChild.nextSibling}if(c=d.length,y(i,d,c),k(i,d,c),i.endOffset<5)return;o=i.endOffset,a=d}else{if(3!==(a=i.endContainer).nodeType&&a.firstChild){for(;3!==a.nodeType&&a.firstChild;)a=a.firstChild;3===a.nodeType&&(y(i,a,0),k(i,a,a.nodeValue.length))}o=1===i.endOffset?2:i.endOffset-1-t}for(r=o;y(i,a,2<=o?o-2:0),k(i,a,1<=o?o-1:0),--o," "!==(g=i.toString())&&""!==g&&160!==g.charCodeAt(0)&&0<=o-2&&g!==n;);var m;(m=i.toString())===n||" "===m||160===m.charCodeAt(0)?(y(i,a,o),k(i,a,r),o+=1):(0===i.startOffset?y(i,a,0):y(i,a,o),k(i,a,r)),"."===(f=i.toString()).charAt(f.length-1)&&k(i,a,r-1),l=(f=i.toString().trim()).match(u);var C=e.getParam("link_default_protocol","http","string");l&&("www."===l[1]?l[1]=C+"://www.":/@$/.test(l[1])&&!/^mailto:/.test(l[1])&&(l[1]="mailto:"+l[1]),s=e.selection.getBookmark(),e.selection.setRng(i),e.execCommand("createlink",!1,l[1]+l[2]),!1!==h&&e.dom.setAttrib(e.selection.getNode(),"target",h),e.selection.moveToBookmark(s),e.nodeChanged())}},t=function(t){var n;t.on("keydown",function(e){if(13!==e.keyCode);else r(t,-1,"")}),i.browser.isIE()?t.on("focus",function(){if(!n){n=!0;try{t.execCommand("AutoUrlDetect",!1,!0)}catch(e){}}}):(t.on("keypress",function(e){if(41!==e.keyCode);else r(t,-1,"(")}),t.on("keyup",function(e){if(32!==e.keyCode);else r(t,0,"")}))};!function n(){e.add("autolink",function(e){t(e)})}()}();