"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/checkout/page",{

/***/ "(app-pages-browser)/./app/checkout/page.tsx":
/*!*******************************!*\
  !*** ./app/checkout/page.tsx ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CheckoutPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _contexts_cart_context__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/cart-context */ \"(app-pages-browser)/./contexts/cart-context.tsx\");\n/* harmony import */ var _contexts_user_context__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/user-context */ \"(app-pages-browser)/./contexts/user-context.tsx\");\n/* harmony import */ var _contexts_coupon_context__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/coupon-context */ \"(app-pages-browser)/./contexts/coupon-context.tsx\");\n/* harmony import */ var _contexts_currency_context__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/contexts/currency-context */ \"(app-pages-browser)/./contexts/currency-context.tsx\");\n/* harmony import */ var _contexts_settings_context__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/contexts/settings-context */ \"(app-pages-browser)/./contexts/settings-context.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_payment_method_details__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/payment-method-details */ \"(app-pages-browser)/./components/ui/payment-method-details.tsx\");\n/* harmony import */ var _components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/breadcrumb */ \"(app-pages-browser)/./components/ui/breadcrumb.tsx\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var _barrel_optimize_names_CreditCard_DollarSign_LogIn_Star_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=CreditCard,DollarSign,LogIn,Star,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/log-in.js\");\n/* harmony import */ var _barrel_optimize_names_CreditCard_DollarSign_LogIn_Star_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=CreditCard,DollarSign,LogIn,Star,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_CreditCard_DollarSign_LogIn_Star_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=CreditCard,DollarSign,LogIn,Star,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_CreditCard_DollarSign_LogIn_Star_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=CreditCard,DollarSign,LogIn,Star,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_CreditCard_DollarSign_LogIn_Star_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=CreditCard,DollarSign,LogIn,Star,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var sweetalert2__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! sweetalert2 */ \"(app-pages-browser)/./node_modules/sweetalert2/dist/sweetalert2.all.js\");\n/* harmony import */ var sweetalert2__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(sweetalert2__WEBPACK_IMPORTED_MODULE_14__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction CheckoutPage() {\n    var _cities_find, _countries_find;\n    _s();\n    const { t, primaryColor } = (0,_contexts_settings_context__WEBPACK_IMPORTED_MODULE_7__.useSettings)();\n    const { user, isLoggedIn, token, isLoading } = (0,_contexts_user_context__WEBPACK_IMPORTED_MODULE_4__.useUser)();\n    const { items, total, subtotal, totalIQD, subtotalIQD, clearCart } = (0,_contexts_cart_context__WEBPACK_IMPORTED_MODULE_3__.useCart)();\n    const { appliedCoupon } = (0,_contexts_coupon_context__WEBPACK_IMPORTED_MODULE_5__.useCoupon)();\n    const { formatIQD, formatUSD } = (0,_contexts_currency_context__WEBPACK_IMPORTED_MODULE_6__.useCurrency)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [selectedPaymentMethod, setSelectedPaymentMethod] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [paymentMethods, setPaymentMethods] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loadingPaymentMethods, setLoadingPaymentMethods] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showUSD, setShowUSD] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [usePoints, setUsePoints] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [shippingAddress, setShippingAddress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        address: \"\",\n        cityId: \"\",\n        countryId: \"107\",\n        zipCode: \"\"\n    });\n    const [countries, setCountries] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [cities, setCities] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loadingCountries, setLoadingCountries] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loadingCities, setLoadingCities] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [savedAddresses, setSavedAddresses] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedAddressId, setSelectedAddressId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loadingAddresses, setLoadingAddresses] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showManualAddress, setShowManualAddress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Redirect to login if not authenticated (only after loading is complete)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CheckoutPage.useEffect\": ()=>{\n            if (!isLoading && !isLoggedIn) {\n                router.push(\"/login\");\n            }\n        }\n    }[\"CheckoutPage.useEffect\"], [\n        isLoggedIn,\n        isLoading,\n        router\n    ]);\n    // Fetch saved addresses\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CheckoutPage.useEffect\": ()=>{\n            if (isLoggedIn && token) {\n                fetchSavedAddresses();\n            }\n        }\n    }[\"CheckoutPage.useEffect\"], [\n        isLoggedIn,\n        token\n    ]);\n    // Calculate credit discount (get from localStorage or state)\n    const userPoints = (user === null || user === void 0 ? void 0 : user.Pointno) || 0;\n    const pointsDiscount = usePoints ? userPoints : 0;\n    const pointsDiscountIQD = Math.round(pointsDiscount * 1500);\n    // Calculate final totals with all discounts\n    const finalTotal = Math.max(0, total - (appliedCoupon ? appliedCoupon.discount : 0) - pointsDiscount);\n    const finalTotalIQD = Math.max(0, totalIQD - (appliedCoupon ? Math.round(appliedCoupon.discount * 1500) : 0) - pointsDiscountIQD);\n    // Fetch saved addresses\n    const fetchSavedAddresses = async ()=>{\n        setLoadingAddresses(true);\n        try {\n            const response = await fetch(\"/api/addresses/get-user-addresses\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    Authorization: \"Bearer \".concat(token)\n                },\n                body: JSON.stringify({\n                    requestParameters: {\n                        recordValueJson: \"[]\"\n                    }\n                })\n            });\n            const data = await response.json();\n            if (data.statusCode === 200 && data.data) {\n                const addressesData = typeof data.data === \"string\" ? JSON.parse(data.data) : data.data;\n                setSavedAddresses(addressesData || []);\n            }\n        } catch (error) {\n            console.error(\"Error fetching saved addresses:\", error);\n        } finally{\n            setLoadingAddresses(false);\n        }\n    };\n    // Fetch payment methods from API\n    const fetchPaymentMethods = async ()=>{\n        try {\n            setLoadingPaymentMethods(true);\n            const response = await fetch(\"/api/payment-methods\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    requestParameters: {}\n                })\n            });\n            if (response.ok) {\n                const data = await response.json();\n                if (data.statusCode === 200 && data.data) {\n                    const methods = JSON.parse(data.data);\n                    setPaymentMethods(methods);\n                    // Set default payment method to the first one\n                    if (methods.length > 0) {\n                        setSelectedPaymentMethod(methods[0].PaymentMethodID);\n                    }\n                }\n            }\n        } catch (error) {\n            console.error(\"Error fetching payment methods:\", error);\n            // Fallback to default payment methods if API fails\n            setPaymentMethods([]);\n        } finally{\n            setLoadingPaymentMethods(false);\n        }\n    };\n    // Get credit usage from localStorage and load countries\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CheckoutPage.useEffect\": ()=>{\n            const savedUsePoints = localStorage.getItem(\"usePoints\") === \"true\";\n            setUsePoints(savedUsePoints);\n            fetchPaymentMethods();\n        }\n    }[\"CheckoutPage.useEffect\"], []);\n    const fetchCountries = async ()=>{\n        setLoadingCountries(true);\n        try {\n            console.log(\"Fetching countries...\");\n            const response = await fetch(\"/api/countries\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    Accept: \"application/json\"\n                },\n                body: JSON.stringify({\n                    requestParameters: {\n                        recordValueJson: \"[]\"\n                    }\n                })\n            });\n            console.log(\"Countries response status:\", response.status);\n            const data = await response.json();\n            console.log(\"Countries response data:\", data);\n            if (data && data.data) {\n                const parsedData = JSON.parse(data.data);\n                console.log(\"Parsed countries data:\", parsedData);\n                if (Array.isArray(parsedData)) {\n                    setCountries(parsedData);\n                    console.log(\"Countries set:\", parsedData.length, \"countries\");\n                } else {\n                    console.error(\"Parsed data is not an array:\", parsedData);\n                }\n            } else {\n                console.error(\"No data in response:\", data);\n                // Add fallback countries for testing\n                const fallbackCountries = [\n                    {\n                        CountryID: 1,\n                        CountryName: \"United States\",\n                        Name: \"United States\"\n                    },\n                    {\n                        CountryID: 2,\n                        CountryName: \"Canada\",\n                        Name: \"Canada\"\n                    },\n                    {\n                        CountryID: 3,\n                        CountryName: \"United Kingdom\",\n                        Name: \"United Kingdom\"\n                    },\n                    {\n                        CountryID: 4,\n                        CountryName: \"Germany\",\n                        Name: \"Germany\"\n                    },\n                    {\n                        CountryID: 5,\n                        CountryName: \"France\",\n                        Name: \"France\"\n                    }\n                ];\n                setCountries(fallbackCountries);\n                console.log(\"Using fallback countries\");\n            }\n        } catch (error) {\n            console.error(\"Error fetching countries:\", error);\n            // Add fallback countries on error\n            const fallbackCountries = [\n                {\n                    CountryID: 1,\n                    CountryName: \"United States\",\n                    Name: \"United States\"\n                },\n                {\n                    CountryID: 2,\n                    CountryName: \"Canada\",\n                    Name: \"Canada\"\n                },\n                {\n                    CountryID: 3,\n                    CountryName: \"United Kingdom\",\n                    Name: \"United Kingdom\"\n                },\n                {\n                    CountryID: 4,\n                    CountryName: \"Germany\",\n                    Name: \"Germany\"\n                },\n                {\n                    CountryID: 5,\n                    CountryName: \"France\",\n                    Name: \"France\"\n                }\n            ];\n            setCountries(fallbackCountries);\n            console.log(\"Using fallback countries due to error\");\n        } finally{\n            setLoadingCountries(false);\n        }\n    };\n    const fetchCities = async (countryId)=>{\n        setLoadingCities(true);\n        setCities([]); // Clear previous cities\n        try {\n            const response = await fetch(\"/api/cities\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    Accept: \"application/json\"\n                },\n                body: JSON.stringify({\n                    requestParameters: {\n                        StateProvinceId: null,\n                        CountryId: countryId,\n                        recordValueJson: \"[]\"\n                    }\n                })\n            });\n            const data = await response.json();\n            if (data && data.data) {\n                const parsedData = JSON.parse(data.data);\n                if (Array.isArray(parsedData)) {\n                    setCities(parsedData);\n                }\n            }\n        } catch (error) {\n            console.error(\"Error fetching cities:\", error);\n        } finally{\n            setLoadingCities(false);\n        }\n    };\n    const handleInputChange = (e)=>{\n        const { name, value } = e.target;\n        setShippingAddress((prev)=>({\n                ...prev,\n                [name]: value\n            }));\n    };\n    const handleCountryChange = (countryId)=>{\n        setShippingAddress((prev)=>({\n                ...prev,\n                countryId: countryId,\n                cityId: \"\"\n            }));\n        if (countryId) {\n            fetchCities(parseInt(countryId));\n        } else {\n            setCities([]);\n        }\n    };\n    const handleCityChange = (cityId)=>{\n        setShippingAddress((prev)=>({\n                ...prev,\n                cityId: cityId\n            }));\n    };\n    const handleAddressSelection = (addressId)=>{\n        const selectedAddress = savedAddresses.find((addr)=>addr.AddressID === addressId);\n        if (selectedAddress) {\n            var _selectedAddress_CityID;\n            setSelectedAddressId(addressId);\n            setShippingAddress({\n                address: \"\".concat(selectedAddress.AddressLineOne).concat(selectedAddress.AddressLineTwo ? \", \" + selectedAddress.AddressLineTwo : \"\"),\n                cityId: ((_selectedAddress_CityID = selectedAddress.CityID) === null || _selectedAddress_CityID === void 0 ? void 0 : _selectedAddress_CityID.toString()) || \"\",\n                countryId: selectedAddress.CountryID.toString(),\n                zipCode: selectedAddress.PostalCode || \"\"\n            });\n        }\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        // Check if user is authenticated\n        if (!isLoggedIn) {\n            sweetalert2__WEBPACK_IMPORTED_MODULE_14___default().fire({\n                title: \"Login Required\",\n                text: \"Please login to complete your purchase\",\n                icon: \"info\",\n                showCancelButton: true,\n                confirmButtonText: \"Login\",\n                cancelButtonText: \"Cancel\"\n            }).then((result)=>{\n                if (result.isConfirmed) {\n                    router.push(\"/login?redirect=checkout\");\n                }\n            });\n            return;\n        }\n        // Check if payment method is selected\n        if (!selectedPaymentMethod) {\n            sweetalert2__WEBPACK_IMPORTED_MODULE_14___default().fire({\n                title: \"Error\",\n                text: \"Please select a payment method\",\n                icon: \"error\"\n            });\n            return;\n        }\n        // Basic validation for shipping address\n        if (!selectedAddressId) {\n            sweetalert2__WEBPACK_IMPORTED_MODULE_14___default().fire({\n                title: \"Address Required\",\n                text: \"Please select a delivery address to continue\",\n                icon: \"error\"\n            });\n            return;\n        }\n        if (!shippingAddress.address) {\n            sweetalert2__WEBPACK_IMPORTED_MODULE_14___default().fire({\n                title: \"Error\",\n                text: \"Please provide a shipping address\",\n                icon: \"error\"\n            });\n            return;\n        }\n        if (!shippingAddress.countryId) {\n            sweetalert2__WEBPACK_IMPORTED_MODULE_14___default().fire({\n                title: \"Error\",\n                text: \"Please select a country\",\n                icon: \"error\"\n            });\n            return;\n        }\n        if (!shippingAddress.cityId) {\n            sweetalert2__WEBPACK_IMPORTED_MODULE_14___default().fire({\n                title: \"Error\",\n                text: \"Please select a city\",\n                icon: \"error\"\n            });\n            return;\n        }\n        try {\n            // Show loading state\n            sweetalert2__WEBPACK_IMPORTED_MODULE_14___default().fire({\n                title: \"Processing\",\n                text: \"Please wait while we process your order...\",\n                allowOutsideClick: false,\n                didOpen: ()=>{\n                    sweetalert2__WEBPACK_IMPORTED_MODULE_14___default().showLoading();\n                }\n            });\n            // Prepare cart items in the format expected by the API\n            const cartItems = items.map((item)=>({\n                    ProductId: item.id,\n                    Quantity: item.quantity,\n                    Price: parseFloat((item.discountPrice || item.price).toFixed(2)),\n                    ItemPriceTotal: parseFloat(((item.discountPrice || item.price) * item.quantity).toFixed(2)),\n                    ItemSubTotal: parseFloat(((item.discountPrice || item.price) * item.quantity).toFixed(2)),\n                    IsShippingFree: true,\n                    ShippingChargesTotal: 0.0,\n                    OrderItemAttributeChargesTotal: 0.0,\n                    DiscountId: (appliedCoupon === null || appliedCoupon === void 0 ? void 0 : appliedCoupon.discountId) || null,\n                    CouponCode: (appliedCoupon === null || appliedCoupon === void 0 ? void 0 : appliedCoupon.code) || \"\",\n                    DiscountedPrice: item.discountPrice ? parseFloat(item.discountPrice.toFixed(2)) : null,\n                    OrderItemDiscountTotal: item.discountPrice ? parseFloat(((item.price - item.discountPrice) * item.quantity).toFixed(2)) : 0.0,\n                    IsDiscountCalculated: false,\n                    ProductAllSelectedAttributes: JSON.stringify(item.attributes || [])\n                }));\n            // Get selected payment method details\n            const selectedPaymentMethodDetails = paymentMethods.find((pm)=>pm.PaymentMethodID === selectedPaymentMethod);\n            const paymentMethodName = (selectedPaymentMethodDetails === null || selectedPaymentMethodDetails === void 0 ? void 0 : selectedPaymentMethodDetails.PaymentMethodName) || \"Unknown Payment Method\";\n            // Check if user is logged in (UserID will be extracted from JWT token)\n            if (!user) {\n                throw new Error(\"User not found. Please login again.\");\n            }\n            // Get country and city names for order note\n            const selectedCountry = countries.find((c)=>c.CountryID.toString() === shippingAddress.countryId);\n            const selectedCity = cities.find((c)=>c.CityID.toString() === shippingAddress.cityId);\n            const countryName = (selectedCountry === null || selectedCountry === void 0 ? void 0 : selectedCountry.CountryName) || (selectedCountry === null || selectedCountry === void 0 ? void 0 : selectedCountry.Name) || \"Unknown Country\";\n            const cityName = (selectedCity === null || selectedCity === void 0 ? void 0 : selectedCity.CityName) || (selectedCity === null || selectedCity === void 0 ? void 0 : selectedCity.Name) || \"Unknown City\";\n            // Prepare order data matching the direct API format (no requestParameters wrapper)\n            // UserID removed - will be auto-injected from JWT token\n            const orderData = {\n                OrderNote: \"Order from WEB app\".concat(appliedCoupon ? \" - Coupon: \".concat(appliedCoupon.code) : \"\").concat(usePoints ? \" - Credit used: \".concat(pointsDiscount) : \"\", \" - Payment: \").concat(paymentMethodName, \" - Address: \").concat(shippingAddress.address, \", \").concat(countryName, \", \").concat(cityName),\n                cartJsonData: JSON.stringify(cartItems),\n                OrderTotal: parseFloat(finalTotal.toFixed(2)),\n                CouponCode: (appliedCoupon === null || appliedCoupon === void 0 ? void 0 : appliedCoupon.code) || \"\",\n                Description: \"Order placed via WEB checkout - Payment Method: \".concat(paymentMethodName),\n                StripeStatus: \"\",\n                StripeResponseJson: \"\",\n                StripeBalanceTransactionId: \"\",\n                StripeChargeId: \"\",\n                PayPalResponseJson: \"\",\n                CurrencyCode: \"USD\",\n                PaymentMethod: selectedPaymentMethod,\n                Point: usePoints && pointsDiscount > 0 ? pointsDiscount : null,\n                addressid: selectedAddressId || 1\n            };\n            // Log order data for debugging\n            console.log(\"Order data being sent:\", orderData);\n            // Prepare headers with JWT token\n            const orderHeaders = {\n                \"Content-Type\": \"application/json\",\n                Accept: \"application/json\"\n            };\n            // Add JWT token to headers if available\n            if (token) {\n                orderHeaders[\"Authorization\"] = \"Bearer \".concat(token);\n                console.log(\"🔐 Added JWT token to order placement request\");\n            }\n            // Make API call to place order through our API route\n            const response = await fetch(\"/api/orders/post-order\", {\n                method: \"POST\",\n                headers: orderHeaders,\n                body: JSON.stringify(orderData)\n            });\n            const responseData = await response.json();\n            console.log(\"Order response:\", responseData);\n            if (response.ok && responseData.data) {\n                // Handle the new direct endpoint response format\n                const orderInfo = responseData.data;\n                // Check if order was placed successfully\n                const isSuccess = orderInfo && (orderInfo.message === \"Order Placed Successfully\" || orderInfo.orderID);\n                if (isSuccess) {\n                    // Show beautiful success alert\n                    sweetalert2__WEBPACK_IMPORTED_MODULE_14___default().fire({\n                        title: \"🎉 Order Placed Successfully!\",\n                        html: '\\n              <div style=\"text-align: center; padding: 20px;\">\\n                <div style=\"font-size: 48px; margin-bottom: 20px;\">✅</div>\\n                <h3 style=\"color: #10B981; margin-bottom: 15px;\">Thank you for your order!</h3>\\n                '.concat(orderInfo.orderID ? \"<p><strong>Order ID:</strong> #\".concat(orderInfo.orderID, \"</p>\") : \"\", \"\\n                \").concat(orderInfo.orderNumber ? \"<p><strong>Order Number:</strong> \".concat(orderInfo.orderNumber, \"</p>\") : \"\", '\\n                <p style=\"margin-top: 15px; color: #6B7280;\">\\n                  <strong>Total:</strong> $').concat(finalTotal.toFixed(2), '\\n                </p>\\n                <p style=\"color: #6B7280;\">\\n                  We will contact you soon.\\n                </p>\\n              </div>\\n            '),\n                        icon: \"success\",\n                        confirmButtonText: \"View My Orders\",\n                        confirmButtonColor: primaryColor,\n                        showCancelButton: true,\n                        cancelButtonText: \"Continue Shopping\",\n                        allowOutsideClick: false,\n                        showCloseButton: false,\n                        customClass: {\n                            popup: \"swal-wide\",\n                            actions: \"swal-actions-visible\"\n                        },\n                        didOpen: ()=>{\n                            // Add custom styles for the success alert\n                            const style = document.createElement(\"style\");\n                            style.textContent = \"\\n                .swal-wide {\\n                  width: 600px !important;\\n                  max-width: 90vw !important;\\n                }\\n                .swal2-html-container {\\n                  font-family: inherit !important;\\n                }\\n                .swal-actions-visible .swal2-actions {\\n                  display: flex !important;\\n                  justify-content: center !important;\\n                  gap: 10px !important;\\n                  margin-top: 20px !important;\\n                  visibility: visible !important;\\n                  opacity: 1 !important;\\n                }\\n                .swal2-confirm, .swal2-cancel {\\n                  display: inline-block !important;\\n                  margin: 0 5px !important;\\n                  visibility: visible !important;\\n                  opacity: 1 !important;\\n                  background-color: #6B7280 !important;\\n                  color: white !important;\\n                  border: none !important;\\n                  padding: 10px 20px !important;\\n                  border-radius: 6px !important;\\n                  cursor: pointer !important;\\n                }\\n                .swal2-confirm {\\n                  background-color: \".concat(primaryColor, \" !important;\\n                }\\n                .swal2-cancel:hover, .swal2-confirm:hover {\\n                  opacity: 0.9 !important;\\n                  transform: translateY(-1px) !important;\\n                }\\n              \");\n                            document.head.appendChild(style);\n                        }\n                    }).then((result)=>{\n                        clearCart();\n                        // Clear credit usage from localStorage\n                        localStorage.removeItem(\"usePoints\");\n                        if (result.isConfirmed) {\n                            router.push(\"/orders\");\n                        } else {\n                            router.push(\"/\");\n                        }\n                    });\n                } else {\n                    throw new Error((orderInfo === null || orderInfo === void 0 ? void 0 : orderInfo.message) || \"Order placement failed\");\n                }\n            } else {\n                var _responseData_details;\n                // Log the full response for debugging\n                console.error(\"Order placement failed. Response:\", responseData);\n                throw new Error((responseData === null || responseData === void 0 ? void 0 : responseData.error) || (responseData === null || responseData === void 0 ? void 0 : (_responseData_details = responseData.details) === null || _responseData_details === void 0 ? void 0 : _responseData_details.ErrorMessage) || \"Order placement failed\");\n            }\n        } catch (error) {\n            console.error(\"Error placing order:\", error);\n            // Show more specific error message\n            const errorMessage = error instanceof Error ? error.message : \"There was an error processing your order. Please try again.\";\n            sweetalert2__WEBPACK_IMPORTED_MODULE_14___default().fire({\n                title: \"Order Failed\",\n                text: errorMessage,\n                icon: \"error\",\n                confirmButtonText: \"Try Again\",\n                footer: \"If the problem persists, please contact support.\"\n            });\n        }\n    };\n    // Show loading state while checking authentication\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto py-8 px-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                        lineNumber: 649,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-muted-foreground\",\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                        lineNumber: 650,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                lineNumber: 648,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n            lineNumber: 647,\n            columnNumber: 7\n        }, this);\n    }\n    if (items.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto py-8 px-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-2xl font-bold mb-4\",\n                        children: \"Your cart is empty\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                        lineNumber: 660,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-muted-foreground mb-4\",\n                        children: \"Add items to your cart to proceed to checkout\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                        lineNumber: 661,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                        asChild: true,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_13___default()), {\n                            href: \"/\",\n                            children: \"Continue Shopping\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                            lineNumber: 665,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                        lineNumber: 664,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                lineNumber: 659,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n            lineNumber: 658,\n            columnNumber: 7\n        }, this);\n    }\n    // Show login prompt if not authenticated\n    if (!isLoggedIn) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto py-8 px-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_9__.Card, {\n                className: \"max-w-md mx-auto\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-8 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CreditCard_DollarSign_LogIn_Star_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                className: \"h-8 w-8 text-primary\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                lineNumber: 679,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                            lineNumber: 678,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-medium mb-2\",\n                            children: \"Login Required\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                            lineNumber: 681,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-muted-foreground mb-4\",\n                            children: \"Please log in to continue with checkout\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                            lineNumber: 682,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                            asChild: true,\n                            className: \"w-full\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_13___default()), {\n                                href: \"/login\",\n                                children: \"Login to Continue\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                lineNumber: 686,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                            lineNumber: 685,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                    lineNumber: 677,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                lineNumber: 676,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n            lineNumber: 675,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto py-4 sm:py-6 lg:py-8 px-3 sm:px-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_12__.Breadcrumb, {\n                className: \"mb-3 sm:mb-4 lg:mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_12__.BreadcrumbList, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_12__.BreadcrumbItem, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_12__.BreadcrumbLink, {\n                                asChild: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_13___default()), {\n                                    href: \"/\",\n                                    children: \"Home\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                    lineNumber: 700,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                lineNumber: 699,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                            lineNumber: 698,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_12__.BreadcrumbSeparator, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                            lineNumber: 703,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_12__.BreadcrumbItem, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_12__.BreadcrumbLink, {\n                                asChild: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_13___default()), {\n                                    href: \"/cart\",\n                                    children: \"Cart\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                    lineNumber: 706,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                lineNumber: 705,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                            lineNumber: 704,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_12__.BreadcrumbSeparator, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                            lineNumber: 709,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_12__.BreadcrumbItem, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_12__.BreadcrumbPage, {\n                                children: \"Checkout\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                lineNumber: 711,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                            lineNumber: 710,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                    lineNumber: 697,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                lineNumber: 696,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col lg:grid lg:grid-cols-3 gap-3 lg:gap-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-2 order-1 lg:order-none\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_9__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                    id: \"checkout-form\",\n                                    onSubmit: handleSubmit,\n                                    className: \"p-3 sm:p-4 lg:p-6 space-y-3 sm:space-y-4 lg:space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2 sm:space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-base sm:text-lg font-bold flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CreditCard_DollarSign_LogIn_Star_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                            lineNumber: 727,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"Account Information\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                    lineNumber: 726,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-gray-50 rounded-lg p-2 sm:p-3\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-2 gap-2 sm:gap-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-muted-foreground\",\n                                                                        children: \"Name\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                                        lineNumber: 733,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"font-medium break-words\",\n                                                                        children: [\n                                                                            user === null || user === void 0 ? void 0 : user.FirstName,\n                                                                            \" \",\n                                                                            user === null || user === void 0 ? void 0 : user.LastName\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                                        lineNumber: 734,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                                lineNumber: 732,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-muted-foreground\",\n                                                                        children: \"Email\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                                        lineNumber: 739,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"font-medium break-all\",\n                                                                        children: (user === null || user === void 0 ? void 0 : user.Email) || (user === null || user === void 0 ? void 0 : user.EmailAddress)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                                        lineNumber: 740,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                                lineNumber: 738,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-muted-foreground\",\n                                                                        children: \"Phone\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                                        lineNumber: 745,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"font-medium break-words\",\n                                                                        children: (user === null || user === void 0 ? void 0 : user.PhoneNumber) || (user === null || user === void 0 ? void 0 : user.PhoneNo) || (user === null || user === void 0 ? void 0 : user.MobileNo)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                                        lineNumber: 746,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                                lineNumber: 744,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-muted-foreground\",\n                                                                        children: \"User ID\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                                        lineNumber: 751,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"font-medium\",\n                                                                        children: [\n                                                                            \"#\",\n                                                                            (user === null || user === void 0 ? void 0 : user.UserID) || (user === null || user === void 0 ? void 0 : user.UserId)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                                        lineNumber: 752,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                                lineNumber: 750,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                        lineNumber: 731,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                    lineNumber: 730,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                            lineNumber: 725,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2 sm:space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-col sm:flex-row sm:justify-between sm:items-center gap-2 sm:gap-0\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                            className: \"text-base sm:text-lg font-bold\",\n                                                            children: \"Shipping Address\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                            lineNumber: 762,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                                            type: \"button\",\n                                                            variant: \"outline\",\n                                                            size: \"sm\",\n                                                            onClick: ()=>router.push(\"/addresses\"),\n                                                            className: \"text-xs sm:text-sm w-full sm:w-auto\",\n                                                            children: \"Manage Addresses\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                            lineNumber: 765,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                    lineNumber: 761,\n                                                    columnNumber: 17\n                                                }, this),\n                                                loadingAddresses && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                                            children: \"Loading saved addresses...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                            lineNumber: 779,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                1,\n                                                                2,\n                                                                3\n                                                            ].map((i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"p-3 border rounded-lg animate-pulse\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-start justify-between\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex-1 space-y-2\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"h-4 bg-gray-200 rounded w-16\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                                                        lineNumber: 788,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"h-4 bg-gray-200 rounded w-3/4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                                                        lineNumber: 789,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"h-3 bg-gray-200 rounded w-1/2\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                                                        lineNumber: 790,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                                                lineNumber: 787,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"w-4 h-4 bg-gray-200 rounded-full\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                                                lineNumber: 792,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                                        lineNumber: 786,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, i, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                                    lineNumber: 782,\n                                                                    columnNumber: 25\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                            lineNumber: 780,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                    lineNumber: 778,\n                                                    columnNumber: 19\n                                                }, this),\n                                                !loadingAddresses && savedAddresses.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                                                    className: \"text-sm sm:text-base font-semibold\",\n                                                                    children: \"Select Delivery Address\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                                    lineNumber: 804,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs sm:text-sm text-gray-500\",\n                                                                    children: [\n                                                                        savedAddresses.length,\n                                                                        \" saved address\",\n                                                                        savedAddresses.length !== 1 ? \"es\" : \"\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                                    lineNumber: 807,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                            lineNumber: 803,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: savedAddresses.map((address)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"relative p-3 sm:p-4 border-2 rounded-lg cursor-pointer transition-all duration-200 hover:shadow-md \".concat(selectedAddressId === address.AddressID ? \"border-primary bg-primary/5 shadow-sm\" : \"border-gray-200 hover:border-gray-300\"),\n                                                                    onClick: ()=>handleAddressSelection(address.AddressID),\n                                                                    children: [\n                                                                        selectedAddressId === address.AddressID && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"absolute top-3 right-3\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"w-6 h-6 bg-primary rounded-full flex items-center justify-center\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                    className: \"w-4 h-4 text-white\",\n                                                                                    fill: \"currentColor\",\n                                                                                    viewBox: \"0 0 20 20\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                        fillRule: \"evenodd\",\n                                                                                        d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                                                        clipRule: \"evenodd\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                                                        lineNumber: 834,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                                                    lineNumber: 829,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                                                lineNumber: 828,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                                            lineNumber: 827,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-start gap-3\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex-shrink-0 mt-1\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center\",\n                                                                                        children: address.AddressTypeID === 1 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                            className: \"w-4 h-4 text-primary\",\n                                                                                            fill: \"currentColor\",\n                                                                                            viewBox: \"0 0 20 20\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                                d: \"M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                                                                lineNumber: 854,\n                                                                                                columnNumber: 37\n                                                                                            }, this)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                                                            lineNumber: 849,\n                                                                                            columnNumber: 35\n                                                                                        }, this) : address.AddressTypeID === 2 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                            className: \"w-4 h-4 text-primary\",\n                                                                                            fill: \"currentColor\",\n                                                                                            viewBox: \"0 0 20 20\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                                d: \"M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4zM18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                                                                lineNumber: 862,\n                                                                                                columnNumber: 37\n                                                                                            }, this)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                                                            lineNumber: 857,\n                                                                                            columnNumber: 35\n                                                                                        }, this) : address.AddressTypeID === 3 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                            className: \"w-4 h-4 text-primary\",\n                                                                                            fill: \"currentColor\",\n                                                                                            viewBox: \"0 0 20 20\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                                fillRule: \"evenodd\",\n                                                                                                d: \"M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z\",\n                                                                                                clipRule: \"evenodd\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                                                                lineNumber: 870,\n                                                                                                columnNumber: 37\n                                                                                            }, this)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                                                            lineNumber: 865,\n                                                                                            columnNumber: 35\n                                                                                        }, this) : address.AddressTypeID === 4 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                            className: \"w-4 h-4 text-primary\",\n                                                                                            fill: \"currentColor\",\n                                                                                            viewBox: \"0 0 20 20\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                                    d: \"M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                                                                    lineNumber: 882,\n                                                                                                    columnNumber: 37\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                                    d: \"M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                                                                    lineNumber: 883,\n                                                                                                    columnNumber: 37\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                                                            lineNumber: 877,\n                                                                                            columnNumber: 35\n                                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                            className: \"w-4 h-4 text-primary\",\n                                                                                            fill: \"currentColor\",\n                                                                                            viewBox: \"0 0 20 20\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                                fillRule: \"evenodd\",\n                                                                                                d: \"M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z\",\n                                                                                                clipRule: \"evenodd\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                                                                lineNumber: 891,\n                                                                                                columnNumber: 37\n                                                                                            }, this)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                                                            lineNumber: 886,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                                                        lineNumber: 847,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                                                    lineNumber: 846,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex-1 min-w-0\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center gap-2 mb-2\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    className: \"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary/10 text-primary break-words\",\n                                                                                                    children: address.AddressTypeName || (address.AddressTypeID === 1 ? \"Home\" : address.AddressTypeID === 2 ? \"Billing\" : address.AddressTypeID === 3 ? \"Shipping\" : address.AddressTypeID === 4 ? \"Mailing\" : \"Other\")\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                                                                    lineNumber: 904,\n                                                                                                    columnNumber: 33\n                                                                                                }, this),\n                                                                                                address.IsDefault && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    className: \"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800\",\n                                                                                                    children: \"Default\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                                                                    lineNumber: 917,\n                                                                                                    columnNumber: 35\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                                                            lineNumber: 903,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"font-medium text-gray-900 mb-1 break-words\",\n                                                                                            children: address.AddressLineOne\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                                                            lineNumber: 922,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        address.AddressLineTwo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"text-sm text-gray-600 mb-1 break-words\",\n                                                                                            children: address.AddressLineTwo\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                                                            lineNumber: 926,\n                                                                                            columnNumber: 33\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center gap-2 text-sm text-gray-600\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    children: address.CityName || \"City\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                                                                    lineNumber: 931,\n                                                                                                    columnNumber: 33\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    children: \"•\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                                                                    lineNumber: 932,\n                                                                                                    columnNumber: 33\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    children: address.CountryName\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                                                                    lineNumber: 933,\n                                                                                                    columnNumber: 33\n                                                                                                }, this),\n                                                                                                address.PostalCode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                            children: \"•\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                                                                            lineNumber: 936,\n                                                                                                            columnNumber: 37\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                            children: address.PostalCode\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                                                                            lineNumber: 937,\n                                                                                                            columnNumber: 37\n                                                                                                        }, this)\n                                                                                                    ]\n                                                                                                }, void 0, true)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                                                            lineNumber: 930,\n                                                                                            columnNumber: 31\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                                                    lineNumber: 902,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                                            lineNumber: 844,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, address.AddressID, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                                    lineNumber: 814,\n                                                                    columnNumber: 25\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                            lineNumber: 812,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                    lineNumber: 802,\n                                                    columnNumber: 19\n                                                }, this),\n                                                !loadingAddresses && savedAddresses.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center py-8 border-2 border-dashed border-gray-200 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-12 h-12 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-6 h-6 text-gray-400\",\n                                                                fill: \"none\",\n                                                                stroke: \"currentColor\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                                        lineNumber: 959,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M15 11a3 3 0 11-6 0 3 3 0 016 0z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                                        lineNumber: 965,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                                lineNumber: 953,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                            lineNumber: 952,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-medium text-gray-900 mb-2\",\n                                                            children: \"No saved addresses\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                            lineNumber: 973,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-500 mb-4\",\n                                                            children: \"Please add a delivery address in your account settings to continue with checkout.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                            lineNumber: 976,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                                            asChild: true,\n                                                            variant: \"outline\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_13___default()), {\n                                                                href: \"/addresses\",\n                                                                children: \"Manage Addresses\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                                lineNumber: 981,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                            lineNumber: 980,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                    lineNumber: 951,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                            lineNumber: 760,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2 sm:space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-base sm:text-lg font-bold\",\n                                                    children: \"Payment Method\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                    lineNumber: 988,\n                                                    columnNumber: 17\n                                                }, this),\n                                                loadingPaymentMethods ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 gap-2 sm:gap-3\",\n                                                    children: [\n                                                        ...Array(6)\n                                                    ].map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex flex-col items-center p-2 sm:p-4 border rounded-md\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-12 h-12 rounded-full bg-gray-200 animate-pulse mb-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                                    lineNumber: 999,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"h-4 w-20 bg-gray-200 rounded animate-pulse mb-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                                    lineNumber: 1000,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-4 h-4 rounded-full border bg-gray-200 animate-pulse\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                                    lineNumber: 1001,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                            lineNumber: 995,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                    lineNumber: 993,\n                                                    columnNumber: 19\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 gap-2 sm:gap-3\",\n                                                    children: paymentMethods.map((method)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex flex-col items-center p-2 sm:p-4 border rounded-md cursor-pointer hover:bg-muted/50 transition-colors relative\",\n                                                            onClick: ()=>setSelectedPaymentMethod(method.PaymentMethodID),\n                                                            style: {\n                                                                borderColor: selectedPaymentMethod === method.PaymentMethodID ? primaryColor : \"\"\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"absolute top-3 right-3 w-5 h-5 rounded-full border flex items-center justify-center\",\n                                                                    style: {\n                                                                        borderColor: primaryColor\n                                                                    },\n                                                                    children: selectedPaymentMethod === method.PaymentMethodID && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-3 h-3 rounded-full\",\n                                                                        style: {\n                                                                            backgroundColor: primaryColor\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                                        lineNumber: 1027,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                                    lineNumber: 1022,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-12 h-12 rounded-full flex items-center justify-center overflow-hidden mb-3\",\n                                                                    style: {\n                                                                        backgroundColor: \"\".concat(primaryColor, \"20\")\n                                                                    },\n                                                                    children: [\n                                                                        method.ImageUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                            src: method.ImageUrl,\n                                                                            alt: method.PaymentMethodName,\n                                                                            className: \"w-full h-full object-cover\",\n                                                                            onError: (e)=>{\n                                                                                // Fallback to icon if image fails to load\n                                                                                e.currentTarget.style.display = \"none\";\n                                                                                const nextElement = e.currentTarget.nextElementSibling;\n                                                                                if (nextElement) {\n                                                                                    nextElement.style.display = \"block\";\n                                                                                }\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                                            lineNumber: 1040,\n                                                                            columnNumber: 29\n                                                                        }, this) : null,\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CreditCard_DollarSign_LogIn_Star_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                            className: \"h-6 w-6\",\n                                                                            style: {\n                                                                                color: primaryColor,\n                                                                                display: method.ImageUrl ? \"none\" : \"block\"\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                                            lineNumber: 1055,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                                    lineNumber: 1035,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-medium text-sm break-words text-center\",\n                                                                            children: method.PaymentMethodName\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                                            lineNumber: 1066,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        method.Description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-muted-foreground mt-1 break-words text-center\",\n                                                                            children: method.Description\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                                            lineNumber: 1070,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                                    lineNumber: 1065,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, method.PaymentMethodID, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                            lineNumber: 1008,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                    lineNumber: 1006,\n                                                    columnNumber: 19\n                                                }, this),\n                                                selectedPaymentMethod && (()=>{\n                                                    const selectedMethod = paymentMethods.find((pm)=>pm.PaymentMethodID === selectedPaymentMethod);\n                                                    if (selectedMethod) {\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_payment_method_details__WEBPACK_IMPORTED_MODULE_11__.PaymentMethodDetails, {\n                                                            paymentMethodName: selectedMethod.PaymentMethodName,\n                                                            paymentMethodId: selectedMethod.PaymentMethodID\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                            lineNumber: 1088,\n                                                            columnNumber: 25\n                                                        }, this);\n                                                    }\n                                                    return null;\n                                                })(),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_13___default()), {\n                                                        href: \"/payment-methods\",\n                                                        className: \"text-sm text-primary hover:underline\",\n                                                        children: \"View detailed payment instructions\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                        lineNumber: 1098,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                    lineNumber: 1097,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                            lineNumber: 987,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                    lineNumber: 719,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3 sm:p-4 lg:p-6 pt-0\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                        type: \"submit\",\n                                        form: \"checkout-form\",\n                                        className: \"w-full hidden lg:block\",\n                                        style: {\n                                            backgroundColor: primaryColor,\n                                            color: \"white\"\n                                        },\n                                        disabled: !selectedPaymentMethod || !selectedAddressId && !shippingAddress.address,\n                                        children: \"Place Order\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                        lineNumber: 1110,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                    lineNumber: 1109,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                            lineNumber: 718,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                        lineNumber: 717,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-1 order-2 lg:order-none\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_9__.Card, {\n                            className: \"lg:sticky lg:top-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-3 sm:p-4 lg:p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 sm:gap-0 mb-3 sm:mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-base sm:text-lg font-bold\",\n                                                children: \"Order Summary\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                lineNumber: 1130,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-1 w-full sm:w-auto\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#\",\n                                                        onClick: (e)=>{\n                                                            e.preventDefault();\n                                                            e.stopPropagation();\n                                                            const orderInfo = \"Order Summary:\\nSubtotal: \".concat(showUSD ? formatUSD(subtotal) : formatIQD(subtotalIQD)).concat(appliedCoupon ? \"\\nCoupon (\".concat(appliedCoupon.code, \"): -\").concat(showUSD ? formatUSD(appliedCoupon.discount) : formatIQD(Math.round(appliedCoupon.discount * 1500))) : '').concat(usePoints && pointsDiscount > 0 ? \"\\nCredit Discount: -$\".concat(pointsDiscount.toFixed(2)) : '', \"\\nTotal: \").concat(showUSD ? formatUSD(finalTotal) : formatIQD(finalTotalIQD));\n                                                            navigator.clipboard.writeText(orderInfo);\n                                                            const link = e.target;\n                                                            const originalText = link.textContent;\n                                                            link.textContent = 'Copied!';\n                                                            setTimeout(()=>{\n                                                                link.textContent = originalText;\n                                                            }, 2000);\n                                                        },\n                                                        className: \"inline-block px-3 py-1.5 bg-gray-50 hover:bg-gray-100 text-gray-600 hover:text-gray-800 border border-gray-200 rounded-md text-xs font-medium transition-colors duration-200 mr-2 no-underline cursor-pointer\",\n                                                        children: \"\\uD83D\\uDCCB Copy Summary\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                        lineNumber: 1134,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                                        variant: showUSD ? \"outline\" : \"default\",\n                                                        size: \"sm\",\n                                                        onClick: ()=>setShowUSD(false),\n                                                        className: \"text-xs px-2 flex-1 sm:flex-none\",\n                                                        children: \"IQD\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                        lineNumber: 1152,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                                        variant: showUSD ? \"default\" : \"outline\",\n                                                        size: \"sm\",\n                                                        onClick: ()=>setShowUSD(true),\n                                                        className: \"text-xs px-2 flex-1 sm:flex-none\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CreditCard_DollarSign_LogIn_Star_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                className: \"h-3 w-3 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                                lineNumber: 1166,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"USD\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                        lineNumber: 1160,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                lineNumber: 1133,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                        lineNumber: 1129,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2 sm:space-y-3 mb-4 sm:mb-6\",\n                                        children: items.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-start\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-start gap-2 sm:gap-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-12 h-12 sm:w-16 sm:h-16 bg-muted rounded-md overflow-hidden flex-shrink-0\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                    src: item.image || \"/products/book\".concat(item.id, \".jpg\"),\n                                                                    alt: item.name,\n                                                                    className: \"w-full h-full object-cover\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                                    lineNumber: 1180,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                                lineNumber: 1179,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-1 min-w-0\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"font-medium text-xs sm:text-sm break-words\",\n                                                                        children: item.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                                        lineNumber: 1187,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-muted-foreground break-words\",\n                                                                        children: [\n                                                                            \"Qty: \",\n                                                                            item.quantity\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                                        lineNumber: 1190,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    item.attributes && item.attributes.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"mt-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-xs font-medium text-gray-600 mb-1\",\n                                                                                children: \"Selected Options:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                                                lineNumber: 1197,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"space-y-1\",\n                                                                                children: item.attributes.map((attr, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-xs text-gray-500 break-words\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                className: \"font-medium\",\n                                                                                                children: [\n                                                                                                    attr.DisplayName || attr.AttributeName,\n                                                                                                    \":\"\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                                                                lineNumber: 1206,\n                                                                                                columnNumber: 35\n                                                                                            }, this),\n                                                                                            \" \",\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                children: attr.AttributeValueText\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                                                                lineNumber: 1209,\n                                                                                                columnNumber: 35\n                                                                                            }, this),\n                                                                                            attr.PriceAdjustment && attr.PriceAdjustment !== 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                className: \"text-green-600 ml-1\",\n                                                                                                children: [\n                                                                                                    \"(\",\n                                                                                                    attr.PriceAdjustmentType === 1 ? \"+\" : \"\",\n                                                                                                    attr.PriceAdjustmentType === 1 ? formatUSD(attr.PriceAdjustment) : \"+\".concat(attr.PriceAdjustment, \"%\"),\n                                                                                                    \")\"\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                                                                lineNumber: 1212,\n                                                                                                columnNumber: 39\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, index, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                                                        lineNumber: 1202,\n                                                                                        columnNumber: 33\n                                                                                    }, this))\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                                                lineNumber: 1200,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                                        lineNumber: 1196,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                                lineNumber: 1186,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                        lineNumber: 1178,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium text-sm\",\n                                                        children: showUSD ? formatUSD(item.adjustedPrice || item.discountPrice || item.price) : formatIQD(item.adjustedIqdPrice || item.iqdPrice || Math.round((item.discountPrice || item.price) * 1500))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                        lineNumber: 1230,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, item.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                lineNumber: 1174,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                        lineNumber: 1172,\n                                        columnNumber: 15\n                                    }, this),\n                                    (selectedAddressId || shippingAddress.address) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"border-t pt-4 mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-medium mb-2 flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 h-4 text-primary\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                                lineNumber: 1259,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M15 11a3 3 0 11-6 0 3 3 0 016 0z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                                lineNumber: 1265,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                        lineNumber: 1253,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"Delivery Address\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                lineNumber: 1252,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-50 rounded-lg p-3\",\n                                                children: selectedAddressId ? (()=>{\n                                                    const selectedAddress = savedAddresses.find((addr)=>addr.AddressID === selectedAddressId);\n                                                    return selectedAddress ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2 mb-1\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary/10 text-primary break-words\",\n                                                                    children: selectedAddress.AddressTypeName || (selectedAddress.AddressTypeID === 1 ? \"Home\" : selectedAddress.AddressTypeID === 2 ? \"Billing\" : selectedAddress.AddressTypeID === 3 ? \"Shipping\" : selectedAddress.AddressTypeID === 4 ? \"Mailing\" : \"Other\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                                    lineNumber: 1283,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                                lineNumber: 1282,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium text-gray-900 break-words\",\n                                                                children: selectedAddress.AddressLineOne\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                                lineNumber: 1296,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            selectedAddress.AddressLineTwo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-600 break-words\",\n                                                                children: selectedAddress.AddressLineTwo\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                                lineNumber: 1300,\n                                                                columnNumber: 31\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-600 break-words\",\n                                                                children: [\n                                                                    selectedAddress.CityName || \"City\",\n                                                                    \" •\",\n                                                                    \" \",\n                                                                    selectedAddress.CountryName,\n                                                                    selectedAddress.PostalCode && \" • \".concat(selectedAddress.PostalCode)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                                lineNumber: 1304,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                        lineNumber: 1281,\n                                                        columnNumber: 27\n                                                    }, this) : null;\n                                                })() : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"font-medium text-gray-900\",\n                                                            children: shippingAddress.address\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                            lineNumber: 1315,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600\",\n                                                            children: [\n                                                                ((_cities_find = cities.find((c)=>c.CityID.toString() === shippingAddress.cityId)) === null || _cities_find === void 0 ? void 0 : _cities_find.CityName) || \"City\",\n                                                                \" \",\n                                                                \"•\",\n                                                                \" \",\n                                                                ((_countries_find = countries.find((c)=>c.CountryID.toString() === shippingAddress.countryId)) === null || _countries_find === void 0 ? void 0 : _countries_find.CountryName) || \"Country\",\n                                                                shippingAddress.zipCode && \" • \".concat(shippingAddress.zipCode)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                            lineNumber: 1318,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                    lineNumber: 1314,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                lineNumber: 1274,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                        lineNumber: 1251,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3 border-t pt-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-muted-foreground\",\n                                                        children: \"Subtotal\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                        lineNumber: 1341,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: showUSD ? formatUSD(subtotal) : formatIQD(subtotalIQD)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                        lineNumber: 1342,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                lineNumber: 1340,\n                                                columnNumber: 17\n                                            }, this),\n                                            appliedCoupon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between text-green-600\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"Coupon (\",\n                                                            appliedCoupon.code,\n                                                            \")\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                        lineNumber: 1350,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"-\",\n                                                            showUSD ? formatUSD(appliedCoupon.discount) : formatIQD(Math.round(appliedCoupon.discount * 1500))\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                        lineNumber: 1351,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                lineNumber: 1349,\n                                                columnNumber: 19\n                                            }, this),\n                                            usePoints && pointsDiscount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between text-yellow-600\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"flex items-center gap-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CreditCard_DollarSign_LogIn_Star_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                className: \"h-3 w-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                                lineNumber: 1364,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \"Credit Discount\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                        lineNumber: 1363,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"-$\",\n                                                            pointsDiscount.toFixed(2)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                        lineNumber: 1367,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                lineNumber: 1362,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-t pt-3 mt-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-lg font-bold\",\n                                                            children: \"Total\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                            lineNumber: 1374,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-right\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-xl font-bold\",\n                                                                            style: {\n                                                                                color: primaryColor\n                                                                            },\n                                                                            children: showUSD ? formatUSD(finalTotal) : formatIQD(finalTotalIQD)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                                            lineNumber: 1377,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                            href: \"#\",\n                                                                            onClick: (e)=>{\n                                                                                e.preventDefault();\n                                                                                e.stopPropagation();\n                                                                                const totalAmount = showUSD ? formatUSD(finalTotal) : formatIQD(finalTotalIQD);\n                                                                                navigator.clipboard.writeText(totalAmount);\n                                                                                const link = e.target;\n                                                                                const originalText = link.textContent;\n                                                                                link.textContent = 'Copied!';\n                                                                                setTimeout(()=>{\n                                                                                    link.textContent = originalText;\n                                                                                }, 2000);\n                                                                            },\n                                                                            className: \"inline-block px-3 py-1.5 bg-gray-50 hover:bg-gray-100 text-gray-600 hover:text-gray-800 border border-gray-200 rounded-md text-xs font-medium transition-colors duration-200 no-underline cursor-pointer\",\n                                                                            children: \"\\uD83D\\uDCCB Copy\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                                            lineNumber: 1385,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                                    lineNumber: 1376,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-muted-foreground\",\n                                                                    children: [\n                                                                        \"≈\",\n                                                                        \" \",\n                                                                        showUSD ? formatIQD(finalTotalIQD) : formatUSD(finalTotal)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                                    lineNumber: 1406,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                            lineNumber: 1375,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                    lineNumber: 1373,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                lineNumber: 1372,\n                                                columnNumber: 17\n                                            }, this),\n                                            (appliedCoupon || usePoints && pointsDiscount > 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-green-50 rounded-lg p-3 border border-green-200\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-green-700\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"font-medium mb-1\",\n                                                            children: \"\\uD83D\\uDCB0 Total Savings:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                            lineNumber: 1420,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"font-bold\",\n                                                            children: showUSD ? formatUSD((appliedCoupon ? appliedCoupon.discount : 0) + pointsDiscount) : formatIQD((appliedCoupon ? Math.round(appliedCoupon.discount * 1500) : 0) + pointsDiscountIQD)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                            lineNumber: 1421,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                    lineNumber: 1419,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                                lineNumber: 1418,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                        lineNumber: 1338,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                lineNumber: 1128,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                            lineNumber: 1127,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                        lineNumber: 1126,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:hidden order-3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_9__.Card, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                    type: \"submit\",\n                                    form: \"checkout-form\",\n                                    className: \"w-full\",\n                                    style: {\n                                        backgroundColor: primaryColor,\n                                        color: \"white\"\n                                    },\n                                    disabled: !selectedPaymentMethod || !selectedAddressId && !shippingAddress.address,\n                                    children: \"Place Order\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                    lineNumber: 1445,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                                lineNumber: 1444,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                            lineNumber: 1443,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                        lineNumber: 1442,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n                lineNumber: 716,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\",\n        lineNumber: 695,\n        columnNumber: 5\n    }, this);\n}\n_s(CheckoutPage, \"Ee1P3vYFnv8mqQMWYM8NgFHtAR8=\", false, function() {\n    return [\n        _contexts_settings_context__WEBPACK_IMPORTED_MODULE_7__.useSettings,\n        _contexts_user_context__WEBPACK_IMPORTED_MODULE_4__.useUser,\n        _contexts_cart_context__WEBPACK_IMPORTED_MODULE_3__.useCart,\n        _contexts_coupon_context__WEBPACK_IMPORTED_MODULE_5__.useCoupon,\n        _contexts_currency_context__WEBPACK_IMPORTED_MODULE_6__.useCurrency,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = CheckoutPage;\nvar _c;\n$RefreshReg$(_c, \"CheckoutPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/checkout/page.tsx\n"));

/***/ })

});