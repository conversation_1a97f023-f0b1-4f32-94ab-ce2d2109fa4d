﻿@model Entities.MainModels.DiscountModel


@await Html.PartialAsync("~/Views/Common/_DataTableLength.cshtml")

<table class="table site-table-listing">
    <thead>
        <tr>
            <th id="lbl_hdr_campgId"> Campaign ID</th>
            <th id="lbl_hdr_campgMainTitle">  Main Title</th>
            <th id="lbl_hdr_campgDiscntTitle"> Discount Title</th>
            <th id="lbl_hdr_campgBody"> URL</th>
            <th id="lbl_hdr_campgStatus"> Status</th>
            <th id="lbl_hdr_campgCovrImg"> Cover Picture</th>
            <th id="lbl_hdr_campgStrtDte"> Display StartDate</th>
            <th id="lbl_hdr_campgEndDte"> Display EndDate</th>
            <th id="lbl_hdr_campgCreatedOn"> Created On</th>





            <th class="text-center" style="width: 20px;"><i class="icon-arrow-down12"></i></th>
        </tr>
    </thead>
    <tbody>

        @{
            if (Model != null && Model.DiscountsCampaignList != null && Model.DiscountsCampaignList.Count > 0)
            {
                foreach (var item in Model.DiscountsCampaignList)
                {
                    <tr>
                        <td><span class="text-dark">@item.CampaignId</span></td>
                        <td>
                            <div class="d-flex align-items-center">
                                <div>
                                    <a href="#" class="text-default font-weight-semibold">@item.MainTitle</a>

                                </div>
                            </div>
                        </td>
                        <td><span class="text-dark">@(item.DiscountTitle)</span></td>
                        <td>@(StringConversionHelper.TruncateAnyStringValue(@item.Body , 40 , true))</td>
                        @if (item.IsActive == false)
                        {
                            <td><span class="badge bg-blue">Not Active</span></td>
                        }
                        else
                        {
                            <td><span class="badge bg-success-400">Active</span></td>
                        }


                        <td>

                            @{
                                string ImagePath = System.IO.Path.Combine(Directory.GetCurrentDirectory(), "wwwroot" + item.CoverPictureUrl);

                                if (System.IO.File.Exists(ImagePath))
                                {

                                    <a href="@item.CoverPictureUrl" target="_blank">
                                        <img src="@item.CoverPictureUrl" class="" width="100" height="100" alt="">
                                    </a>
                                }
                                else
                                {
                                    <a href="/content/themeContent/global_assets/images/placeholders/placeholder.jpg" target="_blank">
                                        <img src="/content/themeContent/global_assets/images/placeholders/placeholder.jpg" class="" width="100" height="100" alt="">
                                    </a>
                                }
                            }
                        </td>



                        <td><span class="text-dark">@(item.DisplayStartDate.ToString("dd MMM, yyyy"))</span></td>



                        <td><span class="text-dark">@(item.DisplayEndDate.ToString("dd MMM, yyyy"))</span></td>
                        <td><span class="text-dark">@(item.CreatedOn.ToString("dd MMM, yyyy"))</span></td>


                        <td class="text-center">
                            <div class="list-icons">
                                <div class="dropdown">
                                    <a href="#" class="list-icons-item" data-toggle="dropdown">
                                        <i class="icon-menu9"></i>
                                    </a>

                                    <div class="dropdown-menu dropdown-menu-right">
                                        <a href="#!" class="dropdown-item text-indigo-600" onclick="showEditModal('@item.CampaignId');"><i class="icon-database-edit2"></i>Edit</a>

                                        @{
                                            ListingDeleteButton pageDeleteButton = new ListingDeleteButton
                                            {
                                                EntityId = Model?.PageBasicInfoObj?.EntityId ?? 0,
                                                primaryKeyColumn = "CampaignId",
                                                primarykeyValue = item.CampaignId.ToString(),
                                                tableName = "DiscountsCampaign",
                                                SqlDeleteType = (short)SqlDeleteTypes.PlainTableDelete,
                                            };
                                        }
                                        @await Html.PartialAsync("~/Views/Common/_ListingDeleteButton.cshtml", pageDeleteButton)

                                       
                                    </div>
                                </div>
                            </div>
                        </td>
                    </tr>
                }
            }
            else
            {
                <tr>
                    <td class="text-center" colspan="20"><b>  No record found </b></td>

                </tr>
            }
        }




    </tbody>
</table>



<div class="row" id="pagination_div">

    @{
        if (Model != null && Model.pageHelperObj != null)
        {
            PagerHelper ph = new PagerHelper();
            ph.CurrentPage = Model.pageHelperObj.CurrentPage;
            ph.TotalRecords = Model.pageHelperObj.TotalRecords;
            ph.RecordsPerPage = Model.pageHelperObj.RecordsPerPage;
            ph.EntityId = Model?.PageBasicInfoObj?.EntityId ?? 0;
            ph.PageUrl =   Url.Action("DiscountCampaigns", "Discounts" , new { langCode = Model?.PageBasicInfoObj?.langCode });

            ph.AjaxEnabled = true;
            ph.OnClientClickAjaxCall = "PaginationAfterAjax(this)";

            @await Html.PartialAsync("~/Views/Common/_Pager.cshtml", ph)
        }

    }


</div>


