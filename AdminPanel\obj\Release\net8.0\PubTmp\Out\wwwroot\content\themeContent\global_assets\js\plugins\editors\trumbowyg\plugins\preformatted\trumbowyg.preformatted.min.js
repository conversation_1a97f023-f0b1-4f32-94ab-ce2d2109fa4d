!function(e){"use strict";function t(){var e,t=null;return window.getSelection?(e=window.getSelection(),e.rangeCount&&(t=e.getRangeAt(0).commonAncestorContainer,1!==t.nodeType&&(t=t.parentNode))):(e=document.selection)&&"Control"!==e.type&&(t=e.createRange().parentElement()),t}function r(e){var t=document.createElement("DIV");return t.innerHTML=e,t.textContent||t.innerText||""}function n(){var t=null;if(document.selection)t=document.selection.createRange().parentElement();else{var r=window.getSelection();r.rangeCount>0&&(t=r.getRangeAt(0).startContainer.parentNode)}var n=e(t).contents().closest("pre").length,o=e(t).contents().closest("code").length;n&&o?e(t).contents().unwrap("code").unwrap("pre"):n?e(t).contents().unwrap("pre"):o&&e(t).contents().unwrap("code")}e.extend(!0,e.trumbowyg,{langs:{en:{preformatted:"Code sample <pre>"},da:{preformatted:"Præformateret <pre>"},fr:{preformatted:"Exemple de code <pre>"},it:{preformatted:"Codice <pre>"},zh_cn:{preformatted:"代码示例 <pre>"},ru:{preformatted:"Пример кода <pre>"},ja:{preformatted:"コードサンプル <pre>"},tr:{preformatted:"Kod örneği <pre>"},zh_tw:{preformatted:"代碼範例 <pre>"},pt_br:{preformatted:"Exemple de código <pre>"}},plugins:{preformatted:{init:function(e){var o={fn:function(){e.saveRange();var o=e.getRangeText();if(""!==o.replace(/\s/g,""))try{var a=t().tagName.toLowerCase();if("code"===a||"pre"===a)return n();e.execCmd("insertHTML","<pre><code>"+r(o)+"</code></pre>")}catch(p){}},tag:"pre"};e.addBtnDef("preformatted",o)}}}})}(jQuery);