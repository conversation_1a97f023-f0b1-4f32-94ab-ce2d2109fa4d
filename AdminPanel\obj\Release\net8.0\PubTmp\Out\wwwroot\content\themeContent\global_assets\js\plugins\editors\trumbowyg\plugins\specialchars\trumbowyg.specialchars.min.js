!function(n){"use strict";function s(s){var a=[];return n.each(s.o.plugins.specialchars.symbolList,function(n,e){e=null===e?"&nbsp":"&#x"+e;var r=e.replace(/:/g,""),l="symbol-"+r,c={text:e,fn:function(){var n=String.fromCodePoint(e.replace("&#","0"));return s.execCmd("insertText",n),!0}};s.addBtnDef(l,c),a.push(l)}),a}var a={symbolList:["0024","20AC","00A3","00A2","00A5","00A4","2030",null,"00A9","00AE","2122",null,"00A7","00B6","00C6","00E6","0152","0153",null,"2022","25CF","2023","25B6","2B29","25C6",null,"00B1","00D7","00F7","21D2","21D4","220F","2211","2243","2264","2265"]};n.extend(!0,n.trumbowyg,{langs:{en:{specialChars:"Special characters"},fr:{specialChars:"Caractères spéciaux"}},plugins:{specialchars:{init:function(n){n.o.plugins.specialchars=n.o.plugins.specialchars||a;var e={dropdown:s(n)};n.addBtnDef("specialChars",e)}}}})}(jQuery);