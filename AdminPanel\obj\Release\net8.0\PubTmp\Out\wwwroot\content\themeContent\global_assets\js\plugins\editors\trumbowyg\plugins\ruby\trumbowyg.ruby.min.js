!function(r){"use strict";r.extend(!0,r.trumbowyg,{langs:{en:{ruby:"Add ruby text",rubyModal:"Ruby modal",rubyText:"Ruby text"},da:{ruby:"Tilføj ruby tekst",rubyModal:"Ruby modal",rubyText:"Ruby tekst"},fr:{ruby:"Ajouter du texte ruby",rubyModal:"Modale ruby",rubyText:"Texte ruby"},id:{ruby:"Sisipkan teks ruby",rubyModal:"Modal teks ruby",rubyText:"Teks ruby"},tr:{ruby:"Ruby metni ekle",rubyModal:"Ruby modal",rubyText:"Ruby metni"},zh_tw:{ruby:"加入 ruby 文字",rubyModal:"Ruby 彈跳視窗",rubyText:"Ruby 文字"},pt_br:{ruby:"Adicionar texto ruby",rubyModal:"Modal ruby",rubyText:"Texto ruby"}},plugins:{ruby:{init:function(u){var e={fn:function(){u.saveRange(),u.openModalInsert(u.lang.ruby,{rubyText:{label:u.lang.rubyText,required:!1},modal:{label:u.lang.rubyModal,value:u.getRangeText(),required:!0}},function(e){var b=r('<ruby title="'+e.rubyText+'">'+e.modal+"<rp> (</rp><rt>"+e.rubyText+"</rt><rp>)</rp></ruby>")[0];return u.range.deleteContents(),u.range.insertNode(b),u.syncCode(),u.$c.trigger("tbwchange"),!0})}};u.addBtnDef("ruby",e)}}}})}(jQuery);