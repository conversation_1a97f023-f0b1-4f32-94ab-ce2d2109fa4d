/**
 * Copyright (c) Tiny Technologies, Inc. All rights reserved.
 * Licensed under the LGPL or a commercial license.
 * For LGPL see License.txt in the project root for license information.
 * For commercial licenses see https://www.tiny.cloud/
 *
 * Version: 5.3.0 (2020-05-21)
 */
!function(){"use strict";var n,t,e,r=tinymce.util.Tools.resolve("tinymce.PluginManager"),s=tinymce.util.Tools.resolve("tinymce.util.Tools"),c=function(n,t,e){var r="UL"===t?"InsertUnorderedList":"InsertOrderedList";n.execCommand(r,!1,!1===e?null:{"list-style-type":e})},o=function(n){return function(){return n}},u=o(!1),l=o(!0),i=function(){return a},a=(n=function(n){return n.isNone()},{fold:function(n,t){return n()},is:u,isSome:u,isNone:l,getOr:e=function(n){return n},getOrThunk:t=function(n){return n()},getOrDie:function(n){throw new Error(n||"error: getOrDie called on none.")},getOrNull:o(null),getOrUndefined:o(undefined),or:e,orThunk:t,map:i,each:function(){},bind:i,exists:u,forall:l,filter:i,equals:n,equals_:n,toArray:function(){return[]},toString:o("none()")}),f=function(e){var n=o(e),t=function(){return i},r=function(n){return n(e)},i={fold:function(n,t){return t(e)},is:function(n){return e===n},isSome:l,isNone:u,getOr:n,getOrThunk:n,getOrDie:n,getOrNull:n,getOrUndefined:n,or:t,orThunk:t,map:function(n){return f(n(e))},each:function(n){n(e)},bind:r,exists:r,forall:r,filter:function(n){return n(e)?i:a},toArray:function(){return[e]},toString:function(){return"some("+e+")"},equals:function(n){return n.is(e)},equals_:function(n,t){return n.fold(u,function(n){return t(e,n)})}};return i},d=function(n){return null===n||n===undefined?a:f(n)},g=function(n){return n&&/^(TH|TD)$/.test(n.nodeName)},p=function(r){return function(n){return n&&/^(OL|UL|DL)$/.test(n.nodeName)&&(e=n,(t=r).$.contains(t.getBody(),e));var t,e}},m=function(n,t,e){var r=function(n,t){for(var e=0;e<n.length;e++){if(t(n[e]))return e}return-1}(t.parents,g),i=-1!==r?t.parents.slice(0,r):t.parents,o=s.grep(i,p(n));return 0<o.length&&o[0].nodeName===e},y=function(i,n,t,e,r,o){i.ui.registry.addSplitButton(n,{tooltip:t,icon:"OL"===r?"ordered-list":"unordered-list",presets:"listpreview",columns:3,fetch:function(n){n(s.map(o,function(n){return{type:"choiceitem",value:"default"===n?"":n,icon:"list-"+("OL"===r?"num":"bull")+"-"+("disc"===n||"decimal"===n?"default":n),text:n.replace(/\-/g," ").replace(/\b\w/g,function(n){return n.toUpperCase()})}}))},onAction:function(){return i.execCommand(e)},onItemAction:function(n,t){c(i,r,t)},select:function(t){var n,e,r;return(e=(n=i).dom.getParent(n.selection.getNode(),"ol,ul"),r=n.dom.getStyle(e,"listStyleType"),d(r)).map(function(n){return t===n}).getOr(!1)},onSetup:function(t){var n=function(n){t.setActive(m(i,n,r))};return i.on("NodeChange",n),function(){return i.off("NodeChange",n)}}})},v=function(n,t,e,r,i,o){var u,l,s,c,a;0<o.length?y(n,t,e,r,i,o):(l=t,s=e,c=r,a=i,(u=n).ui.registry.addToggleButton(l,{active:!1,tooltip:s,icon:"OL"===a?"ordered-list":"unordered-list",onSetup:function(t){var n=function(n){t.setActive(m(u,n,a))};return u.on("NodeChange",n),function(){return u.off("NodeChange",n)}},onAction:function(){return u.execCommand(c)}}))};!function O(){r.add("advlist",function(n){var e,t,r,i,o,u,l;u="lists",l=(o=n).settings.plugins?o.settings.plugins:"",-1!==s.inArray(l.split(/[ ,]/),u)&&(v(t=n,"numlist","Numbered list","InsertOrderedList","OL",(r=t.getParam("advlist_number_styles","default,lower-alpha,lower-greek,lower-roman,upper-alpha,upper-roman"))?r.split(/[ ,]/):[]),v(t,"bullist","Bullet list","InsertUnorderedList","UL",(i=t.getParam("advlist_bullet_styles","default,circle,square"))?i.split(/[ ,]/):[]),(e=n).addCommand("ApplyUnorderedListStyle",function(n,t){c(e,"UL",t["list-style-type"])}),e.addCommand("ApplyOrderedListStyle",function(n,t){c(e,"OL",t["list-style-type"])}))})}()}();