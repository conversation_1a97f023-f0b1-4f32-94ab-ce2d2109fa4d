!function(e){"use strict";var t={rows:8,columns:8,styler:"table"};e.extend(!0,e.trumbowyg,{langs:{en:{table:"Insert table",tableAddRow:"Add row",tableAddColumn:"Add column",tableDeleteRow:"Delete row",tableDeleteColumn:"Delete column",tableDestroy:"Delete table",error:"Error"},da:{table:"Indsæt tabel",tableAddRow:"Tilføj række",tableAddColumn:"Tilføj kolonne",tableDeleteRow:"Slet række",tableDeleteColumn:"Slet kolonne",tableDestroy:"Slet tabel",error:"Fejl"},de:{table:"Tabelle einfügen",tableAddRow:"Zeile hinzufügen",tableAddColumn:"Spalte hinzufügen",tableDeleteRow:"<PERSON>eile löschen",tableDeleteColumn:"Spalte löschen",tableDestroy:"Tabelle löschen",error:"Error"},sk:{table:"Vytvoriť tabuľky",tableAddRow:"Pridať riadok",tableAddColumn:"Pridať stĺpec",error:"Chyba"},fr:{table:"Insérer un tableau",tableAddRow:"Ajouter des lignes",tableAddColumn:"Ajouter des colonnes",tableDeleteRow:"Effacer la ligne",tableDeleteColumn:"Effacer la colonne",tableDestroy:"Effacer le tableau",error:"Erreur"},cs:{table:"Vytvořit příkaz Table",tableAddRow:"Přidat řádek",tableAddColumn:"Přidat sloupec",error:"Chyba"},ru:{table:"Вставить таблицу",tableAddRow:"Добавить строку",tableAddColumn:"Добавить столбец",tableDeleteRow:"Удалить строку",tableDeleteColumn:"Удалить столбец",tableDestroy:"Удалить таблицу",error:"Ошибка"},ja:{table:"表の挿入",tableAddRow:"行の追加",tableAddColumn:"列の追加",error:"エラー"},tr:{table:"Tablo ekle",tableAddRow:"Satır ekle",tableAddColumn:"Kolon ekle",error:"Hata"},zh_tw:{table:"插入表格",tableAddRow:"加入行",tableAddColumn:"加入列",tableDeleteRow:"刪除行",tableDeleteColumn:"刪除列",tableDestroy:"刪除表格",error:"錯誤"},id:{table:"Sisipkan tabel",tableAddRow:"Sisipkan baris",tableAddColumn:"Sisipkan kolom",tableDeleteRow:"Hapus baris",tableDeleteColumn:"Hapus kolom",tableDestroy:"Hapus tabel",error:"Galat"},pt_br:{table:"Inserir tabela",tableAddRow:"Adicionar linha",tableAddColumn:"Adicionar coluna",tableDeleteRow:"Deletar linha",tableDeleteColumn:"Deletar coluna",tableDestroy:"Deletar tabela",error:"Erro"}},plugins:{table:{init:function(l){l.o.plugins.table=e.extend(!0,{},t,l.o.plugins.table||{});var a={fn:function(){l.saveRange();var t="table",a=l.o.prefix+"dropdown",d={"class":a+"-"+t+" "+a+" "+l.o.prefix+"fixed-top"};d["data-"+a]=t;var r=e("<div/>",d);if(0===l.$box.find("."+a+"-"+t).length?l.$box.append(r.hide()):r=l.$box.find("."+a+"-"+t),r.html(""),l.$box.find("."+l.o.prefix+"table-button").hasClass(l.o.prefix+"active-button"))r.append(l.buildSubBtn("tableAddRow")),r.append(l.buildSubBtn("tableAddColumn")),r.append(l.buildSubBtn("tableDeleteRow")),r.append(l.buildSubBtn("tableDeleteColumn")),r.append(l.buildSubBtn("tableDestroy"));else{for(var b=e("<table></table>"),i=0;i<l.o.plugins.table.rows;i+=1)for(var s=e("<tr></tr>").appendTo(b),u=0;u<l.o.plugins.table.columns;u+=1)e("<td></td>").appendTo(s);b.find("td").on("mouseover",o),b.find("td").on("mousedown",n),r.append(b),r.append(e("<center>1x1</center>"))}l.dropdown(t)}},o=function(t){var l=e(t.target),a=l.parents("table"),o=this.cellIndex,n=this.parentNode.rowIndex;a.find("td").removeClass("active");for(var d=0;d<=n;d+=1)for(var r=0;r<=o;r+=1)a.find("tr:nth-of-type("+(d+1)+")").find("td:nth-of-type("+(r+1)+")").addClass("active");a.next("center").html(o+1+"x"+(n+1))},n=function(t){l.saveRange();var a=e("<table></table>");l.o.plugins.table.styler&&a.attr("class",l.o.plugins.table.styler);for(var o=(e(t.target),this.cellIndex),n=this.parentNode.rowIndex,d=0;d<=n;d+=1)for(var r=e("<tr></tr>").appendTo(a),b=0;b<=o;b+=1)e("<td></td>").appendTo(r);l.range.deleteContents(),l.range.insertNode(a[0]),l.$c.trigger("tbwchange")},d={title:l.lang.tableAddRow,text:l.lang.tableAddRow,ico:"row-below",fn:function(){l.saveRange();var t=l.doc.getSelection().focusNode,a=e(t).closest("table");if(a.length>0){for(var o=e("<tr></tr>"),n=0;n<a.find("tr")[0].childElementCount;n+=1)e("<td></td>").appendTo(o);o.appendTo(a)}return!0}},r={title:l.lang.tableAddColumn,text:l.lang.tableAddColumn,ico:"col-right",fn:function(){l.saveRange();var t=l.doc.getSelection().focusNode,a=e(t).closest("table");return a.length>0&&e(a).find("tr").each(function(){e(this).find("td:last").after("<td></td>")}),!0}},b={title:l.lang.tableDestroy,text:l.lang.tableDestroy,ico:"table-delete",fn:function(){l.saveRange();var t=l.doc.getSelection().focusNode,a=e(t).closest("table");return a.remove(),!0}},i={title:l.lang.tableDeleteRow,text:l.lang.tableDeleteRow,ico:"row-delete",fn:function(){l.saveRange();var t=l.doc.getSelection().focusNode,a=e(t).closest("tr");return a.remove(),!0}},s={title:l.lang.tableDeleteColumn,text:l.lang.tableDeleteColumn,ico:"col-delete",fn:function(){l.saveRange();var t=l.doc.getSelection().focusNode,a=e(t).closest("table"),o=e(t).closest("td"),n=o.index();return e(a).find("tr").each(function(){e(this).find("td:eq("+n+")").remove()}),!0}};l.addBtnDef("table",a),l.addBtnDef("tableAddRow",d),l.addBtnDef("tableAddColumn",r),l.addBtnDef("tableDeleteRow",i),l.addBtnDef("tableDeleteColumn",s),l.addBtnDef("tableDestroy",b)}}}})}(jQuery);