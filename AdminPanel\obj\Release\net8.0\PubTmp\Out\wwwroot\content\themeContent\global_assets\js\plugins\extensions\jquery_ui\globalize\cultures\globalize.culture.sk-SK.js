/*
 * Globalize Culture sk-SK
 *
 * http://github.com/jquery/globalize
 *
 * Copyright Software Freedom Conservancy, Inc.
 * Dual licensed under the MIT or GPL Version 2 licenses.
 * http://jquery.org/license
 *
 * This file was generated by the Globalize Culture Generator
 * Translation: bugs found in this file need to be fixed in the generator
 */

(function( window, undefined ) {

var Globalize;

if ( typeof require !== "undefined" &&
	typeof exports !== "undefined" &&
	typeof module !== "undefined" ) {
	// Assume CommonJS
	Globalize = require( "globalize" );
} else {
	// Global variable
	Globalize = window.Globalize;
}

Globalize.addCultureInfo( "sk-SK", "default", {
	name: "sk-SK",
	englishName: "Slovak (Slovakia)",
	nativeName: "slovenčina (Slovenská republika)",
	language: "sk",
	numberFormat: {
		",": " ",
		".": ",",
		"NaN": "Nie je číslo",
		negativeInfinity: "-nekone<PERSON>no",
		positiveInfinity: "+nekonečno",
		percent: {
			pattern: ["-n%","n%"],
			",": " ",
			".": ","
		},
		currency: {
			pattern: ["-n $","n $"],
			",": " ",
			".": ",",
			symbol: "€"
		}
	},
	calendars: {
		standard: {
			"/": ". ",
			firstDay: 1,
			days: {
				names: ["nedeľa","pondelok","utorok","streda","štvrtok","piatok","sobota"],
				namesAbbr: ["ne","po","ut","st","št","pi","so"],
				namesShort: ["ne","po","ut","st","št","pi","so"]
			},
			months: {
				names: ["január","február","marec","apríl","máj","jún","júl","august","september","október","november","december",""],
				namesAbbr: ["1","2","3","4","5","6","7","8","9","10","11","12",""]
			},
			monthsGenitive: {
				names: ["januára","februára","marca","apríla","mája","júna","júla","augusta","septembra","októbra","novembra","decembra",""],
				namesAbbr: ["1","2","3","4","5","6","7","8","9","10","11","12",""]
			},
			AM: null,
			PM: null,
			eras: [{"name":"n. l.","start":null,"offset":0}],
			patterns: {
				d: "d. M. yyyy",
				D: "d. MMMM yyyy",
				t: "H:mm",
				T: "H:mm:ss",
				f: "d. MMMM yyyy H:mm",
				F: "d. MMMM yyyy H:mm:ss",
				M: "dd MMMM",
				Y: "MMMM yyyy"
			}
		}
	}
});

}( this ));
