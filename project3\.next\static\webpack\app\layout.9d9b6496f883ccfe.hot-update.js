"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"36a50bfd3d13\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9nbG9iYWxzLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLElBQVUsSUFBSSxpQkFBaUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xceWh5YXNvZnRcXERvd25sb2Fkc1xcZWNcXC5ORVQgOCBWZXJzaW9uIC0gTGF0ZXN0XFxwcm9qZWN0XFxjb2RlbWVkaWNhbFxccHJvamVjdDNcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIzNmE1MGJmZDNkMTNcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/ui/header.tsx":
/*!**********************************!*\
  !*** ./components/ui/header.tsx ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Header: () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Globe,Heart,LogOut,Mail,MapPin,Menu,MessageCircle,Package,Phone,Search,ShoppingCart,User,UserPlus,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Globe,Heart,LogOut,Mail,MapPin,Menu,MessageCircle,Package,Phone,Search,ShoppingCart,User,UserPlus,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Globe,Heart,LogOut,Mail,MapPin,Menu,MessageCircle,Package,Phone,Search,ShoppingCart,User,UserPlus,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Globe,Heart,LogOut,Mail,MapPin,Menu,MessageCircle,Package,Phone,Search,ShoppingCart,User,UserPlus,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Globe,Heart,LogOut,Mail,MapPin,Menu,MessageCircle,Package,Phone,Search,ShoppingCart,User,UserPlus,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Globe,Heart,LogOut,Mail,MapPin,Menu,MessageCircle,Package,Phone,Search,ShoppingCart,User,UserPlus,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Globe,Heart,LogOut,Mail,MapPin,Menu,MessageCircle,Package,Phone,Search,ShoppingCart,User,UserPlus,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Globe,Heart,LogOut,Mail,MapPin,Menu,MessageCircle,Package,Phone,Search,ShoppingCart,User,UserPlus,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Globe,Heart,LogOut,Mail,MapPin,Menu,MessageCircle,Package,Phone,Search,ShoppingCart,User,UserPlus,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Globe,Heart,LogOut,Mail,MapPin,Menu,MessageCircle,Package,Phone,Search,ShoppingCart,User,UserPlus,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Globe,Heart,LogOut,Mail,MapPin,Menu,MessageCircle,Package,Phone,Search,ShoppingCart,User,UserPlus,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-plus.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Globe,Heart,LogOut,Mail,MapPin,Menu,MessageCircle,Package,Phone,Search,ShoppingCart,User,UserPlus,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Globe,Heart,LogOut,Mail,MapPin,Menu,MessageCircle,Package,Phone,Search,ShoppingCart,User,UserPlus,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shopping-cart.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Globe,Heart,LogOut,Mail,MapPin,Menu,MessageCircle,Package,Phone,Search,ShoppingCart,User,UserPlus,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Globe,Heart,LogOut,Mail,MapPin,Menu,MessageCircle,Package,Phone,Search,ShoppingCart,User,UserPlus,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _contexts_cart_context__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/cart-context */ \"(app-pages-browser)/./contexts/cart-context.tsx\");\n/* harmony import */ var _contexts_wishlist_context__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/wishlist-context */ \"(app-pages-browser)/./contexts/wishlist-context.tsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _lib_api_helper__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/api-helper */ \"(app-pages-browser)/./lib/api-helper.ts\");\n/* harmony import */ var _components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/navigation-menu */ \"(app-pages-browser)/./components/ui/navigation-menu.tsx\");\n/* harmony import */ var _components_ui_popover__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/popover */ \"(app-pages-browser)/./components/ui/popover.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./lib/utils.ts\");\n/* harmony import */ var _button__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _contexts_settings_context__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/contexts/settings-context */ \"(app-pages-browser)/./contexts/settings-context.tsx\");\n/* harmony import */ var _contexts_user_context__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/contexts/user-context */ \"(app-pages-browser)/./contexts/user-context.tsx\");\n/* harmony import */ var _color_picker__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./color-picker */ \"(app-pages-browser)/./components/ui/color-picker.tsx\");\n/* __next_internal_client_entry_do_not_use__ Header auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction Header() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.usePathname)();\n    const [categories, setCategories] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(true);\n    const [showColorPicker, setShowColorPicker] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);\n    const [selectedCategoryId, setSelectedCategoryId] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);\n    const [selectedSubcategory, setSelectedSubcategory] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);\n    const [showMobileMenu, setShowMobileMenu] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [showMobileCategories, setShowMobileCategories] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)('');\n    const [cartCount, setCartCount] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(0);\n    const [wishlistCount, setWishlistCount] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(0);\n    const cart = (0,_contexts_cart_context__WEBPACK_IMPORTED_MODULE_4__.useCart)();\n    const wishlist = (0,_contexts_wishlist_context__WEBPACK_IMPORTED_MODULE_5__.useWishlist)();\n    const { user, isLoggedIn, logout } = (0,_contexts_user_context__WEBPACK_IMPORTED_MODULE_13__.useUser)();\n    const { theme, language, primaryColor, primaryTextColor, toggleTheme, setLanguage, setPrimaryColor, t } = (0,_contexts_settings_context__WEBPACK_IMPORTED_MODULE_12__.useSettings)();\n    const handleSearch = ()=>{\n        // Navigate to products page with search parameters\n        const params = new URLSearchParams();\n        if (searchTerm) {\n            params.append('search', searchTerm);\n        }\n        if (selectedCategoryId) {\n            params.append('category', selectedCategoryId.toString());\n        }\n        router.push(\"/products?\".concat(params.toString()));\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"Header.useEffect\": ()=>{\n            const fetchCategories = {\n                \"Header.useEffect.fetchCategories\": async ()=>{\n                    try {\n                        var _categoriesResponse_data, _categoriesResponse_data1;\n                        const param = {\n                            \"PageNumber\": 1,\n                            \"PageSize\": 100,\n                            \"SortColumn\": \"Name\",\n                            \"SortOrder\": \"ASC\"\n                        };\n                        const headers = {\n                            'Content-Type': 'application/json',\n                            'Accept': 'application/json',\n                            'Authorization': 'Bearer ' + localStorage.getItem('token')\n                        };\n                        const categoriesResponse = await (0,_lib_api_helper__WEBPACK_IMPORTED_MODULE_7__.MakeApiCallAsync)(_lib_api_helper__WEBPACK_IMPORTED_MODULE_7__.Config.END_POINT_NAMES.GET_CATEGORIES_LIST, null, param, headers, \"POST\", true);\n                        if (categoriesResponse === null || categoriesResponse === void 0 ? void 0 : (_categoriesResponse_data = categoriesResponse.data) === null || _categoriesResponse_data === void 0 ? void 0 : _categoriesResponse_data.data) {\n                            try {\n                                const parsedData = JSON.parse(categoriesResponse.data.data);\n                                if (Array.isArray(parsedData)) {\n                                    // Create a map of parent categories\n                                    const parentCategories = parsedData.filter({\n                                        \"Header.useEffect.fetchCategories.parentCategories\": (cat)=>!cat.ParentCategoryID\n                                    }[\"Header.useEffect.fetchCategories.parentCategories\"]);\n                                    const childCategories = parsedData.filter({\n                                        \"Header.useEffect.fetchCategories.childCategories\": (cat)=>cat.ParentCategoryID\n                                    }[\"Header.useEffect.fetchCategories.childCategories\"]);\n                                    // Format parent categories with their children\n                                    const formattedCategories = parentCategories.map({\n                                        \"Header.useEffect.fetchCategories.formattedCategories\": (parent)=>({\n                                                id: parent.CategoryID,\n                                                name: parent.Name,\n                                                subcategories: childCategories.filter({\n                                                    \"Header.useEffect.fetchCategories.formattedCategories\": (child)=>child.ParentCategoryID === parent.CategoryID\n                                                }[\"Header.useEffect.fetchCategories.formattedCategories\"]).map({\n                                                    \"Header.useEffect.fetchCategories.formattedCategories\": (child)=>child.Name\n                                                }[\"Header.useEffect.fetchCategories.formattedCategories\"])\n                                            })\n                                    }[\"Header.useEffect.fetchCategories.formattedCategories\"]);\n                                    setCategories(formattedCategories);\n                                } else {\n                                    console.error('Categories data is not an array:', parsedData);\n                                    setCategories([]);\n                                }\n                            } catch (parseError) {\n                                console.error('Error parsing categories data:', parseError);\n                                setCategories([]);\n                            }\n                        } else if (categoriesResponse === null || categoriesResponse === void 0 ? void 0 : (_categoriesResponse_data1 = categoriesResponse.data) === null || _categoriesResponse_data1 === void 0 ? void 0 : _categoriesResponse_data1.errorMessage) {\n                            console.error('API Error:', categoriesResponse.data.errorMessage);\n                            setCategories([]);\n                        } else {\n                            console.error('Invalid or empty response from API');\n                            setCategories([]);\n                        }\n                    } catch (error) {\n                        console.error('Error fetching categories:', error);\n                        setCategories([]);\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"Header.useEffect.fetchCategories\"];\n            fetchCategories();\n        }\n    }[\"Header.useEffect\"], []);\n    // Update cart count when cart items change\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"Header.useEffect\": ()=>{\n            if (cart && cart.items) {\n                setCartCount(cart.totalItems);\n            }\n        }\n    }[\"Header.useEffect\"], [\n        cart,\n        cart === null || cart === void 0 ? void 0 : cart.items\n    ]);\n    // Update wishlist count when wishlist items change\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"Header.useEffect\": ()=>{\n            if (wishlist) {\n                setWishlistCount(wishlist.totalItems);\n            }\n        }\n    }[\"Header.useEffect\"], [\n        wishlist,\n        wishlist === null || wishlist === void 0 ? void 0 : wishlist.wishlistItems\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"w-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                variant: \"ghost\",\n                size: \"sm\",\n                className: \"fixed bottom-36 right-4 md:bottom-24 md:right-6 md:left-auto z-50 bg-gradient-to-r from-purple-500 via-pink-500 to-red-500 shadow-2xl rounded-2xl border-2 border-white/50 flex hover:scale-110 hover:-rotate-6 transition-all duration-300 hover:shadow-purple-500/50 group items-center justify-center w-14 h-14 md:w-16 md:h-16 hover:rounded-full\",\n                onClick: ()=>setShowColorPicker(true),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-8 w-8 md:h-10 md:w-10 rounded-xl group-hover:rounded-full ring-2 ring-white/80 group-hover:ring-4 transition-all duration-300 shadow-inner\",\n                            style: {\n                                backgroundColor: primaryColor\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                            lineNumber: 173,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute -top-2 -right-2 w-5 h-5 bg-gradient-to-br from-cyan-400 to-blue-500 rounded-full shadow-lg animate-spin group-hover:animate-pulse\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                            lineNumber: 174,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute -bottom-1 -left-1 w-3 h-3 bg-gradient-to-br from-green-400 to-emerald-500 rounded-full animate-bounce\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                            lineNumber: 175,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                    lineNumber: 172,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                lineNumber: 166,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hidden md:block py-2.5\",\n                style: {\n                    backgroundColor: primaryColor,\n                    color: primaryTextColor\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto flex flex-col md:flex-row justify-between items-center text-sm px-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex md:flex-row items-start justify-start gap-4 md:gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"tel:009647836071686\",\n                                    className: \"flex items-center gap-2 hover:text-white/80\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                            lineNumber: 184,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs md:text-sm\",\n                                            children: t('phone')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                            lineNumber: 185,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                    lineNumber: 183,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"mailto:<EMAIL>\",\n                                    className: \"flex items-center gap-2 hover:text-white/80\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                            lineNumber: 188,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs md:text-sm\",\n                                            children: t('email')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                            lineNumber: 189,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                    lineNumber: 187,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                            lineNumber: 182,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2 md:gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"sm\",\n                                    className: \"text-white hover:text-white/80 p-1 md:p-2 flex items-center gap-2\",\n                                    onClick: ()=>setLanguage(language === 'en' ? 'ar' : 'en'),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                            lineNumber: 194,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm\",\n                                            children: language === 'en' ? 'العربية' : 'English'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                            lineNumber: 195,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                    lineNumber: 193,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"sm\",\n                                    className: \"text-white hover:text-white/80 flex items-center gap-2\",\n                                    onClick: ()=>window.open(\"https://wa.me/9647836071686?text=\".concat(encodeURIComponent('Hello! I would like to chat with you regarding your services.')), '_blank'),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                            lineNumber: 203,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm\",\n                                            children: t('liveChat')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                            lineNumber: 204,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                    lineNumber: 197,\n                                    columnNumber: 13\n                                }, this),\n                                isLoggedIn ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_9__.Popover, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_9__.PopoverTrigger, {\n                                            asChild: true,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"sm\",\n                                                className: \"text-white hover:text-white/80 p-1 md:p-2 flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                        lineNumber: 210,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm\",\n                                                        children: [\n                                                            \"Welcome, \",\n                                                            (user === null || user === void 0 ? void 0 : user.FirstName) || (user === null || user === void 0 ? void 0 : user.UserName)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                        lineNumber: 211,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"h-3 w-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                        lineNumber: 212,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 209,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                            lineNumber: 208,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_9__.PopoverContent, {\n                                            className: \"w-48 p-2 bg-white border border-gray-200 shadow-lg\",\n                                            align: \"end\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/account\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                            variant: \"ghost\",\n                                                            size: \"sm\",\n                                                            className: \"w-full justify-start hover:bg-gray-50\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                    className: \"h-4 w-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                                    lineNumber: 219,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \"My Account\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                            lineNumber: 218,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                        lineNumber: 217,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/orders\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                            variant: \"ghost\",\n                                                            size: \"sm\",\n                                                            className: \"w-full justify-start hover:bg-gray-50\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    className: \"h-4 w-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                                    lineNumber: 225,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \"My Orders\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                            lineNumber: 224,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                        lineNumber: 223,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/addresses\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                            variant: \"ghost\",\n                                                            size: \"sm\",\n                                                            className: \"w-full justify-start hover:bg-gray-50\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                    className: \"h-4 w-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                                    lineNumber: 231,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \"My Addresses\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                            lineNumber: 230,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                        lineNumber: 229,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/wishlist\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                            variant: \"ghost\",\n                                                            size: \"sm\",\n                                                            className: \"w-full justify-start hover:bg-gray-50\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                    className: \"h-4 w-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                                    lineNumber: 237,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \"Wishlist\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                            lineNumber: 236,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                        lineNumber: 235,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"border-t border-gray-100 my-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                        lineNumber: 241,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                        variant: \"ghost\",\n                                                        size: \"sm\",\n                                                        className: \"w-full justify-start text-red-600 hover:text-red-700 hover:bg-red-50\",\n                                                        onClick: ()=>{\n                                                            logout();\n                                                            router.push('/');\n                                                            sonner__WEBPACK_IMPORTED_MODULE_6__.toast.success('Logged out successfully');\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                className: \"h-4 w-4 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                                lineNumber: 252,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \"Logout\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                        lineNumber: 242,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 216,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                            lineNumber: 215,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                    lineNumber: 207,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/login\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"sm\",\n                                                className: \"text-white hover:text-white/80 p-1 md:p-2 flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                        lineNumber: 262,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm\",\n                                                        children: t('login')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                        lineNumber: 263,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 261,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                            lineNumber: 260,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/signup\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"sm\",\n                                                className: \"text-white hover:text-white/80 p-1 md:p-2 flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                        lineNumber: 268,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm\",\n                                                        children: t('signUp')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                        lineNumber: 269,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 267,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                            lineNumber: 266,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                            lineNumber: 192,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                    lineNumber: 181,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                lineNumber: 180,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto py-4 px-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"md:hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-20\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                        lineNumber: 285,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/\",\n                                        className: \"flex items-center gap-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-[#1B3764] flex items-center gap-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: \"\".concat(\"https://admin.codemedicalapps.com/\", \"content/commonImages/otherImages/18b_logo2x.png\"),\n                                                alt: \"Logo\",\n                                                className: \"h-12 w-auto\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 290,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                            lineNumber: 289,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                        lineNumber: 288,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        className: \"flex items-center gap-2 px-3 py-2 rounded-full border border-gray-200 hover:bg-gray-50\",\n                                        onClick: ()=>setLanguage(language === 'en' ? 'ar' : 'en'),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-lg\",\n                                                children: language === 'en' ? '🇺🇸' : '🇮🇶'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 301,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium\",\n                                                children: language === 'en' ? 'EN' : 'AR'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 304,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                        lineNumber: 295,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                lineNumber: 283,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2 border rounded-full px-4 py-3 bg-background shadow-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            placeholder: t('products') || 'البحث عن المنتجات...',\n                                            className: \"bg-transparent border-none focus:outline-none text-sm flex-1 placeholder:text-gray-400\",\n                                            value: searchTerm,\n                                            onChange: (e)=>setSearchTerm(e.target.value),\n                                            onKeyDown: (e)=>e.key === 'Enter' && handleSearch()\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                            lineNumber: 313,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                            variant: \"ghost\",\n                                            className: \"h-8 w-8 p-0 hover:bg-accent/80 transition-colors\",\n                                            style: {\n                                                color: primaryColor\n                                            },\n                                            onClick: handleSearch,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 327,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                            lineNumber: 321,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                    lineNumber: 312,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                lineNumber: 311,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                        lineNumber: 281,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"hidden md:flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-4 flex-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/\",\n                                        className: \"flex items-center gap-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-[#1B3764] flex items-center gap-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: \"\".concat(\"https://admin.codemedicalapps.com/\", \"content/commonImages/otherImages/18b_logo2x.png\"),\n                                                alt: \"Logo\",\n                                                className: \"h-16 w-auto\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 339,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                            lineNumber: 338,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                        lineNumber: 337,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 border rounded-full px-3 py-1.5 flex-1 max-w-md ml-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_9__.Popover, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_9__.PopoverTrigger, {\n                                                        asChild: true,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                            variant: \"ghost\",\n                                                            className: \"h-8 flex items-center gap-1 px-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-muted-foreground text-sm\",\n                                                                    children: selectedCategory || selectedSubcategory || t('category')\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                                    lineNumber: 348,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-muted-foreground\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                                    lineNumber: 349,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                            lineNumber: 347,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                        lineNumber: 346,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_9__.PopoverContent, {\n                                                        className: \"w-64 p-0 bg-white border border-gray-200 shadow-lg\",\n                                                        align: \"start\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"max-h-[300px] overflow-auto\",\n                                                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-4 text-center text-muted-foreground\",\n                                                                children: t('loadingCategories')\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                                lineNumber: 355,\n                                                                columnNumber: 23\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid\",\n                                                                children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"group\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                className: \"w-full px-4 py-2 text-left hover:bg-gray-50\",\n                                                                                onClick: ()=>{\n                                                                                    setSelectedCategory(category.name);\n                                                                                    setSelectedCategoryId(category.id);\n                                                                                    setSelectedSubcategory(null);\n                                                                                },\n                                                                                children: category.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                                                lineNumber: 360,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"hidden group-hover:block absolute left-full top-0 w-48 bg-white shadow-lg rounded-md border border-gray-200\",\n                                                                                children: category.subcategories.map((sub, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        className: \"w-full px-4 py-2 text-left hover:bg-gray-50\",\n                                                                                        onClick: ()=>{\n                                                                                            setSelectedSubcategory(sub);\n                                                                                            setSelectedCategory(null);\n                                                                                            // Keep the parent category ID for search purposes\n                                                                                            setSelectedCategoryId(category.id);\n                                                                                        },\n                                                                                        children: sub\n                                                                                    }, index, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                                                        lineNumber: 369,\n                                                                                        columnNumber: 33\n                                                                                    }, this))\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                                                lineNumber: 367,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, category.id, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                                        lineNumber: 359,\n                                                                        columnNumber: 27\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                                lineNumber: 357,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                            lineNumber: 353,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                        lineNumber: 352,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 345,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-5 w-px bg-border mx-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 390,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                placeholder: t('products'),\n                                                className: \"bg-transparent border-none focus:outline-none text-sm flex-1\",\n                                                value: searchTerm,\n                                                onChange: (e)=>setSearchTerm(e.target.value),\n                                                onKeyDown: (e)=>e.key === 'Enter' && handleSearch()\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 391,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                variant: \"ghost\",\n                                                className: \"h-8 w-8 p-0\",\n                                                onClick: handleSearch,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                    lineNumber: 400,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 399,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                        lineNumber: 344,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                lineNumber: 335,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden md:block\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_8__.NavigationMenu, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_8__.NavigationMenuList, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_8__.NavigationMenuItem, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_8__.NavigationMenuLink, {\n                                                    asChild: true,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/\",\n                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_10__.cn)(\"group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50\"),\n                                                        children: t('home')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                        lineNumber: 411,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                    lineNumber: 410,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 409,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_8__.NavigationMenuItem, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_8__.NavigationMenuLink, {\n                                                    asChild: true,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/hot-deals\",\n                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_10__.cn)(\"group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50\"),\n                                                        children: t('hotDeals')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                        lineNumber: 420,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                    lineNumber: 419,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 418,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_8__.NavigationMenuItem, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_8__.NavigationMenuLink, {\n                                                    asChild: true,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/products\",\n                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_10__.cn)(\"group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50\"),\n                                                        children: t('products') || 'Products'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                        lineNumber: 429,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                    lineNumber: 428,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 427,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_8__.NavigationMenuItem, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_8__.NavigationMenuLink, {\n                                                    asChild: true,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/payment-methods\",\n                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_10__.cn)(\"group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50\"),\n                                                        children: t('paymentMethods')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                        lineNumber: 438,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                    lineNumber: 437,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 436,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_8__.NavigationMenuItem, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_8__.NavigationMenuLink, {\n                                                    asChild: true,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/follow-us\",\n                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_10__.cn)(\"group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50\"),\n                                                        children: t('followUs')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                        lineNumber: 447,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                    lineNumber: 446,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 445,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_8__.NavigationMenuItem, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_8__.NavigationMenuLink, {\n                                                    asChild: true,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/about\",\n                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_10__.cn)(\"group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50\"),\n                                                        children: t('aboutUs')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                        lineNumber: 456,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                    lineNumber: 455,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 454,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_8__.NavigationMenuItem, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_8__.NavigationMenuLink, {\n                                                    asChild: true,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/contact\",\n                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_10__.cn)(\"group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50\"),\n                                                        children: t('contactUs')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                        lineNumber: 465,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                    lineNumber: 464,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 463,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                        lineNumber: 408,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                    lineNumber: 407,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                lineNumber: 406,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"hidden md:flex items-center gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/wishlist\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                    variant: \"ghost\",\n                                                    size: \"icon\",\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                            className: \"h-5 w-5\",\n                                                            style: {\n                                                                color: primaryColor\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                            lineNumber: 482,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"absolute -top-1 -right-1 bg-primary text-white text-xs rounded-full h-4 w-4 flex items-center justify-center\",\n                                                            children: wishlistCount\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                            lineNumber: 483,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                    lineNumber: 481,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 480,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/cart\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                    variant: \"ghost\",\n                                                    size: \"icon\",\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                            className: \"h-5 w-5\",\n                                                            style: {\n                                                                color: primaryColor\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                            lineNumber: 490,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"absolute -top-1 -right-1 bg-primary text-white text-xs rounded-full h-4 w-4 flex items-center justify-center\",\n                                                            children: cartCount\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                            lineNumber: 491,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                    lineNumber: 489,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 488,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                        lineNumber: 479,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"icon\",\n                                        className: \"md:hidden\",\n                                        onClick: ()=>setShowMobileMenu(!showMobileMenu),\n                                        \"aria-label\": \"Toggle menu\",\n                                        children: showMobileMenu ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                            className: \"h-6 w-6\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                            lineNumber: 506,\n                                            columnNumber: 33\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                            className: \"h-6 w-6\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                            lineNumber: 506,\n                                            columnNumber: 61\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                        lineNumber: 499,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                lineNumber: 477,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                        lineNumber: 333,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                lineNumber: 279,\n                columnNumber: 7\n            }, this),\n            showMobileMenu && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"md:hidden bg-white border-t border-gray-200 shadow-lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: \"px-4 py-4 space-y-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/\",\n                            className: \"block px-4 py-3 text-gray-700 hover:bg-gray-50 hover:text-primary rounded-lg transition-colors\",\n                            onClick: ()=>setShowMobileMenu(false),\n                            children: t('home')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                            lineNumber: 515,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/hot-deals\",\n                            className: \"block px-4 py-3 text-gray-700 hover:bg-gray-50 hover:text-primary rounded-lg transition-colors\",\n                            onClick: ()=>setShowMobileMenu(false),\n                            children: t('hotDeals')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                            lineNumber: 522,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/products\",\n                            className: \"block px-4 py-3 text-gray-700 hover:bg-gray-50 hover:text-primary rounded-lg transition-colors\",\n                            onClick: ()=>setShowMobileMenu(false),\n                            children: t('products') || 'Products'\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                            lineNumber: 529,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/payment-methods\",\n                            className: \"block px-4 py-3 text-gray-700 hover:bg-gray-50 hover:text-primary rounded-lg transition-colors\",\n                            onClick: ()=>setShowMobileMenu(false),\n                            children: t('paymentMethods')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                            lineNumber: 536,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/follow-us\",\n                            className: \"block px-4 py-3 text-gray-700 hover:bg-gray-50 hover:text-primary rounded-lg transition-colors\",\n                            onClick: ()=>setShowMobileMenu(false),\n                            children: t('followUs')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                            lineNumber: 543,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/about\",\n                            className: \"block px-4 py-3 text-gray-700 hover:bg-gray-50 hover:text-primary rounded-lg transition-colors\",\n                            onClick: ()=>setShowMobileMenu(false),\n                            children: t('aboutUs')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                            lineNumber: 550,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/contact\",\n                            className: \"block px-4 py-3 text-gray-700 hover:bg-gray-50 hover:text-primary rounded-lg transition-colors\",\n                            onClick: ()=>setShowMobileMenu(false),\n                            children: t('contactUs')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                            lineNumber: 557,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-t border-gray-200 pt-4 mt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/wishlist\",\n                                    className: \"flex items-center gap-3 px-4 py-3 text-gray-700 hover:bg-gray-50 hover:text-primary rounded-lg transition-colors\",\n                                    onClick: ()=>setShowMobileMenu(false),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                            lineNumber: 572,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Wishlist\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                            lineNumber: 573,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"ml-auto bg-primary text-white text-xs rounded-full h-5 w-5 flex items-center justify-center\",\n                                            children: wishlistCount\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                            lineNumber: 574,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                    lineNumber: 567,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/cart\",\n                                    className: \"flex items-center gap-3 px-4 py-3 text-gray-700 hover:bg-gray-50 hover:text-primary rounded-lg transition-colors\",\n                                    onClick: ()=>setShowMobileMenu(false),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                            lineNumber: 583,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Cart\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                            lineNumber: 584,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"ml-auto bg-primary text-white text-xs rounded-full h-5 w-5 flex items-center justify-center\",\n                                            children: cartCount\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                            lineNumber: 585,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                    lineNumber: 578,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                            lineNumber: 566,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-t border-gray-200 pt-4 mt-4\",\n                            children: isLoggedIn ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"px-4 py-2 text-sm text-gray-500\",\n                                        children: [\n                                            \"Welcome, \",\n                                            (user === null || user === void 0 ? void 0 : user.FirstName) || (user === null || user === void 0 ? void 0 : user.UserName)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                        lineNumber: 595,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/account\",\n                                        className: \"flex items-center gap-3 px-4 py-3 text-gray-700 hover:bg-gray-50 hover:text-primary rounded-lg transition-colors\",\n                                        onClick: ()=>setShowMobileMenu(false),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 603,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"My Account\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 604,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                        lineNumber: 598,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/orders\",\n                                        className: \"flex items-center gap-3 px-4 py-3 text-gray-700 hover:bg-gray-50 hover:text-primary rounded-lg transition-colors\",\n                                        onClick: ()=>setShowMobileMenu(false),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 611,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"My Orders\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 612,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                        lineNumber: 606,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/addresses\",\n                                        className: \"flex items-center gap-3 px-4 py-3 text-gray-700 hover:bg-gray-50 hover:text-primary rounded-lg transition-colors\",\n                                        onClick: ()=>setShowMobileMenu(false),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 619,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"My Addresses\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 620,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                        lineNumber: 614,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"flex items-center gap-3 w-full px-4 py-3 text-red-600 hover:bg-red-50 rounded-lg transition-colors\",\n                                        onClick: ()=>{\n                                            logout();\n                                            setShowMobileMenu(false);\n                                            router.push('/');\n                                            sonner__WEBPACK_IMPORTED_MODULE_6__.toast.success('Logged out successfully');\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 631,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Logout\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 632,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                        lineNumber: 622,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/login\",\n                                        className: \"flex items-center gap-3 px-4 py-3 text-gray-700 hover:bg-gray-50 hover:text-primary rounded-lg transition-colors\",\n                                        onClick: ()=>setShowMobileMenu(false),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 642,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: t('login')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 643,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                        lineNumber: 637,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/signup\",\n                                        className: \"flex items-center gap-3 px-4 py-3 text-gray-700 hover:bg-gray-50 hover:text-primary rounded-lg transition-colors\",\n                                        onClick: ()=>setShowMobileMenu(false),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 650,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: t('signUp')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 651,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                        lineNumber: 645,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                            lineNumber: 592,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                    lineNumber: 514,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                lineNumber: 513,\n                columnNumber: 9\n            }, this),\n            showColorPicker && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_color_picker__WEBPACK_IMPORTED_MODULE_14__.ColorPicker, {\n                onColorSelect: (color)=>{\n                    setPrimaryColor(color);\n                    setShowColorPicker(false);\n                },\n                onClose: ()=>setShowColorPicker(false)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                lineNumber: 661,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n        lineNumber: 165,\n        columnNumber: 5\n    }, this);\n}\n_s(Header, \"TB0htKxrnyj7Nsrnl0veiZ09D1k=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_1__.usePathname,\n        _contexts_cart_context__WEBPACK_IMPORTED_MODULE_4__.useCart,\n        _contexts_wishlist_context__WEBPACK_IMPORTED_MODULE_5__.useWishlist,\n        _contexts_user_context__WEBPACK_IMPORTED_MODULE_13__.useUser,\n        _contexts_settings_context__WEBPACK_IMPORTED_MODULE_12__.useSettings\n    ];\n});\n_c = Header;\nvar _c;\n$RefreshReg$(_c, \"Header\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/ui/header.tsx\n"));

/***/ })

});