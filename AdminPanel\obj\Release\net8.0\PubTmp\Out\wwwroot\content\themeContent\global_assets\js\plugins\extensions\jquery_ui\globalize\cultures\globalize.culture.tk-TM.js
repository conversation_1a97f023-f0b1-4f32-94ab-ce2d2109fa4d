/*
 * Globalize Culture tk-TM
 *
 * http://github.com/jquery/globalize
 *
 * Copyright Software Freedom Conservancy, Inc.
 * Dual licensed under the MIT or GPL Version 2 licenses.
 * http://jquery.org/license
 *
 * This file was generated by the Globalize Culture Generator
 * Translation: bugs found in this file need to be fixed in the generator
 */

(function( window, undefined ) {

var Globalize;

if ( typeof require !== "undefined" &&
	typeof exports !== "undefined" &&
	typeof module !== "undefined" ) {
	// Assume CommonJS
	Globalize = require( "globalize" );
} else {
	// Global variable
	Globalize = window.Globalize;
}

Globalize.addCultureInfo( "tk-TM", "default", {
	name: "tk-TM",
	englishName: "Turkmen (Turkmenistan)",
	nativeName: "türkmençe (Türkmenistan)",
	language: "tk",
	numberFormat: {
		",": " ",
		".": ",",
		negativeInfinity: "-üznüksizlik",
		positiveInfinity: "üznüksizlik",
		percent: {
			pattern: ["-n%","n%"],
			",": " ",
			".": ","
		},
		currency: {
			pattern: ["-n$","n$"],
			",": " ",
			".": ",",
			symbol: "m."
		}
	},
	calendars: {
		standard: {
			"/": ".",
			firstDay: 1,
			days: {
				names: ["Duşenbe","Sişenbe","Çarşenbe","Penşenbe","Anna","Şenbe","Ýekşenbe"],
				namesAbbr: ["Db","Sb","Çb","Pb","An","Şb","Ýb"],
				namesShort: ["D","S","Ç","P","A","Ş","Ý"]
			},
			months: {
				names: ["Ýanwar","Fewral","Mart","Aprel","Maý","lýun","lýul","Awgust","Sentýabr","Oktýabr","Noýabr","Dekabr",""],
				namesAbbr: ["Ýan","Few","Mart","Apr","Maý","lýun","lýul","Awg","Sen","Okt","Not","Dek",""]
			},
			AM: null,
			PM: null,
			patterns: {
				d: "dd.MM.yy",
				D: "yyyy 'ý.' MMMM d",
				t: "H:mm",
				T: "H:mm:ss",
				f: "yyyy 'ý.' MMMM d H:mm",
				F: "yyyy 'ý.' MMMM d H:mm:ss",
				Y: "yyyy 'ý.' MMMM"
			}
		}
	}
});

}( this ));
