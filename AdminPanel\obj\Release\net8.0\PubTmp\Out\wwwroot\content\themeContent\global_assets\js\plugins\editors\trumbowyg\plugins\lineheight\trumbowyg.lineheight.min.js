!function(e){"use strict";function n(n){var i=[],l=["0.9","normal","1.5","2.0"];return e.each(l,function(l,r){n.addBtnDef("lineheight_"+r,{text:'<span style="line-height: '+r+';">'+n.lang.lineheights[r]+"</span>",hasIcon:!1,fn:function(){n.saveRange();var i=n.getRangeText();if(""!==i.replace(/\s/g,""))try{var l=t();e(l).css("lineHeight",r)}catch(a){}}}),i.push("lineheight_"+r)}),i}function t(){var e,n=null;return window.getSelection?(e=window.getSelection(),e.rangeCount&&(n=e.getRangeAt(0).commonAncestorContainer,1!==n.nodeType&&(n=n.parentNode))):(e=document.selection)&&"Control"!==e.type&&(n=e.createRange().parentElement()),n}e.extend(!0,e.trumbowyg,{langs:{en:{lineheight:"Line height",lineheights:{.9:"Small",normal:"Regular",1.5:"Large","2.0":"Extra large"}},da:{lineheight:"Linjehøjde",lineheights:{.9:"Lille",normal:"Normal",1.5:"Stor","2.0":"Ekstra stor"}},fr:{lineheight:"Hauteur de ligne",lineheights:{.9:"Petit",normal:"Regular",1.5:"Grand","2.0":"Très grand"}},nl:{lineheight:"Regelhoogte",lineheights:{.9:"Klein",normal:"Normaal",1.5:"Groot","2.0":"Extra groot"}},tr:{lineheight:"Satır yüksekliği",lineheights:{.9:"Küçük",normal:"Normal",1.5:"Büyük","2.0":"Çok Büyük"}},zh_tw:{lineheight:"文字間距",lineheights:{.9:"小",normal:"正常",1.5:"大","2.0":"特大"}},pt_br:{lineheight:"Altura de linha",lineheights:{.9:"Pequena",normal:"Regular",1.5:"Grande","2.0":"Extra grande"}}}}),e.extend(!0,e.trumbowyg,{plugins:{lineheight:{init:function(e){e.addBtnDef("lineheight",{dropdown:n(e)})}}}})}(jQuery);