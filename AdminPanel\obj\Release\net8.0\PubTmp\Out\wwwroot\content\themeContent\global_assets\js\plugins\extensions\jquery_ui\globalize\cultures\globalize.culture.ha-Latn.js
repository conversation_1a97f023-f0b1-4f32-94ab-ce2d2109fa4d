/*
 * Globalize Culture ha-Latn
 *
 * http://github.com/jquery/globalize
 *
 * Copyright Software Freedom Conservancy, Inc.
 * Dual licensed under the MIT or GPL Version 2 licenses.
 * http://jquery.org/license
 *
 * This file was generated by the Globalize Culture Generator
 * Translation: bugs found in this file need to be fixed in the generator
 */

(function( window, undefined ) {

var Globalize;

if ( typeof require !== "undefined" &&
	typeof exports !== "undefined" &&
	typeof module !== "undefined" ) {
	// Assume CommonJS
	Globalize = require( "globalize" );
} else {
	// Global variable
	Globalize = window.Globalize;
}

Globalize.addCultureInfo( "ha-Latn", "default", {
	name: "ha-Latn",
	englishName: "Hausa (Latin)",
	nativeName: "Hausa",
	language: "ha-Latn",
	numberFormat: {
		currency: {
			pattern: ["$-n","$ n"],
			symbol: "N"
		}
	},
	calendars: {
		standard: {
			days: {
				names: ["<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON>"],
				namesAbbr: ["Lah","Li<PERSON>","<PERSON><PERSON>","<PERSON>r","<PERSON>h","Ju<PERSON>","<PERSON><PERSON>"],
				namesShort: ["L","<PERSON>","T","L","A","J","A"]
			},
			months: {
				names: ["<PERSON>uwaru","Febreru","<PERSON>","Afrilu","Mayu","Yuni","Yuli","Agusta","<PERSON>tumba","Oktocba","Nuwamba","Disamba",""],
				names<PERSON>bbr: ["<PERSON>","Feb","Mar","Afr","May","Yun","Yul","Agu","Sat","Okt","Nuw","Dis",""]
			},
			AM: ["Safe","safe","SAFE"],
			PM: ["Yamma","yamma","YAMMA"],
			eras: [{"name":"AD","start":null,"offset":0}],
			patterns: {
				d: "d/M/yyyy"
			}
		}
	}
});

}( this ));
