/**
 * Local Test Script for Enhanced Order Processing
 * Tests against localhost:5005 backend
 * Uses Node.js built-in modules (no external dependencies)
 */

const http = require('http');
const https = require('https');

// Local Configuration
const BACKEND_URL = 'http://localhost:5005';
const TEST_USER_ID = 1;
const TEST_COUPON_CODE = 'A9804F';

// Simple cart data for testing
const testCartData = [
  {
    ProductId: 15,
    ProductName: "Test Product 1",
    Price: 50.00,
    Quantity: 1,
    ItemPriceTotal: 50.00,
    OrderItemDiscountTotal: 0,
    ShippingChargesTotal: 0,
    OrderItemAttributeChargesTotal: 0,
    ItemSubTotal: 50.00,
    ProductAllSelectedAttributes: "[]"
  }
];

// HTTP request helper function
function makeHttpRequest(url, method, data, headers = {}) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const isHttps = urlObj.protocol === 'https:';
    const httpModule = isHttps ? https : http;

    const defaultHeaders = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      ...headers
    };

    const postData = data ? JSON.stringify(data) : null;
    if (postData) {
      defaultHeaders['Content-Length'] = Buffer.byteLength(postData);
    }

    const options = {
      hostname: urlObj.hostname,
      port: urlObj.port || (isHttps ? 443 : 80),
      path: urlObj.pathname,
      method: method,
      headers: defaultHeaders,
      timeout: 30000
    };

    const req = httpModule.request(options, (res) => {
      let responseData = '';

      res.on('data', (chunk) => {
        responseData += chunk;
      });

      res.on('end', () => {
        try {
          const parsedData = JSON.parse(responseData);
          resolve({
            status: res.statusCode,
            data: parsedData,
            headers: res.headers
          });
        } catch (error) {
          resolve({
            status: res.statusCode,
            data: responseData,
            headers: res.headers
          });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });

    if (postData) {
      req.write(postData);
    }

    req.end();
  });
}

async function testScenario1() {
  console.log('\n🧪 Testing Scenario 1: Normal order without points and coupon');
  console.log('=' .repeat(60));
  
  const orderData = {
    OrderNote: "Local Test - Scenario 1: No points, no coupon",
    cartJsonData: JSON.stringify(testCartData),
    OrderTotal: 50.00,
    CouponCode: "",
    Description: "Local test order without points and coupon",
    StripeStatus: "",
    StripeResponseJson: "",
    StripeBalanceTransactionId: "",
    StripeChargeId: "",
    PayPalResponseJson: "",
    CurrencyCode: "USD",
    PaymentMethod: 6, // Cash on delivery
    Point: null,
    addressid: 1
  };

  return await makeOrderRequest(orderData, "Scenario 1");
}

async function testScenario2() {
  console.log('\n🧪 Testing Scenario 2: Normal order with points only');
  console.log('=' .repeat(60));
  
  const orderData = {
    OrderNote: "Local Test - Scenario 2: Points only",
    cartJsonData: JSON.stringify(testCartData),
    OrderTotal: 45.00, // Reduced by 5 points
    CouponCode: "",
    Description: "Local test order with points only",
    StripeStatus: "",
    StripeResponseJson: "",
    StripeBalanceTransactionId: "",
    StripeChargeId: "",
    PayPalResponseJson: "",
    CurrencyCode: "USD",
    PaymentMethod: 6,
    Point: 5, // Using 5 points
    addressid: 1
  };

  return await makeOrderRequest(orderData, "Scenario 2");
}

async function testScenario3() {
  console.log('\n🧪 Testing Scenario 3: Normal order with coupon only');
  console.log('=' .repeat(60));
  
  const orderData = {
    OrderNote: "Local Test - Scenario 3: Coupon only",
    cartJsonData: JSON.stringify(testCartData),
    OrderTotal: 45.00, // Reduced by coupon
    CouponCode: TEST_COUPON_CODE,
    Description: "Local test order with coupon only",
    StripeStatus: "",
    StripeResponseJson: "",
    StripeBalanceTransactionId: "",
    StripeChargeId: "",
    PayPalResponseJson: "",
    CurrencyCode: "USD",
    PaymentMethod: 6,
    Point: null,
    addressid: 1
  };

  return await makeOrderRequest(orderData, "Scenario 3");
}

async function testScenario4() {
  console.log('\n🧪 Testing Scenario 4: Normal order with both points and coupon');
  console.log('=' .repeat(60));
  
  const orderData = {
    OrderNote: "Local Test - Scenario 4: Points and coupon",
    cartJsonData: JSON.stringify(testCartData),
    OrderTotal: 40.00, // Reduced by both points and coupon
    CouponCode: TEST_COUPON_CODE,
    Description: "Local test order with both points and coupon",
    StripeStatus: "",
    StripeResponseJson: "",
    StripeBalanceTransactionId: "",
    StripeChargeId: "",
    PayPalResponseJson: "",
    CurrencyCode: "USD",
    PaymentMethod: 6,
    Point: 5, // Using 5 points
    addressid: 1
  };

  return await makeOrderRequest(orderData, "Scenario 4");
}

async function makeOrderRequest(orderData, scenarioName) {
  try {
    console.log('📤 Request Data:');
    console.log(JSON.stringify(orderData, null, 2));

    const response = await makeHttpRequest(
      `${BACKEND_URL}/api/v1/common/post-order-direct`,
      'POST',
      orderData
    );

    console.log('✅ Response Status:', response.status);
    console.log('📥 Response Data:');
    console.log(JSON.stringify(response.data, null, 2));

    if (response.data.statusCode === 200) {
      console.log(`🎉 ${scenarioName} - Order placed successfully!`);
      if (response.data.data) {
        console.log(`📋 Order ID: ${response.data.data.OrderID}`);
        console.log(`📋 Order Number: ${response.data.data.OrderNumber}`);
      }
      return { success: true, data: response.data };
    } else {
      console.log(`❌ ${scenarioName} - Order failed:`, response.data.errorMessage || 'Unknown error');
      return { success: false, data: response.data };
    }

  } catch (error) {
    console.log(`❌ ${scenarioName} - Error:`, error.message);
    return { success: false, error: error.message };
  }
}

async function testBackendConnection() {
  console.log('🔍 Testing backend connection...');
  try {
    const response = await makeHttpRequest(`${BACKEND_URL}/api/v1/common/test`, 'GET');
    console.log('✅ Backend is accessible');
    return true;
  } catch (error) {
    if (error.code === 'ECONNREFUSED' || error.message.includes('ECONNREFUSED')) {
      console.log('❌ Backend connection refused. Make sure the backend is running on localhost:5005');
    } else if (error.message.includes('404')) {
      console.log('✅ Backend is accessible (404 is expected for test endpoint)');
      return true;
    } else {
      console.log('⚠️  Backend connection issue:', error.message);
    }
    return false;
  }
}

async function runLocalTests() {
  console.log('🚀 Starting Local Enhanced Order Processing Tests');
  console.log(`Backend URL: ${BACKEND_URL}`);
  console.log(`Test User ID: ${TEST_USER_ID}`);
  console.log(`Test Coupon Code: ${TEST_COUPON_CODE}`);
  console.log('=' .repeat(60));
  
  // Test backend connection first
  const isConnected = await testBackendConnection();
  if (!isConnected) {
    console.log('\n❌ Cannot connect to backend. Please ensure:');
    console.log('1. Backend is running on localhost:5005');
    console.log('2. CORS is configured to allow requests from this script');
    console.log('3. The API endpoint /api/v1/common/post-order-direct exists');
    return;
  }
  
  const results = [];
  
  // Test each scenario
  console.log('\n🧪 Running test scenarios...');
  
  try {
    results.push({ name: 'Scenario 1', result: await testScenario1() });
    await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second
    
    results.push({ name: 'Scenario 2', result: await testScenario2() });
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    results.push({ name: 'Scenario 3', result: await testScenario3() });
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    results.push({ name: 'Scenario 4', result: await testScenario4() });
    
  } catch (error) {
    console.log('❌ Test execution error:', error.message);
  }
  
  // Summary
  console.log('\n📊 LOCAL TEST SUMMARY');
  console.log('=' .repeat(60));
  
  results.forEach((test, index) => {
    const status = test.result.success ? '✅ PASSED' : '❌ FAILED';
    console.log(`${index + 1}. ${test.name}: ${status}`);
  });
  
  const passedTests = results.filter(t => t.result.success).length;
  const totalTests = results.length;
  
  console.log(`\n🎯 Overall Result: ${passedTests}/${totalTests} tests passed`);
  
  if (passedTests === totalTests) {
    console.log('🎉 All local tests passed! Enhanced order processing is working correctly.');
  } else {
    console.log('⚠️  Some tests failed. Check the backend logs for more details.');
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  runLocalTests().catch(console.error);
}

module.exports = {
  runLocalTests,
  testScenario1,
  testScenario2,
  testScenario3,
  testScenario4
};
