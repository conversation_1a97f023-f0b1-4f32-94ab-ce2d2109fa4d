/**
 * Copyright (c) Tiny Technologies, Inc. All rights reserved.
 * Licensed under the LGPL or a commercial license.
 * For LGPL see License.txt in the project root for license information.
 * For commercial licenses see https://www.tiny.cloud/
 *
 * Version: 5.3.0 (2020-05-21)
 */
!function(g){"use strict";var n,t,e,r,c=function(n){var t=n;return{get:function(){return t},set:function(n){t=n}}},o=tinymce.util.Tools.resolve("tinymce.PluginManager"),u=function(n){return{isFullscreen:function(){return null!==n.get()}}},i=function(){},a=function(n){return function(){return n}},l=a(!1),f=a(!0),d=function(){return s},s=(n=function(n){return n.isNone()},{fold:function(n,t){return n()},is:l,isSome:l,isNone:f,getOr:e=function(n){return n},getOrThunk:t=function(n){return n()},getOrDie:function(n){throw new Error(n||"error: getOrDie called on none.")},getOrNull:a(null),getOrUndefined:a(undefined),or:e,orThunk:t,map:d,each:i,bind:d,exists:l,forall:f,filter:d,equals:n,equals_:n,toArray:function(){return[]},toString:a("none()")}),m=function(e){var n=a(e),t=function(){return o},r=function(n){return n(e)},o={fold:function(n,t){return t(e)},is:function(n){return e===n},isSome:f,isNone:l,getOr:n,getOrThunk:n,getOrDie:n,getOrNull:n,getOrUndefined:n,or:t,orThunk:t,map:function(n){return m(n(e))},each:function(n){n(e)},bind:r,exists:r,forall:r,filter:function(n){return n(e)?o:s},toArray:function(){return[e]},toString:function(){return"some("+e+")"},equals:function(n){return n.is(e)},equals_:function(n,t){return n.fold(l,function(n){return t(e,n)})}};return o},h={some:m,none:d,from:function(n){return null===n||n===undefined?s:m(n)}},p=function(){return n=function(n){n.unbind()},t=c(h.none()),e=function(){t.get().each(n)},{clear:function(){e(),t.set(h.none())},isSet:function(){return t.get().isSome()},set:function(n){e(),t.set(h.some(n))}};var n,t,e},v=function(r){return function(n){return e=typeof(t=n),(null===t?"null":"object"==e&&(Array.prototype.isPrototypeOf(t)||t.constructor&&"Array"===t.constructor.name)?"array":"object"==e&&(String.prototype.isPrototypeOf(t)||t.constructor&&"String"===t.constructor.name)?"string":e)===r;var t,e}},y=function(t){return function(n){return typeof n===t}},w=v("string"),b=v("array"),S=y("boolean"),T=y("function"),x=y("number"),C=Array.prototype.push,A=function(n,t){for(var e=n.length,r=new Array(e),o=0;o<e;o++){var i=n[o];r[o]=t(i,o)}return r},E=function(n,t){for(var e=0,r=n.length;e<r;e++){t(n[e],e)}},O=function(n,t){for(var e=[],r=0,o=n.length;r<o;r++){var i=n[r];t(i,r)&&e.push(i)}return e},D=function(n,t){return function(n){for(var t=[],e=0,r=n.length;e<r;++e){if(!b(n[e]))throw new Error("Arr.flatten item "+e+" was not an array, input: "+n);C.apply(t,n[e])}return t}(A(n,t))},M=Object.keys,N=function(n){return n.style!==undefined&&T(n.style.getPropertyValue)},k=function(n){if(null===n||n===undefined)throw new Error("Node cannot be null or undefined");return{dom:a(n)}},F={fromHtml:function(n,t){var e=(t||g.document).createElement("div");if(e.innerHTML=n,!e.hasChildNodes()||1<e.childNodes.length)throw g.console.error("HTML does not have a single root node",n),new Error("HTML must have a single root node");return k(e.childNodes[0])},fromTag:function(n,t){var e=(t||g.document).createElement(n);return k(e)},fromText:function(n,t){var e=(t||g.document).createTextNode(n);return k(e)},fromDom:k,fromPoint:function(n,t,e){var r=n.dom();return h.from(r.elementFromPoint(t,e)).map(k)}},P=("undefined"!=typeof g.window?g.window:Function("return this;")(),r=3,function(n){return n.dom().nodeType===r}),L=function(n,t,e){!function(n,t,e){if(!(w(e)||S(e)||x(e)))throw g.console.error("Invalid call to Attr.set. Key ",t,":: Value ",e,":: Element ",n),new Error("Attribute value was not simple");n.setAttribute(t,e+"")}(n.dom(),t,e)},H=function(n,t){var e=n.dom().getAttribute(t);return null===e?undefined:e},q=function(n,t){n.dom().removeAttribute(t)},V=function(n,t){var e=n.dom();!function(n,t){for(var e=M(n),r=0,o=e.length;r<o;r++){var i=e[r];t(n[i],i)}}(t,function(n,t){!function(n,t,e){if(!w(e))throw g.console.error("Invalid call to CSS.set. Property ",t,":: Value ",e,":: Element ",n),new Error("CSS value must be a string: "+e);N(n)&&n.style.setProperty(t,e)}(e,t,n)})},W=function(n,t){var e,r,o=n.dom(),i=g.window.getComputedStyle(o).getPropertyValue(t);return""!==i||(r=P(e=n)?e.dom().parentNode:e.dom())!==undefined&&null!==r&&r.ownerDocument.body.contains(r)?i:j(o,t)},j=function(n,t){return N(n)?n.style.getPropertyValue(t):""},z=function(n){var e,r,t,o,i,u=F.fromDom(n.target),c=function(){return n.stopPropagation()},l=function(){return n.preventDefault()},f=(e=l,r=c,function(){for(var n=[],t=0;t<arguments.length;t++)n[t]=arguments[t];return e(r.apply(null,n))});return t=u,o=n.clientX,i=n.clientY,{target:a(t),x:a(o),y:a(i),stop:c,prevent:l,kill:f,raw:a(n)}},B=function(n,t){var e=n.dom();if(1!==e.nodeType)return!1;var r=e;if(r.matches!==undefined)return r.matches(t);if(r.msMatchesSelector!==undefined)return r.msMatchesSelector(t);if(r.webkitMatchesSelector!==undefined)return r.webkitMatchesSelector(t);if(r.mozMatchesSelector!==undefined)return r.mozMatchesSelector(t);throw new Error("Browser lacks native selectors")},I=function(e){var n;return n=e,h.from(n.dom().parentNode).map(F.fromDom).map(U).map(function(n){return O(n,function(n){return t=n,e.dom()!==t.dom();var t})}).getOr([])},U=function(n){return A(n.dom().childNodes,F.fromDom)},_=function(e,r){return{left:a(e),top:a(r),translate:function(n,t){return _(e+n,r+t)}}},K=_,X=function(n){var t=n===undefined?g.window:n;return h.from(t.visualViewport)},Y=function(n,t,e,r){return{x:n,y:t,width:e,height:r,right:n+e,bottom:t+r}},G=function(n){var t,e,r,o,i=n===undefined?g.window:n,u=i.document,c=(t=F.fromDom(u),e=t!==undefined?t.dom():g.document,r=e.body.scrollLeft||e.documentElement.scrollLeft,o=e.body.scrollTop||e.documentElement.scrollTop,K(r,o));return X(i).fold(function(){var n=i.document.documentElement,t=n.clientWidth,e=n.clientHeight;return Y(c.left(),c.top(),t,e)},function(n){return Y(Math.max(n.pageLeft,c.left()),Math.max(n.pageTop,c.top()),n.width,n.height)})},J=function(e,n,t){return X(t).map(function(n){var t=function(n){return z(n)};return n.addEventListener(e,t),{unbind:function(){return n.removeEventListener(e,t)}}}).getOrThunk(function(){return{unbind:i}})},Q=tinymce.util.Tools.resolve("tinymce.dom.DOMUtils"),R=tinymce.util.Tools.resolve("tinymce.Env"),Z=tinymce.util.Tools.resolve("tinymce.util.Delay"),$=function(n,t){n.fire("FullscreenStateChanged",{state:t})},nn=function(n,t,e){return O(function(n,t){for(var e=T(t)?t:l,r=n.dom(),o=[];null!==r.parentNode&&r.parentNode!==undefined;){var i=r.parentNode,u=F.fromDom(i);if(o.push(u),!0===e(u))break;r=i}return o}(n,e),t)},tn=function(n){return t=n,o=e===undefined?g.document:e.dom(),1!==(r=o).nodeType&&9!==r.nodeType||0===r.childElementCount?[]:A(o.querySelectorAll(t),F.fromDom);var t,e,r,o},en=function(n,t){return e=function(n){return B(n,t)},O(I(n),e);var e},rn="data-ephox-mobile-fullscreen-style",on="position:absolute!important;",un="top:0!important;left:0!important;margin:0!important;padding:0!important;width:100%!important;height:100%!important;overflow:visible!important;",cn=R.os.isAndroid(),ln=function(o,n,t){var e,r,i,u=function(r){return function(n){var t=H(n,"style"),e=t===undefined?"no-styles":t.trim();e!==r&&(L(n,rn,e),V(n,o.parseStyle(r)))}},c=(e="*",nn(n,function(n){return B(n,e)},r)),l=D(c,function(n){return en(n,"*:not(.tox-silver-sink)")}),f=(i=W(t,"background-color"))!==undefined&&""!==i?"background-color:"+i+"!important":"background-color:rgb(255,255,255)!important;";E(l,u("display:none!important;")),E(c,u(on+un+f)),u((!0===cn?"":on)+un+f)(n)},fn=Q.DOM,an=X().fold(function(){return{bind:i,unbind:i}},function(t){var e,r=(e=c(h.none()),{clear:function(){e.set(h.none())},set:function(n){e.set(h.some(n))},isSet:function(){return e.get().isSome()},on:function(n){e.get().each(n)}}),o=p(),i=p(),u=Z.throttle(function(){g.document.body.scrollTop=0,g.document.documentElement.scrollTop=0,g.window.requestAnimationFrame(function(){r.on(function(n){return V(n,{top:t.offsetTop+"px",left:t.offsetLeft+"px",height:t.height+"px",width:t.width+"px"})})})},50);return{bind:function(n){r.set(n),u(),o.set(J("resize")),i.set(J("scroll"))},unbind:function(){r.on(function(){o.clear(),i.clear()}),r.clear()}}}),dn=function(n,t){var e,r,o,i=g.document.body,u=g.document.documentElement;r=n.getContainer();var c,l,f,a,d=F.fromDom(r),s=t.get(),m=F.fromDom(n.getBody()),h=R.deviceType.isTouch();if(e=r.style,o=n.getContentAreaContainer().firstChild.style,s)o.width=s.iframeWidth,o.height=s.iframeHeight,e.width=s.containerWidth,e.height=s.containerHeight,e.top=s.containerTop,e.left=s.containerLeft,h&&(l=n.dom,f=tn("["+rn+"]"),E(f,function(n){var t=H(n,rn);"no-styles"!==t?V(n,l.parseStyle(t)):q(n,"style"),q(n,rn)})),fn.removeClass(i,"tox-fullscreen"),fn.removeClass(u,"tox-fullscreen"),fn.removeClass(r,"tox-fullscreen"),c=s.scrollPos,g.window.scrollTo(c.x,c.y),t.set(null),$(n,!1),an.unbind(),n.off("remove",an.unbind);else{var p={scrollPos:{x:(a=G(g.window)).x,y:a.y},containerWidth:e.width,containerHeight:e.height,containerTop:e.top,containerLeft:e.left,iframeWidth:o.width,iframeHeight:o.height};h&&ln(n.dom,d,m),o.width=o.height="100%",e.width=e.height="",fn.addClass(i,"tox-fullscreen"),fn.addClass(u,"tox-fullscreen"),fn.addClass(r,"tox-fullscreen"),an.bind(d),n.on("remove",an.unbind),t.set(p),$(n,!0)}},sn=function(e,r){return function(t){t.setActive(null!==r.get());var n=function(n){return t.setActive(n.state)};return e.on("FullscreenStateChanged",n),function(){return e.off("FullscreenStateChanged",n)}}};!function mn(){o.add("fullscreen",function(n){var t,e,r,o,i=c(null);return n.settings.inline||(e=i,(t=n).addCommand("mceFullScreen",function(){dn(t,e)}),o=i,(r=n).ui.registry.addToggleMenuItem("fullscreen",{text:"Fullscreen",icon:"fullscreen",shortcut:"Meta+Shift+F",onAction:function(){return r.execCommand("mceFullScreen")},onSetup:sn(r,o)}),r.ui.registry.addToggleButton("fullscreen",{tooltip:"Fullscreen",icon:"fullscreen",onAction:function(){return r.execCommand("mceFullScreen")},onSetup:sn(r,o)}),n.addShortcut("Meta+Shift+F","","mceFullScreen")),u(i)})}()}(window);