/*
 * Globalize Culture id-ID
 *
 * http://github.com/jquery/globalize
 *
 * Copyright Software Freedom Conservancy, Inc.
 * Dual licensed under the MIT or GPL Version 2 licenses.
 * http://jquery.org/license
 *
 * This file was generated by the Globalize Culture Generator
 * Translation: bugs found in this file need to be fixed in the generator
 */

(function( window, undefined ) {

var Globalize;

if ( typeof require !== "undefined" &&
	typeof exports !== "undefined" &&
	typeof module !== "undefined" ) {
	// Assume CommonJS
	Globalize = require( "globalize" );
} else {
	// Global variable
	Globalize = window.Globalize;
}

Globalize.addCultureInfo( "id-ID", "default", {
	name: "id-ID",
	englishName: "Indonesian (Indonesia)",
	nativeName: "Bahasa Indonesia (Indonesia)",
	language: "id",
	numberFormat: {
		",": ".",
		".": ",",
		percent: {
			",": ".",
			".": ","
		},
		currency: {
			decimals: 0,
			",": ".",
			".": ",",
			symbol: "Rp"
		}
	},
	calendars: {
		standard: {
			firstDay: 1,
			days: {
				names: ["<PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON>","Sabtu"],
				namesAbbr: ["<PERSON>gu","<PERSON>","Sel","<PERSON>bu","Kamis","Jumat","Sabtu"],
				namesShort: ["M","S","S","R","K","J","S"]
			},
			months: {
				names: ["Januari","<PERSON>ruari","<PERSON>t","April","Mei","Juni","Juli","Agustus","September","Oktober","Nopember","Desember",""],
				namesAbbr: ["Jan","Feb","Mar","Apr","Mei","Jun","Jul","Agust","Sep","Okt","Nop","Des",""]
			},
			AM: null,
			PM: null,
			patterns: {
				d: "dd/MM/yyyy",
				D: "dd MMMM yyyy",
				t: "H:mm",
				T: "H:mm:ss",
				f: "dd MMMM yyyy H:mm",
				F: "dd MMMM yyyy H:mm:ss",
				M: "dd MMMM",
				Y: "MMMM yyyy"
			}
		}
	}
});

}( this ));
