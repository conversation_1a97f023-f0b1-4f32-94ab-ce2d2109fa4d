!function(a){"use strict";function n(n){var i=[];return a.each(n.o.plugins.fontfamily.fontList,function(a,e){n.addBtnDef("fontfamily_"+a,{title:'<span style="font-family: '+e.family+';">'+e.name+"</span>",hasIcon:!1,fn:function(){n.execCmd("fontName",e.family,!0)}}),i.push("fontfamily_"+a)}),i}a.extend(!0,a.trumbowyg,{langs:{en:{fontFamily:"Font"},da:{fontFamily:"Skrifttype"},fr:{fontFamily:"Police"},de:{fontFamily:"Schriftart"},nl:{fontFamily:"Lettertype"},tr:{fontFamily:"Yazı Tipi"},zh_tw:{fontFamily:"字體"},pt_br:{fontFamily:"Fonte"}}});var i={fontList:[{name:"<PERSON><PERSON>",family:"Arial, Helvetica, sans-serif"},{name:"Aria<PERSON> Black",family:"'Aria<PERSON> Black', Gadget, sans-serif"},{name:"Comic Sans",family:"'Comic Sans MS', Textile, cursive, sans-serif"},{name:"Courier New",family:"'Courier New', Courier, monospace"},{name:"Georgia",family:"Georgia, serif"},{name:"Impact",family:"Impact, Charcoal, sans-serif"},{name:"Lucida Console",family:"'Lucida Console', Monaco, monospace"},{name:"Lucida Sans",family:"'Lucida Sans Uncide', 'Lucida Grande', sans-serif"},{name:"Palatino",family:"'Palatino Linotype', 'Book Antiqua', Palatino, serif"},{name:"Tahoma",family:"Tahoma, Geneva, sans-serif"},{name:"Times New Roman",family:"'Times New Roman', Times, serif"},{name:"Trebuchet",family:"'Trebuchet MS', Helvetica, sans-serif"},{name:"Verdana",family:"Verdana, Geneva, sans-serif"}]};a.extend(!0,a.trumbowyg,{plugins:{fontfamily:{init:function(a){a.o.plugins.fontfamily=a.o.plugins.fontfamily||i,a.addBtnDef("fontfamily",{dropdown:n(a),hasIcon:!1,text:a.lang.fontFamily})}}}})}(jQuery);