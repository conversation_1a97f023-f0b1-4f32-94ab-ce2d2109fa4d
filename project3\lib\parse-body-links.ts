/**
 * Utility function to parse links in HTML content
 * This function finds all anchor tags in the HTML content and makes them work properly
 * by adding the appropriate target and rel attributes
 * It also converts plain URLs into clickable links
 */
export function parseBodyLinks(htmlContent: string): string {
  if (!htmlContent) return '';
  
  // Check if the content is just a URL (common case in campaign Body)
  if (htmlContent.trim().match(/^https?:\/\/.+$/i)) {
    // If the content is just a URL, wrap it in an anchor tag with target="_blank"
    return `<a href="${htmlContent}" target="_blank" rel="noopener noreferrer">${htmlContent}</a>`;
  }
  
  // Simple URL regex that works in all browsers
  const urlRegex = /(https?:\/\/[^\s<]+[^\s<.,:;"'\)\]\}])/gi;
  
  // First pass: temporarily mark URLs that are already in anchor tags
  let tempContent = htmlContent.replace(/<a[^>]*>.*?<\/a>/gi, (match) => {
    // Replace URLs in anchor tags with a placeholder
    return match.replace(urlRegex, 'URLPLACEHOLDER');
  });
  
  // Second pass: convert plain URLs to anchor tags
  tempContent = tempContent.replace(urlRegex, '<a href="$1" target="_blank" rel="noopener noreferrer">$1</a>');
  
  // Restore original anchor tags
  tempContent = tempContent.replace(/URLPLACEHOLDER/g, (match) => {
    return match; // This will be replaced with the original URL in the next step
  });
  
  // Regular expression to find anchor tags
  const anchorTagRegex = /<a\s+(?:[^>]*?\s+)?href=(["'])(.*?)\1[^>]*>(.*?)<\/a>/gi;
  
  // Replace anchor tags with properly formatted ones
  const parsedContent = tempContent.replace(anchorTagRegex, (match, quote, url, text) => {
    // Check if the URL is external
    const isExternal = url.startsWith('http') || url.startsWith('https');
    
    // Add target and rel attributes for external links
    const target = isExternal ? ' target="_blank"' : '';
    const rel = isExternal ? ' rel="noopener noreferrer"' : '';
    
    // Return the formatted anchor tag
    return `<a href=${quote}${url}${quote}${target}${rel}>${text}</a>`;
  });
  
  return parsedContent;
}