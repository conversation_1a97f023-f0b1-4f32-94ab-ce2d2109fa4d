/**
 * Utility function to parse links in HTML content
 * This function finds all anchor tags in the HTML content and makes them work properly
 * by adding the appropriate target and rel attributes
 */
export function parseBodyLinks(htmlContent: string): string {
  if (!htmlContent) return '';
  
  // Regular expression to find anchor tags
  const anchorTagRegex = /<a\s+(?:[^>]*?\s+)?href=(["'])(.*?)\1[^>]*>(.*?)<\/a>/gi;
  
  // Replace anchor tags with properly formatted ones
  const parsedContent = htmlContent.replace(anchorTagRegex, (match, quote, url, text) => {
    // Check if the URL is external
    const isExternal = url.startsWith('http') || url.startsWith('https');
    
    // Add target and rel attributes for external links
    const target = isExternal ? ' target="_blank"' : '';
    const rel = isExternal ? ' rel="noopener noreferrer"' : '';
    
    // Return the formatted anchor tag
    return `<a href=${quote}${url}${quote}${target}${rel}>${text}</a>`;
  });
  
  return parsedContent;
}