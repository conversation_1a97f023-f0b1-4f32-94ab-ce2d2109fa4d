"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"ffd329e12960\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9nbG9iYWxzLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLElBQVUsSUFBSSxpQkFBaUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xceWh5YXNvZnRcXERvd25sb2Fkc1xcZWNcXC5ORVQgOCBWZXJzaW9uIC0gTGF0ZXN0XFxwcm9qZWN0XFxjb2RlbWVkaWNhbFxccHJvamVjdDNcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJmZmQzMjllMTI5NjBcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/ui/header.tsx":
/*!**********************************!*\
  !*** ./components/ui/header.tsx ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Header: () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Globe,Heart,LogOut,Mail,MapPin,Menu,MessageCircle,Package,Phone,Search,ShoppingCart,User,UserPlus,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Globe,Heart,LogOut,Mail,MapPin,Menu,MessageCircle,Package,Phone,Search,ShoppingCart,User,UserPlus,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Globe,Heart,LogOut,Mail,MapPin,Menu,MessageCircle,Package,Phone,Search,ShoppingCart,User,UserPlus,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Globe,Heart,LogOut,Mail,MapPin,Menu,MessageCircle,Package,Phone,Search,ShoppingCart,User,UserPlus,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Globe,Heart,LogOut,Mail,MapPin,Menu,MessageCircle,Package,Phone,Search,ShoppingCart,User,UserPlus,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Globe,Heart,LogOut,Mail,MapPin,Menu,MessageCircle,Package,Phone,Search,ShoppingCart,User,UserPlus,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Globe,Heart,LogOut,Mail,MapPin,Menu,MessageCircle,Package,Phone,Search,ShoppingCart,User,UserPlus,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Globe,Heart,LogOut,Mail,MapPin,Menu,MessageCircle,Package,Phone,Search,ShoppingCart,User,UserPlus,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Globe,Heart,LogOut,Mail,MapPin,Menu,MessageCircle,Package,Phone,Search,ShoppingCart,User,UserPlus,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Globe,Heart,LogOut,Mail,MapPin,Menu,MessageCircle,Package,Phone,Search,ShoppingCart,User,UserPlus,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Globe,Heart,LogOut,Mail,MapPin,Menu,MessageCircle,Package,Phone,Search,ShoppingCart,User,UserPlus,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-plus.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Globe,Heart,LogOut,Mail,MapPin,Menu,MessageCircle,Package,Phone,Search,ShoppingCart,User,UserPlus,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Globe,Heart,LogOut,Mail,MapPin,Menu,MessageCircle,Package,Phone,Search,ShoppingCart,User,UserPlus,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shopping-cart.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Globe,Heart,LogOut,Mail,MapPin,Menu,MessageCircle,Package,Phone,Search,ShoppingCart,User,UserPlus,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Globe,Heart,LogOut,Mail,MapPin,Menu,MessageCircle,Package,Phone,Search,ShoppingCart,User,UserPlus,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _contexts_cart_context__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/cart-context */ \"(app-pages-browser)/./contexts/cart-context.tsx\");\n/* harmony import */ var _contexts_wishlist_context__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/wishlist-context */ \"(app-pages-browser)/./contexts/wishlist-context.tsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _lib_api_helper__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/api-helper */ \"(app-pages-browser)/./lib/api-helper.ts\");\n/* harmony import */ var _components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/navigation-menu */ \"(app-pages-browser)/./components/ui/navigation-menu.tsx\");\n/* harmony import */ var _components_ui_popover__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/popover */ \"(app-pages-browser)/./components/ui/popover.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./lib/utils.ts\");\n/* harmony import */ var _button__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _contexts_settings_context__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/contexts/settings-context */ \"(app-pages-browser)/./contexts/settings-context.tsx\");\n/* harmony import */ var _contexts_user_context__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/contexts/user-context */ \"(app-pages-browser)/./contexts/user-context.tsx\");\n/* harmony import */ var _color_picker__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./color-picker */ \"(app-pages-browser)/./components/ui/color-picker.tsx\");\n/* __next_internal_client_entry_do_not_use__ Header auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction Header() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const [categories, setCategories] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(true);\n    const [showColorPicker, setShowColorPicker] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);\n    const [selectedCategoryId, setSelectedCategoryId] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);\n    const [selectedSubcategory, setSelectedSubcategory] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);\n    const [showMobileMenu, setShowMobileMenu] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [showMobileCategories, setShowMobileCategories] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)('');\n    const [cartCount, setCartCount] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(0);\n    const [wishlistCount, setWishlistCount] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(0);\n    const cart = (0,_contexts_cart_context__WEBPACK_IMPORTED_MODULE_4__.useCart)();\n    const wishlist = (0,_contexts_wishlist_context__WEBPACK_IMPORTED_MODULE_5__.useWishlist)();\n    const { user, isLoggedIn, logout } = (0,_contexts_user_context__WEBPACK_IMPORTED_MODULE_13__.useUser)();\n    const { theme, language, primaryColor, primaryTextColor, toggleTheme, setLanguage, setPrimaryColor, t } = (0,_contexts_settings_context__WEBPACK_IMPORTED_MODULE_12__.useSettings)();\n    const handleSearch = ()=>{\n        // Navigate to products page with search parameters\n        const params = new URLSearchParams();\n        if (searchTerm) {\n            params.append('search', searchTerm);\n        }\n        if (selectedCategoryId) {\n            params.append('category', selectedCategoryId.toString());\n        }\n        router.push(\"/products?\".concat(params.toString()));\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"Header.useEffect\": ()=>{\n            const fetchCategories = {\n                \"Header.useEffect.fetchCategories\": async ()=>{\n                    try {\n                        var _categoriesResponse_data, _categoriesResponse_data1;\n                        const param = {\n                            \"PageNumber\": 1,\n                            \"PageSize\": 100,\n                            \"SortColumn\": \"Name\",\n                            \"SortOrder\": \"ASC\"\n                        };\n                        const headers = {\n                            'Content-Type': 'application/json',\n                            'Accept': 'application/json',\n                            'Authorization': 'Bearer ' + localStorage.getItem('token')\n                        };\n                        const categoriesResponse = await (0,_lib_api_helper__WEBPACK_IMPORTED_MODULE_7__.MakeApiCallAsync)(_lib_api_helper__WEBPACK_IMPORTED_MODULE_7__.Config.END_POINT_NAMES.GET_CATEGORIES_LIST, null, param, headers, \"POST\", true);\n                        if (categoriesResponse === null || categoriesResponse === void 0 ? void 0 : (_categoriesResponse_data = categoriesResponse.data) === null || _categoriesResponse_data === void 0 ? void 0 : _categoriesResponse_data.data) {\n                            try {\n                                const parsedData = JSON.parse(categoriesResponse.data.data);\n                                if (Array.isArray(parsedData)) {\n                                    // Create a map of parent categories\n                                    const parentCategories = parsedData.filter({\n                                        \"Header.useEffect.fetchCategories.parentCategories\": (cat)=>!cat.ParentCategoryID\n                                    }[\"Header.useEffect.fetchCategories.parentCategories\"]);\n                                    const childCategories = parsedData.filter({\n                                        \"Header.useEffect.fetchCategories.childCategories\": (cat)=>cat.ParentCategoryID\n                                    }[\"Header.useEffect.fetchCategories.childCategories\"]);\n                                    // Format parent categories with their children\n                                    const formattedCategories = parentCategories.map({\n                                        \"Header.useEffect.fetchCategories.formattedCategories\": (parent)=>({\n                                                id: parent.CategoryID,\n                                                name: parent.Name,\n                                                subcategories: childCategories.filter({\n                                                    \"Header.useEffect.fetchCategories.formattedCategories\": (child)=>child.ParentCategoryID === parent.CategoryID\n                                                }[\"Header.useEffect.fetchCategories.formattedCategories\"]).map({\n                                                    \"Header.useEffect.fetchCategories.formattedCategories\": (child)=>child.Name\n                                                }[\"Header.useEffect.fetchCategories.formattedCategories\"])\n                                            })\n                                    }[\"Header.useEffect.fetchCategories.formattedCategories\"]);\n                                    setCategories(formattedCategories);\n                                } else {\n                                    console.error('Categories data is not an array:', parsedData);\n                                    setCategories([]);\n                                }\n                            } catch (parseError) {\n                                console.error('Error parsing categories data:', parseError);\n                                setCategories([]);\n                            }\n                        } else if (categoriesResponse === null || categoriesResponse === void 0 ? void 0 : (_categoriesResponse_data1 = categoriesResponse.data) === null || _categoriesResponse_data1 === void 0 ? void 0 : _categoriesResponse_data1.errorMessage) {\n                            console.error('API Error:', categoriesResponse.data.errorMessage);\n                            setCategories([]);\n                        } else {\n                            console.error('Invalid or empty response from API');\n                            setCategories([]);\n                        }\n                    } catch (error) {\n                        console.error('Error fetching categories:', error);\n                        setCategories([]);\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"Header.useEffect.fetchCategories\"];\n            fetchCategories();\n        }\n    }[\"Header.useEffect\"], []);\n    // Update cart count when cart items change\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"Header.useEffect\": ()=>{\n            if (cart && cart.items) {\n                setCartCount(cart.totalItems);\n            }\n        }\n    }[\"Header.useEffect\"], [\n        cart,\n        cart === null || cart === void 0 ? void 0 : cart.items\n    ]);\n    // Update wishlist count when wishlist items change\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"Header.useEffect\": ()=>{\n            if (wishlist) {\n                setWishlistCount(wishlist.totalItems);\n            }\n        }\n    }[\"Header.useEffect\"], [\n        wishlist,\n        wishlist === null || wishlist === void 0 ? void 0 : wishlist.wishlistItems\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"w-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                variant: \"ghost\",\n                size: \"sm\",\n                className: \"fixed bottom-36 right-4 md:bottom-24 md:right-6 md:left-auto z-50 bg-gradient-to-r from-purple-500 via-pink-500 to-red-500 shadow-2xl rounded-2xl border-2 border-white/50 flex hover:scale-110 hover:-rotate-6 transition-all duration-300 hover:shadow-purple-500/50 group items-center justify-center w-14 h-14 md:w-16 md:h-16 hover:rounded-full\",\n                onClick: ()=>setShowColorPicker(true),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-8 w-8 md:h-10 md:w-10 rounded-xl group-hover:rounded-full ring-2 ring-white/80 group-hover:ring-4 transition-all duration-300 shadow-inner\",\n                            style: {\n                                backgroundColor: primaryColor\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                            lineNumber: 172,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute -top-2 -right-2 w-5 h-5 bg-gradient-to-br from-cyan-400 to-blue-500 rounded-full shadow-lg animate-spin group-hover:animate-pulse\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                            lineNumber: 173,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute -bottom-1 -left-1 w-3 h-3 bg-gradient-to-br from-green-400 to-emerald-500 rounded-full animate-bounce\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                            lineNumber: 174,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                    lineNumber: 171,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                lineNumber: 165,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hidden md:block py-2.5\",\n                style: {\n                    backgroundColor: primaryColor,\n                    color: primaryTextColor\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto flex flex-col md:flex-row justify-between items-center text-sm px-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex md:flex-row items-start justify-start gap-4 md:gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"tel:009647836071686\",\n                                    className: \"flex items-center gap-2 hover:text-white/80\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                            lineNumber: 183,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs md:text-sm\",\n                                            children: t('phone')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                            lineNumber: 184,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                    lineNumber: 182,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"mailto:<EMAIL>\",\n                                    className: \"flex items-center gap-2 hover:text-white/80\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                            lineNumber: 187,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs md:text-sm\",\n                                            children: t('email')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                            lineNumber: 188,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                    lineNumber: 186,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                            lineNumber: 181,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2 md:gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"sm\",\n                                    className: \"text-white hover:text-white/80 p-1 md:p-2 flex items-center gap-2\",\n                                    onClick: ()=>setLanguage(language === 'en' ? 'ar' : 'en'),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                            lineNumber: 193,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm\",\n                                            children: language === 'en' ? 'العربية' : 'English'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                            lineNumber: 194,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                    lineNumber: 192,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"sm\",\n                                    className: \"text-white hover:text-white/80 flex items-center gap-2\",\n                                    onClick: ()=>window.open(\"https://wa.me/9647836071686?text=\".concat(encodeURIComponent('Hello! I would like to chat with you regarding your services.')), '_blank'),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                            lineNumber: 202,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm\",\n                                            children: t('liveChat')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                            lineNumber: 203,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 13\n                                }, this),\n                                isLoggedIn ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_9__.Popover, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_9__.PopoverTrigger, {\n                                            asChild: true,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"sm\",\n                                                className: \"text-white hover:text-white/80 p-1 md:p-2 flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                        lineNumber: 209,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm\",\n                                                        children: [\n                                                            \"Welcome, \",\n                                                            (user === null || user === void 0 ? void 0 : user.FirstName) || (user === null || user === void 0 ? void 0 : user.UserName)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                        lineNumber: 210,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"h-3 w-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                        lineNumber: 211,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 208,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                            lineNumber: 207,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_9__.PopoverContent, {\n                                            className: \"w-48 p-2 bg-white border border-gray-200 shadow-lg\",\n                                            align: \"end\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/account\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                            variant: \"ghost\",\n                                                            size: \"sm\",\n                                                            className: \"w-full justify-start hover:bg-gray-50\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                    className: \"h-4 w-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                                    lineNumber: 218,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \"My Account\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                            lineNumber: 217,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                        lineNumber: 216,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/orders\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                            variant: \"ghost\",\n                                                            size: \"sm\",\n                                                            className: \"w-full justify-start hover:bg-gray-50\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    className: \"h-4 w-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                                    lineNumber: 224,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \"My Orders\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                            lineNumber: 223,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                        lineNumber: 222,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/addresses\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                            variant: \"ghost\",\n                                                            size: \"sm\",\n                                                            className: \"w-full justify-start hover:bg-gray-50\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                    className: \"h-4 w-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                                    lineNumber: 230,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \"My Addresses\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                            lineNumber: 229,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                        lineNumber: 228,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/wishlist\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                            variant: \"ghost\",\n                                                            size: \"sm\",\n                                                            className: \"w-full justify-start hover:bg-gray-50\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                    className: \"h-4 w-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                                    lineNumber: 236,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \"Wishlist\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                            lineNumber: 235,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                        lineNumber: 234,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"border-t border-gray-100 my-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                        lineNumber: 240,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                        variant: \"ghost\",\n                                                        size: \"sm\",\n                                                        className: \"w-full justify-start text-red-600 hover:text-red-700 hover:bg-red-50\",\n                                                        onClick: ()=>{\n                                                            logout();\n                                                            router.push('/');\n                                                            sonner__WEBPACK_IMPORTED_MODULE_6__.toast.success('Logged out successfully');\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                className: \"h-4 w-4 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                                lineNumber: 251,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \"Logout\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                        lineNumber: 241,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 215,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                            lineNumber: 214,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/login\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"sm\",\n                                                className: \"text-white hover:text-white/80 p-1 md:p-2 flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                        lineNumber: 261,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm\",\n                                                        children: t('login')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                        lineNumber: 262,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 260,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                            lineNumber: 259,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/signup\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"sm\",\n                                                className: \"text-white hover:text-white/80 p-1 md:p-2 flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                        lineNumber: 267,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm\",\n                                                        children: t('signUp')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                        lineNumber: 268,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 266,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                            lineNumber: 265,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                            lineNumber: 191,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                    lineNumber: 180,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                lineNumber: 179,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto py-4 px-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"md:hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-20\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                        lineNumber: 284,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/\",\n                                        className: \"flex items-center gap-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-[#1B3764] flex items-center gap-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: \"\".concat(\"https://admin.codemedicalapps.com/\", \"content/commonImages/otherImages/18b_logo2x.png\"),\n                                                alt: \"Logo\",\n                                                className: \"h-12 w-auto\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 289,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                            lineNumber: 288,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                        lineNumber: 287,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        className: \"flex items-center gap-2 px-3 py-2 rounded-full border border-gray-200 hover:bg-gray-50\",\n                                        onClick: ()=>setLanguage(language === 'en' ? 'ar' : 'en'),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-lg\",\n                                                children: language === 'en' ? '🇺🇸' : '🇮🇶'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 300,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium\",\n                                                children: language === 'en' ? 'EN' : 'AR'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 303,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                        lineNumber: 294,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                lineNumber: 282,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2 border rounded-full px-4 py-3 bg-background shadow-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            placeholder: t('products') || 'البحث عن المنتجات...',\n                                            className: \"bg-transparent border-none focus:outline-none text-sm flex-1 placeholder:text-gray-400\",\n                                            value: searchTerm,\n                                            onChange: (e)=>setSearchTerm(e.target.value),\n                                            onKeyDown: (e)=>e.key === 'Enter' && handleSearch()\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                            lineNumber: 312,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                            variant: \"ghost\",\n                                            className: \"h-8 w-8 p-0 hover:bg-accent/80 transition-colors\",\n                                            style: {\n                                                color: primaryColor\n                                            },\n                                            onClick: handleSearch,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 326,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                            lineNumber: 320,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                    lineNumber: 311,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                lineNumber: 310,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                        lineNumber: 280,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"hidden md:flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-4 flex-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/\",\n                                        className: \"flex items-center gap-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-[#1B3764] flex items-center gap-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: \"\".concat(\"https://admin.codemedicalapps.com/\", \"content/commonImages/otherImages/18b_logo2x.png\"),\n                                                alt: \"Logo\",\n                                                className: \"h-16 w-auto\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 338,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                            lineNumber: 337,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                        lineNumber: 336,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 border rounded-full px-3 py-1.5 flex-1 max-w-md ml-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_9__.Popover, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_9__.PopoverTrigger, {\n                                                        asChild: true,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                            variant: \"ghost\",\n                                                            className: \"h-8 flex items-center gap-1 px-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-muted-foreground text-sm\",\n                                                                    children: selectedCategory || selectedSubcategory || t('category')\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                                    lineNumber: 347,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-muted-foreground\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                                    lineNumber: 348,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                            lineNumber: 346,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                        lineNumber: 345,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_9__.PopoverContent, {\n                                                        className: \"w-64 p-0 bg-white border border-gray-200 shadow-lg\",\n                                                        align: \"start\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"max-h-[300px] overflow-auto\",\n                                                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-4 text-center text-muted-foreground\",\n                                                                children: t('loadingCategories')\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                                lineNumber: 354,\n                                                                columnNumber: 23\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid\",\n                                                                children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"group\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                className: \"w-full px-4 py-2 text-left hover:bg-gray-50\",\n                                                                                onClick: ()=>{\n                                                                                    setSelectedCategory(category.name);\n                                                                                    setSelectedCategoryId(category.id);\n                                                                                    setSelectedSubcategory(null);\n                                                                                },\n                                                                                children: category.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                                                lineNumber: 359,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"hidden group-hover:block absolute left-full top-0 w-48 bg-white shadow-lg rounded-md border border-gray-200\",\n                                                                                children: category.subcategories.map((sub, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        className: \"w-full px-4 py-2 text-left hover:bg-gray-50\",\n                                                                                        onClick: ()=>{\n                                                                                            setSelectedSubcategory(sub);\n                                                                                            setSelectedCategory(null);\n                                                                                            // Keep the parent category ID for search purposes\n                                                                                            setSelectedCategoryId(category.id);\n                                                                                        },\n                                                                                        children: sub\n                                                                                    }, index, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                                                        lineNumber: 368,\n                                                                                        columnNumber: 33\n                                                                                    }, this))\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                                                lineNumber: 366,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, category.id, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                                        lineNumber: 358,\n                                                                        columnNumber: 27\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                                lineNumber: 356,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                            lineNumber: 352,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                        lineNumber: 351,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 344,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-5 w-px bg-border mx-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 389,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                placeholder: t('products'),\n                                                className: \"bg-transparent border-none focus:outline-none text-sm flex-1\",\n                                                value: searchTerm,\n                                                onChange: (e)=>setSearchTerm(e.target.value),\n                                                onKeyDown: (e)=>e.key === 'Enter' && handleSearch()\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 390,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                variant: \"ghost\",\n                                                className: \"h-8 w-8 p-0\",\n                                                onClick: handleSearch,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                    lineNumber: 399,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 398,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                        lineNumber: 343,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                lineNumber: 334,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden md:block\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_8__.NavigationMenu, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_8__.NavigationMenuList, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_8__.NavigationMenuItem, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_8__.NavigationMenuLink, {\n                                                    asChild: true,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/\",\n                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_10__.cn)(\"group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50\"),\n                                                        children: t('home')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                        lineNumber: 410,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                    lineNumber: 409,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 408,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_8__.NavigationMenuItem, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_8__.NavigationMenuLink, {\n                                                    asChild: true,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/hot-deals\",\n                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_10__.cn)(\"group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50\"),\n                                                        children: t('hotDeals')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                        lineNumber: 419,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                    lineNumber: 418,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 417,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_8__.NavigationMenuItem, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_8__.NavigationMenuLink, {\n                                                    asChild: true,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/products\",\n                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_10__.cn)(\"group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50\"),\n                                                        children: t('products') || 'Products'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                        lineNumber: 428,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                    lineNumber: 427,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 426,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_8__.NavigationMenuItem, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_8__.NavigationMenuLink, {\n                                                    asChild: true,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/payment-methods\",\n                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_10__.cn)(\"group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50\"),\n                                                        children: t('paymentMethods')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                        lineNumber: 437,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                    lineNumber: 436,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 435,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_8__.NavigationMenuItem, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_8__.NavigationMenuLink, {\n                                                    asChild: true,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/follow-us\",\n                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_10__.cn)(\"group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50\"),\n                                                        children: t('followUs')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                        lineNumber: 446,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                    lineNumber: 445,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 444,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_8__.NavigationMenuItem, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_8__.NavigationMenuLink, {\n                                                    asChild: true,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/about\",\n                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_10__.cn)(\"group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50\"),\n                                                        children: t('aboutUs')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                        lineNumber: 455,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                    lineNumber: 454,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 453,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_8__.NavigationMenuItem, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_8__.NavigationMenuLink, {\n                                                    asChild: true,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/contact\",\n                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_10__.cn)(\"group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50\"),\n                                                        children: t('contactUs')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                        lineNumber: 464,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                    lineNumber: 463,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 462,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                        lineNumber: 407,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                    lineNumber: 406,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                lineNumber: 405,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"hidden md:flex items-center gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/wishlist\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                    variant: \"ghost\",\n                                                    size: \"icon\",\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                            className: \"h-5 w-5\",\n                                                            style: {\n                                                                color: primaryColor\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                            lineNumber: 481,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"absolute -top-1 -right-1 bg-primary text-white text-xs rounded-full h-4 w-4 flex items-center justify-center\",\n                                                            children: wishlistCount\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                            lineNumber: 482,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                    lineNumber: 480,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 479,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/cart\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                    variant: \"ghost\",\n                                                    size: \"icon\",\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                            className: \"h-5 w-5\",\n                                                            style: {\n                                                                color: primaryColor\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                            lineNumber: 489,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"absolute -top-1 -right-1 bg-primary text-white text-xs rounded-full h-4 w-4 flex items-center justify-center\",\n                                                            children: cartCount\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                            lineNumber: 490,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                    lineNumber: 488,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 487,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                        lineNumber: 478,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"icon\",\n                                        className: \"md:hidden\",\n                                        onClick: ()=>setShowMobileMenu(!showMobileMenu),\n                                        \"aria-label\": \"Toggle menu\",\n                                        children: showMobileMenu ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                            className: \"h-6 w-6\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                            lineNumber: 505,\n                                            columnNumber: 33\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                            className: \"h-6 w-6\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                            lineNumber: 505,\n                                            columnNumber: 61\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                        lineNumber: 498,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                lineNumber: 476,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                        lineNumber: 332,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                lineNumber: 278,\n                columnNumber: 7\n            }, this),\n            showMobileMenu && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"md:hidden bg-white border-t border-gray-200 shadow-lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: \"px-4 py-4 space-y-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/\",\n                            className: \"block px-4 py-3 text-gray-700 hover:bg-gray-50 hover:text-primary rounded-lg transition-colors\",\n                            onClick: ()=>setShowMobileMenu(false),\n                            children: t('home')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                            lineNumber: 514,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/hot-deals\",\n                            className: \"block px-4 py-3 text-gray-700 hover:bg-gray-50 hover:text-primary rounded-lg transition-colors\",\n                            onClick: ()=>setShowMobileMenu(false),\n                            children: t('hotDeals')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                            lineNumber: 521,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/products\",\n                            className: \"block px-4 py-3 text-gray-700 hover:bg-gray-50 hover:text-primary rounded-lg transition-colors\",\n                            onClick: ()=>setShowMobileMenu(false),\n                            children: t('products') || 'Products'\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                            lineNumber: 528,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/payment-methods\",\n                            className: \"block px-4 py-3 text-gray-700 hover:bg-gray-50 hover:text-primary rounded-lg transition-colors\",\n                            onClick: ()=>setShowMobileMenu(false),\n                            children: t('paymentMethods')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                            lineNumber: 535,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/follow-us\",\n                            className: \"block px-4 py-3 text-gray-700 hover:bg-gray-50 hover:text-primary rounded-lg transition-colors\",\n                            onClick: ()=>setShowMobileMenu(false),\n                            children: t('followUs')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                            lineNumber: 542,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/about\",\n                            className: \"block px-4 py-3 text-gray-700 hover:bg-gray-50 hover:text-primary rounded-lg transition-colors\",\n                            onClick: ()=>setShowMobileMenu(false),\n                            children: t('aboutUs')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                            lineNumber: 549,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/contact\",\n                            className: \"block px-4 py-3 text-gray-700 hover:bg-gray-50 hover:text-primary rounded-lg transition-colors\",\n                            onClick: ()=>setShowMobileMenu(false),\n                            children: t('contactUs')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                            lineNumber: 556,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-t border-gray-200 pt-4 mt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/wishlist\",\n                                    className: \"flex items-center gap-3 px-4 py-3 text-gray-700 hover:bg-gray-50 hover:text-primary rounded-lg transition-colors\",\n                                    onClick: ()=>setShowMobileMenu(false),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                            lineNumber: 571,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Wishlist\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                            lineNumber: 572,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"ml-auto bg-primary text-white text-xs rounded-full h-5 w-5 flex items-center justify-center\",\n                                            children: wishlistCount\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                            lineNumber: 573,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                    lineNumber: 566,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/cart\",\n                                    className: \"flex items-center gap-3 px-4 py-3 text-gray-700 hover:bg-gray-50 hover:text-primary rounded-lg transition-colors\",\n                                    onClick: ()=>setShowMobileMenu(false),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                            lineNumber: 582,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Cart\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                            lineNumber: 583,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"ml-auto bg-primary text-white text-xs rounded-full h-5 w-5 flex items-center justify-center\",\n                                            children: cartCount\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                            lineNumber: 584,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                    lineNumber: 577,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                            lineNumber: 565,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-t border-gray-200 pt-4 mt-4\",\n                            children: isLoggedIn ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"px-4 py-2 text-sm text-gray-500\",\n                                        children: [\n                                            \"Welcome, \",\n                                            (user === null || user === void 0 ? void 0 : user.FirstName) || (user === null || user === void 0 ? void 0 : user.UserName)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                        lineNumber: 594,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/account\",\n                                        className: \"flex items-center gap-3 px-4 py-3 text-gray-700 hover:bg-gray-50 hover:text-primary rounded-lg transition-colors\",\n                                        onClick: ()=>setShowMobileMenu(false),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 602,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"My Account\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 603,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                        lineNumber: 597,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/orders\",\n                                        className: \"flex items-center gap-3 px-4 py-3 text-gray-700 hover:bg-gray-50 hover:text-primary rounded-lg transition-colors\",\n                                        onClick: ()=>setShowMobileMenu(false),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 610,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"My Orders\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 611,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                        lineNumber: 605,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/addresses\",\n                                        className: \"flex items-center gap-3 px-4 py-3 text-gray-700 hover:bg-gray-50 hover:text-primary rounded-lg transition-colors\",\n                                        onClick: ()=>setShowMobileMenu(false),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 618,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"My Addresses\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 619,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                        lineNumber: 613,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"flex items-center gap-3 w-full px-4 py-3 text-red-600 hover:bg-red-50 rounded-lg transition-colors\",\n                                        onClick: ()=>{\n                                            logout();\n                                            setShowMobileMenu(false);\n                                            router.push('/');\n                                            sonner__WEBPACK_IMPORTED_MODULE_6__.toast.success('Logged out successfully');\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 630,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Logout\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 631,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                        lineNumber: 621,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/login\",\n                                        className: \"flex items-center gap-3 px-4 py-3 text-gray-700 hover:bg-gray-50 hover:text-primary rounded-lg transition-colors\",\n                                        onClick: ()=>setShowMobileMenu(false),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 641,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: t('login')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 642,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                        lineNumber: 636,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/signup\",\n                                        className: \"flex items-center gap-3 px-4 py-3 text-gray-700 hover:bg-gray-50 hover:text-primary rounded-lg transition-colors\",\n                                        onClick: ()=>setShowMobileMenu(false),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 649,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: t('signUp')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 650,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                        lineNumber: 644,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                            lineNumber: 591,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                    lineNumber: 513,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                lineNumber: 512,\n                columnNumber: 9\n            }, this),\n            showColorPicker && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_color_picker__WEBPACK_IMPORTED_MODULE_14__.ColorPicker, {\n                onColorSelect: (color)=>{\n                    setPrimaryColor(color);\n                    setShowColorPicker(false);\n                },\n                onClose: ()=>setShowColorPicker(false)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                lineNumber: 660,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n        lineNumber: 164,\n        columnNumber: 5\n    }, this);\n}\n_s(Header, \"SDwA1+gYkbYv5bxFsancV1XGjp4=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter,\n        _contexts_cart_context__WEBPACK_IMPORTED_MODULE_4__.useCart,\n        _contexts_wishlist_context__WEBPACK_IMPORTED_MODULE_5__.useWishlist,\n        _contexts_user_context__WEBPACK_IMPORTED_MODULE_13__.useUser,\n        _contexts_settings_context__WEBPACK_IMPORTED_MODULE_12__.useSettings\n    ];\n});\n_c = Header;\nvar _c;\n$RefreshReg$(_c, \"Header\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2NvbXBvbmVudHMvdWkvaGVhZGVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQXNCc0I7QUFDbUM7QUFDNUI7QUFDZTtBQUNNO0FBQ1E7QUFDM0I7QUFDNkI7QUFRbkI7QUFLUjtBQUNBO0FBQ0M7QUFDd0I7QUFDUjtBQUNMO0FBUXRDLFNBQVNvQzs7SUFDZCxNQUFNQyxTQUFTdEIsMERBQVNBO0lBQ3hCLE1BQU0sQ0FBQ3VCLFlBQVlDLGNBQWMsR0FBR3JCLCtDQUFRQSxDQUFhLEVBQUU7SUFDM0QsTUFBTSxDQUFDc0IsV0FBV0MsYUFBYSxHQUFHdkIsK0NBQVFBLENBQUM7SUFDM0MsTUFBTSxDQUFDd0IsaUJBQWlCQyxtQkFBbUIsR0FBR3pCLCtDQUFRQSxDQUFDO0lBQ3ZELE1BQU0sQ0FBQzBCLGtCQUFrQkMsb0JBQW9CLEdBQUczQiwrQ0FBUUEsQ0FBZ0I7SUFDeEUsTUFBTSxDQUFDNEIsb0JBQW9CQyxzQkFBc0IsR0FBRzdCLCtDQUFRQSxDQUFnQjtJQUM1RSxNQUFNLENBQUM4QixxQkFBcUJDLHVCQUF1QixHQUFHL0IsK0NBQVFBLENBQWdCO0lBQzlFLE1BQU0sQ0FBQ2dDLGdCQUFnQkMsa0JBQWtCLEdBQUdqQywrQ0FBUUEsQ0FBQztJQUNyRCxNQUFNLENBQUNrQyxzQkFBc0JDLHdCQUF3QixHQUFHbkMsK0NBQVFBLENBQUM7SUFDakUsTUFBTSxDQUFDb0MsWUFBWUMsY0FBYyxHQUFHckMsK0NBQVFBLENBQUM7SUFDN0MsTUFBTSxDQUFDc0MsV0FBV0MsYUFBYSxHQUFHdkMsK0NBQVFBLENBQUM7SUFDM0MsTUFBTSxDQUFDd0MsZUFBZUMsaUJBQWlCLEdBQUd6QywrQ0FBUUEsQ0FBQztJQUNuRCxNQUFNMEMsT0FBT3pDLCtEQUFPQTtJQUNwQixNQUFNMEMsV0FBV3pDLHVFQUFXQTtJQUM1QixNQUFNLEVBQUUwQyxJQUFJLEVBQUVDLFVBQVUsRUFBRUMsTUFBTSxFQUFFLEdBQUc5QixnRUFBT0E7SUFDNUMsTUFBTSxFQUFFK0IsS0FBSyxFQUFFQyxRQUFRLEVBQUVDLFlBQVksRUFBRUMsZ0JBQWdCLEVBQUVDLFdBQVcsRUFBRUMsV0FBVyxFQUFFQyxlQUFlLEVBQUVDLENBQUMsRUFBRSxHQUFHdkMsd0VBQVdBO0lBRXJILE1BQU13QyxlQUFlO1FBQ25CLG1EQUFtRDtRQUNuRCxNQUFNQyxTQUFTLElBQUlDO1FBRW5CLElBQUlyQixZQUFZO1lBQ2RvQixPQUFPRSxNQUFNLENBQUMsVUFBVXRCO1FBQzFCO1FBRUEsSUFBSVIsb0JBQW9CO1lBQ3RCNEIsT0FBT0UsTUFBTSxDQUFDLFlBQVk5QixtQkFBbUIrQixRQUFRO1FBQ3ZEO1FBRUF4QyxPQUFPeUMsSUFBSSxDQUFDLGFBQStCLE9BQWxCSixPQUFPRyxRQUFRO0lBQzFDO0lBRUE1RCxnREFBU0E7NEJBQUM7WUFDUixNQUFNOEQ7b0RBQWtCO29CQUN0QixJQUFJOzRCQWNFQywwQkEwQk9BO3dCQXZDWCxNQUFNQyxRQUFROzRCQUNaLGNBQWM7NEJBQ2QsWUFBWTs0QkFDWixjQUFjOzRCQUNkLGFBQWE7d0JBQ2Y7d0JBQ0EsTUFBTUMsVUFBVTs0QkFDZCxnQkFBZ0I7NEJBQ2hCLFVBQVU7NEJBQ1YsaUJBQWlCLFlBQVlDLGFBQWFDLE9BQU8sQ0FBQzt3QkFDcEQ7d0JBQ0EsTUFBTUoscUJBQXFCLE1BQU0xRCxpRUFBZ0JBLENBQUNDLG1EQUFNQSxDQUFDOEQsZUFBZSxDQUFDQyxtQkFBbUIsRUFBRSxNQUFNTCxPQUFPQyxTQUFTLFFBQVE7d0JBRTVILElBQUlGLCtCQUFBQSwwQ0FBQUEsMkJBQUFBLG1CQUFvQk8sSUFBSSxjQUF4QlAsK0NBQUFBLHlCQUEwQk8sSUFBSSxFQUFFOzRCQUNsQyxJQUFJO2dDQUNGLE1BQU1DLGFBQWFDLEtBQUtDLEtBQUssQ0FBQ1YsbUJBQW1CTyxJQUFJLENBQUNBLElBQUk7Z0NBQzFELElBQUlJLE1BQU1DLE9BQU8sQ0FBQ0osYUFBYTtvQ0FDN0Isb0NBQW9DO29DQUNwQyxNQUFNSyxtQkFBbUJMLFdBQVdNLE1BQU07NkZBQUNDLENBQUFBLE1BQU8sQ0FBQ0EsSUFBSUMsZ0JBQWdCOztvQ0FDdkUsTUFBTUMsa0JBQWtCVCxXQUFXTSxNQUFNOzRGQUFDQyxDQUFBQSxNQUFPQSxJQUFJQyxnQkFBZ0I7O29DQUVyRSwrQ0FBK0M7b0NBQy9DLE1BQU1FLHNCQUFzQkwsaUJBQWlCTSxHQUFHO2dHQUFDQyxDQUFBQSxTQUFXO2dEQUMxREMsSUFBSUQsT0FBT0UsVUFBVTtnREFDckJDLE1BQU1ILE9BQU9JLElBQUk7Z0RBQ2pCQyxlQUFlUixnQkFDWkgsTUFBTTs0R0FBQ1ksQ0FBQUEsUUFBU0EsTUFBTVYsZ0JBQWdCLEtBQUtJLE9BQU9FLFVBQVU7MkdBQzVESCxHQUFHOzRHQUFDTyxDQUFBQSxRQUFTQSxNQUFNRixJQUFJOzs0Q0FDNUI7O29DQUVBakUsY0FBYzJEO2dDQUNoQixPQUFPO29DQUNMUyxRQUFRQyxLQUFLLENBQUMsb0NBQW9DcEI7b0NBQ2xEakQsY0FBYyxFQUFFO2dDQUNsQjs0QkFDRixFQUFFLE9BQU9zRSxZQUFZO2dDQUNuQkYsUUFBUUMsS0FBSyxDQUFDLGtDQUFrQ0M7Z0NBQ2hEdEUsY0FBYyxFQUFFOzRCQUNsQjt3QkFDRixPQUFPLElBQUl5QywrQkFBQUEsMENBQUFBLDRCQUFBQSxtQkFBb0JPLElBQUksY0FBeEJQLGdEQUFBQSwwQkFBMEI4QixZQUFZLEVBQUU7NEJBQ2pESCxRQUFRQyxLQUFLLENBQUMsY0FBYzVCLG1CQUFtQk8sSUFBSSxDQUFDdUIsWUFBWTs0QkFDaEV2RSxjQUFjLEVBQUU7d0JBQ2xCLE9BQU87NEJBQ0xvRSxRQUFRQyxLQUFLLENBQUM7NEJBQ2RyRSxjQUFjLEVBQUU7d0JBQ2xCO29CQUNGLEVBQUUsT0FBT3FFLE9BQU87d0JBQ2RELFFBQVFDLEtBQUssQ0FBQyw4QkFBOEJBO3dCQUM1Q3JFLGNBQWMsRUFBRTtvQkFDbEIsU0FBVTt3QkFDUkUsYUFBYTtvQkFDZjtnQkFDRjs7WUFFQXNDO1FBQ0Y7MkJBQUcsRUFBRTtJQUVMLDJDQUEyQztJQUMzQzlELGdEQUFTQTs0QkFBQztZQUNSLElBQUkyQyxRQUFRQSxLQUFLbUQsS0FBSyxFQUFFO2dCQUN0QnRELGFBQWFHLEtBQUtvRCxVQUFVO1lBQzlCO1FBQ0Y7MkJBQUc7UUFBQ3BEO1FBQU1BLGlCQUFBQSwyQkFBQUEsS0FBTW1ELEtBQUs7S0FBQztJQUV0QixtREFBbUQ7SUFDbkQ5RixnREFBU0E7NEJBQUM7WUFDUixJQUFJNEMsVUFBVTtnQkFDWkYsaUJBQWlCRSxTQUFTbUQsVUFBVTtZQUN0QztRQUNGOzJCQUFHO1FBQUNuRDtRQUFVQSxxQkFBQUEsK0JBQUFBLFNBQVVvRCxhQUFhO0tBQUM7SUFFdEMscUJBQ0UsOERBQUNDO1FBQU9DLFdBQVU7OzBCQUNoQiw4REFBQ25GLDRDQUFNQTtnQkFDTG9GLFNBQVE7Z0JBQ1JDLE1BQUs7Z0JBQ0xGLFdBQVU7Z0JBQ1ZHLFNBQVMsSUFBTTNFLG1CQUFtQjswQkFFbEMsNEVBQUM0RTtvQkFBSUosV0FBVTs7c0NBQ2IsOERBQUNJOzRCQUFJSixXQUFVOzRCQUErSUssT0FBTztnQ0FBRUMsaUJBQWlCdEQ7NEJBQWE7Ozs7OztzQ0FDck0sOERBQUNvRDs0QkFBSUosV0FBVTs7Ozs7O3NDQUNmLDhEQUFDSTs0QkFBSUosV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBS25CLDhEQUFDSTtnQkFBSUosV0FBVTtnQkFBeUJLLE9BQU87b0JBQUVDLGlCQUFpQnREO29CQUFjdUQsT0FBT3REO2dCQUFpQjswQkFDdEcsNEVBQUNtRDtvQkFBSUosV0FBVTs7c0NBQ2IsOERBQUNJOzRCQUFJSixXQUFVOzs4Q0FDYiw4REFBQ25HLGtEQUFJQTtvQ0FBQzJHLE1BQUs7b0NBQXNCUixXQUFVOztzREFDekMsOERBQUNuSCw0TEFBS0E7NENBQUNtSCxXQUFVOzs7Ozs7c0RBQ2pCLDhEQUFDUzs0Q0FBS1QsV0FBVTtzREFBc0IzQyxFQUFFOzs7Ozs7Ozs7Ozs7OENBRTFDLDhEQUFDeEQsa0RBQUlBO29DQUFDMkcsTUFBSztvQ0FBa0NSLFdBQVU7O3NEQUNyRCw4REFBQ2xILDRMQUFJQTs0Q0FBQ2tILFdBQVU7Ozs7OztzREFDaEIsOERBQUNTOzRDQUFLVCxXQUFVO3NEQUFzQjNDLEVBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7OztzQ0FHNUMsOERBQUMrQzs0QkFBSUosV0FBVTs7OENBQ2IsOERBQUNuRiw0Q0FBTUE7b0NBQUNvRixTQUFRO29DQUFRQyxNQUFLO29DQUFLRixXQUFVO29DQUFvRUcsU0FBUyxJQUFNaEQsWUFBWUosYUFBYSxPQUFPLE9BQU87O3NEQUNwSyw4REFBQ3hELDRMQUFLQTs0Q0FBQ3lHLFdBQVU7Ozs7OztzREFDakIsOERBQUNTOzRDQUFLVCxXQUFVO3NEQUFXakQsYUFBYSxPQUFPLFlBQVk7Ozs7Ozs7Ozs7Ozs4Q0FFN0QsOERBQUNsQyw0Q0FBTUE7b0NBQ0xvRixTQUFRO29DQUNSQyxNQUFLO29DQUNMRixXQUFVO29DQUNWRyxTQUFTLElBQU1PLE9BQU9DLElBQUksQ0FBQyxvQ0FBd0gsT0FBcEZDLG1CQUFtQixtRUFBb0U7O3NEQUV0Siw4REFBQzdILDRMQUFhQTs0Q0FBQ2lILFdBQVU7Ozs7OztzREFDekIsOERBQUNTOzRDQUFLVCxXQUFVO3NEQUFXM0MsRUFBRTs7Ozs7Ozs7Ozs7O2dDQUU5QlQsMkJBQ0MsOERBQUNuQywyREFBT0E7O3NEQUNOLDhEQUFDRSxrRUFBY0E7NENBQUNrRyxPQUFPO3NEQUNyQiw0RUFBQ2hHLDRDQUFNQTtnREFBQ29GLFNBQVE7Z0RBQVFDLE1BQUs7Z0RBQUtGLFdBQVU7O2tFQUMxQyw4REFBQ2hILDRMQUFJQTt3REFBQ2dILFdBQVU7Ozs7OztrRUFDaEIsOERBQUNTO3dEQUFLVCxXQUFVOzs0REFBVTs0REFBVXJELENBQUFBLGlCQUFBQSwyQkFBQUEsS0FBTW1FLFNBQVMsTUFBSW5FLGlCQUFBQSwyQkFBQUEsS0FBTW9FLFFBQVE7Ozs7Ozs7a0VBQ3JFLDhEQUFDekgsNExBQVdBO3dEQUFDMEcsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7c0RBRzNCLDhEQUFDdEYsa0VBQWNBOzRDQUFDc0YsV0FBVTs0Q0FBcURnQixPQUFNO3NEQUNuRiw0RUFBQ1o7Z0RBQUlKLFdBQVU7O2tFQUNiLDhEQUFDbkcsa0RBQUlBO3dEQUFDMkcsTUFBSztrRUFDVCw0RUFBQzNGLDRDQUFNQTs0REFBQ29GLFNBQVE7NERBQVFDLE1BQUs7NERBQUtGLFdBQVU7OzhFQUMxQyw4REFBQ2hILDRMQUFJQTtvRUFBQ2dILFdBQVU7Ozs7OztnRUFBaUI7Ozs7Ozs7Ozs7OztrRUFJckMsOERBQUNuRyxrREFBSUE7d0RBQUMyRyxNQUFLO2tFQUNULDRFQUFDM0YsNENBQU1BOzREQUFDb0YsU0FBUTs0REFBUUMsTUFBSzs0REFBS0YsV0FBVTs7OEVBQzFDLDhEQUFDdkcsNExBQU9BO29FQUFDdUcsV0FBVTs7Ozs7O2dFQUFpQjs7Ozs7Ozs7Ozs7O2tFQUl4Qyw4REFBQ25HLGtEQUFJQTt3REFBQzJHLE1BQUs7a0VBQ1QsNEVBQUMzRiw0Q0FBTUE7NERBQUNvRixTQUFROzREQUFRQyxNQUFLOzREQUFLRixXQUFVOzs4RUFDMUMsOERBQUNyRyw0TEFBTUE7b0VBQUNxRyxXQUFVOzs7Ozs7Z0VBQWlCOzs7Ozs7Ozs7Ozs7a0VBSXZDLDhEQUFDbkcsa0RBQUlBO3dEQUFDMkcsTUFBSztrRUFDVCw0RUFBQzNGLDRDQUFNQTs0REFBQ29GLFNBQVE7NERBQVFDLE1BQUs7NERBQUtGLFdBQVU7OzhFQUMxQyw4REFBQzlHLDRMQUFLQTtvRUFBQzhHLFdBQVU7Ozs7OztnRUFBaUI7Ozs7Ozs7Ozs7OztrRUFJdEMsOERBQUNJO3dEQUFJSixXQUFVOzs7Ozs7a0VBQ2YsOERBQUNuRiw0Q0FBTUE7d0RBQ0xvRixTQUFRO3dEQUNSQyxNQUFLO3dEQUNMRixXQUFVO3dEQUNWRyxTQUFTOzREQUNQdEQ7NERBQ0EzQixPQUFPeUMsSUFBSSxDQUFDOzREQUNaekQseUNBQUtBLENBQUMrRyxPQUFPLENBQUM7d0RBQ2hCOzswRUFFQSw4REFBQ3ZILDRMQUFNQTtnRUFBQ3NHLFdBQVU7Ozs7Ozs0REFBaUI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3lEQU8zQzs7c0RBQ0UsOERBQUNuRyxrREFBSUE7NENBQUMyRyxNQUFLO3NEQUNULDRFQUFDM0YsNENBQU1BO2dEQUFDb0YsU0FBUTtnREFBUUMsTUFBSztnREFBS0YsV0FBVTs7a0VBQzFDLDhEQUFDaEgsNExBQUlBO3dEQUFDZ0gsV0FBVTs7Ozs7O2tFQUNoQiw4REFBQ1M7d0RBQUtULFdBQVU7a0VBQVczQyxFQUFFOzs7Ozs7Ozs7Ozs7Ozs7OztzREFHakMsOERBQUN4RCxrREFBSUE7NENBQUMyRyxNQUFLO3NEQUNULDRFQUFDM0YsNENBQU1BO2dEQUFDb0YsU0FBUTtnREFBUUMsTUFBSztnREFBS0YsV0FBVTs7a0VBQzFDLDhEQUFDL0csNExBQVFBO3dEQUFDK0csV0FBVTs7Ozs7O2tFQUNwQiw4REFBQ1M7d0RBQUtULFdBQVU7a0VBQVczQyxFQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBVTNDLDhEQUFDK0M7Z0JBQUlKLFdBQVU7O2tDQUViLDhEQUFDSTt3QkFBSUosV0FBVTs7MENBRWIsOERBQUNJO2dDQUFJSixXQUFVOztrREFFYiw4REFBQ0k7d0NBQUlKLFdBQVU7Ozs7OztrREFHZiw4REFBQ25HLGtEQUFJQTt3Q0FBQzJHLE1BQUs7d0NBQUlSLFdBQVU7a0RBQ3ZCLDRFQUFDSTs0Q0FBSUosV0FBVTtzREFDYiw0RUFBQ2tCO2dEQUFJQyxLQUFLLEdBQTBDLE9BQXZDQyxvQ0FBc0MsRUFBQztnREFBa0RHLEtBQUk7Z0RBQU92QixXQUFVOzs7Ozs7Ozs7Ozs7Ozs7O2tEQUsvSCw4REFBQ25GLDRDQUFNQTt3Q0FDTG9GLFNBQVE7d0NBQ1JDLE1BQUs7d0NBQ0xGLFdBQVU7d0NBQ1ZHLFNBQVMsSUFBTWhELFlBQVlKLGFBQWEsT0FBTyxPQUFPOzswREFFdEQsOERBQUMwRDtnREFBS1QsV0FBVTswREFDYmpELGFBQWEsT0FBTyxTQUFTOzs7Ozs7MERBRWhDLDhEQUFDMEQ7Z0RBQUtULFdBQVU7MERBQ2JqRCxhQUFhLE9BQU8sT0FBTzs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQU1sQyw4REFBQ3FEO2dDQUFJSixXQUFVOzBDQUNiLDRFQUFDSTtvQ0FBSUosV0FBVTs7c0RBQ2IsOERBQUN3Qjs0Q0FDQ0MsTUFBSzs0Q0FDTEMsYUFBYXJFLEVBQUUsZUFBZTs0Q0FDOUIyQyxXQUFVOzRDQUNWMkIsT0FBT3hGOzRDQUNQeUYsVUFBVSxDQUFDQyxJQUFNekYsY0FBY3lGLEVBQUVDLE1BQU0sQ0FBQ0gsS0FBSzs0Q0FDN0NJLFdBQVcsQ0FBQ0YsSUFBTUEsRUFBRUcsR0FBRyxLQUFLLFdBQVcxRTs7Ozs7O3NEQUV6Qyw4REFBQ3pDLDRDQUFNQTs0Q0FDTG9GLFNBQVE7NENBQ1JELFdBQVU7NENBQ1ZLLE9BQU87Z0RBQUVFLE9BQU92RDs0Q0FBYTs0Q0FDN0JtRCxTQUFTN0M7c0RBRVQsNEVBQUNqRSw0TEFBTUE7Z0RBQUMyRyxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQU0xQiw4REFBQ0k7d0JBQUlKLFdBQVU7OzBDQUViLDhEQUFDSTtnQ0FBSUosV0FBVTs7a0RBRWIsOERBQUNuRyxrREFBSUE7d0NBQUMyRyxNQUFLO3dDQUFJUixXQUFVO2tEQUN2Qiw0RUFBQ0k7NENBQUlKLFdBQVU7c0RBQ2IsNEVBQUNrQjtnREFBSUMsS0FBSyxHQUEwQyxPQUF2Q0Msb0NBQXNDLEVBQUM7Z0RBQWtERyxLQUFJO2dEQUFPdkIsV0FBVTs7Ozs7Ozs7Ozs7Ozs7OztrREFLL0gsOERBQUNJO3dDQUFJSixXQUFVOzswREFDYiw4REFBQ3ZGLDJEQUFPQTs7a0VBQ04sOERBQUNFLGtFQUFjQTt3REFBQ2tHLE9BQU87a0VBQ3JCLDRFQUFDaEcsNENBQU1BOzREQUFDb0YsU0FBUTs0REFBUUQsV0FBVTs7OEVBQ2hDLDhEQUFDUztvRUFBS1QsV0FBVTs4RUFBaUN2RSxvQkFBb0JJLHVCQUF1QndCLEVBQUU7Ozs7Ozs4RUFDOUYsOERBQUMvRCw0TEFBV0E7b0VBQUMwRyxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7OztrRUFHM0IsOERBQUN0RixrRUFBY0E7d0RBQUNzRixXQUFVO3dEQUFxRGdCLE9BQU07a0VBQ25GLDRFQUFDWjs0REFBSUosV0FBVTtzRUFDWjNFLDBCQUNDLDhEQUFDK0U7Z0VBQUlKLFdBQVU7MEVBQXlDM0MsRUFBRTs7Ozs7cUZBRTFELDhEQUFDK0M7Z0VBQUlKLFdBQVU7MEVBQ1o3RSxXQUFXNkQsR0FBRyxDQUFDLENBQUNpRCx5QkFDZiw4REFBQzdCO3dFQUFzQkosV0FBVTs7MEZBQy9CLDhEQUFDa0M7Z0ZBQU9sQyxXQUFVO2dGQUE4Q0csU0FBUztvRkFDdkV6RSxvQkFBb0J1RyxTQUFTN0MsSUFBSTtvRkFDakN4RCxzQkFBc0JxRyxTQUFTL0MsRUFBRTtvRkFDakNwRCx1QkFBdUI7Z0ZBQ3pCOzBGQUNHbUcsU0FBUzdDLElBQUk7Ozs7OzswRkFFaEIsOERBQUNnQjtnRkFBSUosV0FBVTswRkFDWmlDLFNBQVMzQyxhQUFhLENBQUNOLEdBQUcsQ0FBQyxDQUFDbUQsS0FBS0Msc0JBQ2hDLDhEQUFDRjt3RkFFQ2xDLFdBQVU7d0ZBQ1ZHLFNBQVM7NEZBQ1ByRSx1QkFBdUJxRzs0RkFDdkJ6RyxvQkFBb0I7NEZBQ3BCLGtEQUFrRDs0RkFDbERFLHNCQUFzQnFHLFNBQVMvQyxFQUFFO3dGQUNuQztrR0FFQ2lEO3VGQVRJQzs7Ozs7Ozs7Ozs7dUVBWEhILFNBQVMvQyxFQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswREErQmpDLDhEQUFDa0I7Z0RBQUlKLFdBQVU7Ozs7OzswREFDZiw4REFBQ3dCO2dEQUNDQyxNQUFLO2dEQUNMQyxhQUFhckUsRUFBRTtnREFDZjJDLFdBQVU7Z0RBQ1YyQixPQUFPeEY7Z0RBQ1B5RixVQUFVLENBQUNDLElBQU16RixjQUFjeUYsRUFBRUMsTUFBTSxDQUFDSCxLQUFLO2dEQUM3Q0ksV0FBVyxDQUFDRixJQUFNQSxFQUFFRyxHQUFHLEtBQUssV0FBVzFFOzs7Ozs7MERBRXpDLDhEQUFDekMsNENBQU1BO2dEQUFDb0YsU0FBUTtnREFBUUQsV0FBVTtnREFBY0csU0FBUzdDOzBEQUN2RCw0RUFBQ2pFLDRMQUFNQTtvREFBQzJHLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQU14Qiw4REFBQ0k7Z0NBQUlKLFdBQVU7MENBQ2IsNEVBQUMzRiwwRUFBY0E7OENBQ2IsNEVBQUNHLDhFQUFrQkE7OzBEQUNqQiw4REFBQ0YsOEVBQWtCQTswREFDakIsNEVBQUNDLDhFQUFrQkE7b0RBQUNzRyxPQUFPOzhEQUN6Qiw0RUFBQ2hILGtEQUFJQTt3REFBQzJHLE1BQUs7d0RBQUlSLFdBQVdwRiwrQ0FBRUEsQ0FDMUI7a0VBRUN5QyxFQUFFOzs7Ozs7Ozs7Ozs7Ozs7OzBEQUlULDhEQUFDL0MsOEVBQWtCQTswREFDakIsNEVBQUNDLDhFQUFrQkE7b0RBQUNzRyxPQUFPOzhEQUN6Qiw0RUFBQ2hILGtEQUFJQTt3REFBQzJHLE1BQUs7d0RBQWFSLFdBQVdwRiwrQ0FBRUEsQ0FDbkM7a0VBRUN5QyxFQUFFOzs7Ozs7Ozs7Ozs7Ozs7OzBEQUlULDhEQUFDL0MsOEVBQWtCQTswREFDakIsNEVBQUNDLDhFQUFrQkE7b0RBQUNzRyxPQUFPOzhEQUN6Qiw0RUFBQ2hILGtEQUFJQTt3REFBQzJHLE1BQUs7d0RBQVlSLFdBQVdwRiwrQ0FBRUEsQ0FDbEM7a0VBRUN5QyxFQUFFLGVBQWU7Ozs7Ozs7Ozs7Ozs7Ozs7MERBSXhCLDhEQUFDL0MsOEVBQWtCQTswREFDakIsNEVBQUNDLDhFQUFrQkE7b0RBQUNzRyxPQUFPOzhEQUN6Qiw0RUFBQ2hILGtEQUFJQTt3REFBQzJHLE1BQUs7d0RBQW1CUixXQUFXcEYsK0NBQUVBLENBQ3pDO2tFQUVDeUMsRUFBRTs7Ozs7Ozs7Ozs7Ozs7OzswREFJVCw4REFBQy9DLDhFQUFrQkE7MERBQ2pCLDRFQUFDQyw4RUFBa0JBO29EQUFDc0csT0FBTzs4REFDekIsNEVBQUNoSCxrREFBSUE7d0RBQUMyRyxNQUFLO3dEQUFhUixXQUFXcEYsK0NBQUVBLENBQ25DO2tFQUVDeUMsRUFBRTs7Ozs7Ozs7Ozs7Ozs7OzswREFJVCw4REFBQy9DLDhFQUFrQkE7MERBQ2pCLDRFQUFDQyw4RUFBa0JBO29EQUFDc0csT0FBTzs4REFDekIsNEVBQUNoSCxrREFBSUE7d0RBQUMyRyxNQUFLO3dEQUFTUixXQUFXcEYsK0NBQUVBLENBQy9CO2tFQUVDeUMsRUFBRTs7Ozs7Ozs7Ozs7Ozs7OzswREFJVCw4REFBQy9DLDhFQUFrQkE7MERBQ2pCLDRFQUFDQyw4RUFBa0JBO29EQUFDc0csT0FBTzs4REFDekIsNEVBQUNoSCxrREFBSUE7d0RBQUMyRyxNQUFLO3dEQUFXUixXQUFXcEYsK0NBQUVBLENBQ2pDO2tFQUVDeUMsRUFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MENBU2YsOERBQUMrQztnQ0FBSUosV0FBVTs7a0RBRWIsOERBQUNJO3dDQUFJSixXQUFVOzswREFDYiw4REFBQ25HLGtEQUFJQTtnREFBQzJHLE1BQUs7MERBQ1QsNEVBQUMzRiw0Q0FBTUE7b0RBQUNvRixTQUFRO29EQUFRQyxNQUFLO29EQUFPRixXQUFVOztzRUFDNUMsOERBQUM5Ryw0TEFBS0E7NERBQUM4RyxXQUFVOzREQUFVSyxPQUFPO2dFQUFFRSxPQUFPdkQ7NERBQWE7Ozs7OztzRUFDeEQsOERBQUN5RDs0REFBS1QsV0FBVTtzRUFDYnpEOzs7Ozs7Ozs7Ozs7Ozs7OzswREFJUCw4REFBQzFDLGtEQUFJQTtnREFBQzJHLE1BQUs7MERBQ1QsNEVBQUMzRiw0Q0FBTUE7b0RBQUNvRixTQUFRO29EQUFRQyxNQUFLO29EQUFPRixXQUFVOztzRUFDNUMsOERBQUM3Ryw0TEFBWUE7NERBQUM2RyxXQUFVOzREQUFVSyxPQUFPO2dFQUFFRSxPQUFPdkQ7NERBQWE7Ozs7OztzRUFDL0QsOERBQUN5RDs0REFBS1QsV0FBVTtzRUFDYjNEOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrREFPVCw4REFBQ3hCLDRDQUFNQTt3Q0FDTG9GLFNBQVE7d0NBQ1JDLE1BQUs7d0NBQ0xGLFdBQVU7d0NBQ1ZHLFNBQVMsSUFBTW5FLGtCQUFrQixDQUFDRDt3Q0FDbENzRyxjQUFXO2tEQUVWdEcsK0JBQWlCLDhEQUFDdkMsNExBQUNBOzRDQUFDd0csV0FBVTs7Ozs7aUVBQWUsOERBQUM1Ryw0TEFBSUE7NENBQUM0RyxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztZQU1yRWpFLGdDQUNDLDhEQUFDcUU7Z0JBQUlKLFdBQVU7MEJBQ2IsNEVBQUNzQztvQkFBSXRDLFdBQVU7O3NDQUNiLDhEQUFDbkcsa0RBQUlBOzRCQUNIMkcsTUFBSzs0QkFDTFIsV0FBVTs0QkFDVkcsU0FBUyxJQUFNbkUsa0JBQWtCO3NDQUVoQ3FCLEVBQUU7Ozs7OztzQ0FFTCw4REFBQ3hELGtEQUFJQTs0QkFDSDJHLE1BQUs7NEJBQ0xSLFdBQVU7NEJBQ1ZHLFNBQVMsSUFBTW5FLGtCQUFrQjtzQ0FFaENxQixFQUFFOzs7Ozs7c0NBRUwsOERBQUN4RCxrREFBSUE7NEJBQ0gyRyxNQUFLOzRCQUNMUixXQUFVOzRCQUNWRyxTQUFTLElBQU1uRSxrQkFBa0I7c0NBRWhDcUIsRUFBRSxlQUFlOzs7Ozs7c0NBRXBCLDhEQUFDeEQsa0RBQUlBOzRCQUNIMkcsTUFBSzs0QkFDTFIsV0FBVTs0QkFDVkcsU0FBUyxJQUFNbkUsa0JBQWtCO3NDQUVoQ3FCLEVBQUU7Ozs7OztzQ0FFTCw4REFBQ3hELGtEQUFJQTs0QkFDSDJHLE1BQUs7NEJBQ0xSLFdBQVU7NEJBQ1ZHLFNBQVMsSUFBTW5FLGtCQUFrQjtzQ0FFaENxQixFQUFFOzs7Ozs7c0NBRUwsOERBQUN4RCxrREFBSUE7NEJBQ0gyRyxNQUFLOzRCQUNMUixXQUFVOzRCQUNWRyxTQUFTLElBQU1uRSxrQkFBa0I7c0NBRWhDcUIsRUFBRTs7Ozs7O3NDQUVMLDhEQUFDeEQsa0RBQUlBOzRCQUNIMkcsTUFBSzs0QkFDTFIsV0FBVTs0QkFDVkcsU0FBUyxJQUFNbkUsa0JBQWtCO3NDQUVoQ3FCLEVBQUU7Ozs7OztzQ0FJTCw4REFBQytDOzRCQUFJSixXQUFVOzs4Q0FDYiw4REFBQ25HLGtEQUFJQTtvQ0FDSDJHLE1BQUs7b0NBQ0xSLFdBQVU7b0NBQ1ZHLFNBQVMsSUFBTW5FLGtCQUFrQjs7c0RBRWpDLDhEQUFDOUMsNExBQUtBOzRDQUFDOEcsV0FBVTs7Ozs7O3NEQUNqQiw4REFBQ1M7c0RBQUs7Ozs7OztzREFDTiw4REFBQ0E7NENBQUtULFdBQVU7c0RBQ2J6RDs7Ozs7Ozs7Ozs7OzhDQUdMLDhEQUFDMUMsa0RBQUlBO29DQUNIMkcsTUFBSztvQ0FDTFIsV0FBVTtvQ0FDVkcsU0FBUyxJQUFNbkUsa0JBQWtCOztzREFFakMsOERBQUM3Qyw0TEFBWUE7NENBQUM2RyxXQUFVOzs7Ozs7c0RBQ3hCLDhEQUFDUztzREFBSzs7Ozs7O3NEQUNOLDhEQUFDQTs0Q0FBS1QsV0FBVTtzREFDYjNEOzs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBTVAsOERBQUMrRDs0QkFBSUosV0FBVTtzQ0FDWnBELDJCQUNDOztrREFDRSw4REFBQ3dEO3dDQUFJSixXQUFVOzs0Q0FBa0M7NENBQ3JDckQsQ0FBQUEsaUJBQUFBLDJCQUFBQSxLQUFNbUUsU0FBUyxNQUFJbkUsaUJBQUFBLDJCQUFBQSxLQUFNb0UsUUFBUTs7Ozs7OztrREFFN0MsOERBQUNsSCxrREFBSUE7d0NBQ0gyRyxNQUFLO3dDQUNMUixXQUFVO3dDQUNWRyxTQUFTLElBQU1uRSxrQkFBa0I7OzBEQUVqQyw4REFBQ2hELDRMQUFJQTtnREFBQ2dILFdBQVU7Ozs7OzswREFDaEIsOERBQUNTOzBEQUFLOzs7Ozs7Ozs7Ozs7a0RBRVIsOERBQUM1RyxrREFBSUE7d0NBQ0gyRyxNQUFLO3dDQUNMUixXQUFVO3dDQUNWRyxTQUFTLElBQU1uRSxrQkFBa0I7OzBEQUVqQyw4REFBQ3ZDLDRMQUFPQTtnREFBQ3VHLFdBQVU7Ozs7OzswREFDbkIsOERBQUNTOzBEQUFLOzs7Ozs7Ozs7Ozs7a0RBRVIsOERBQUM1RyxrREFBSUE7d0NBQ0gyRyxNQUFLO3dDQUNMUixXQUFVO3dDQUNWRyxTQUFTLElBQU1uRSxrQkFBa0I7OzBEQUVqQyw4REFBQ3JDLDRMQUFNQTtnREFBQ3FHLFdBQVU7Ozs7OzswREFDbEIsOERBQUNTOzBEQUFLOzs7Ozs7Ozs7Ozs7a0RBRVIsOERBQUN5Qjt3Q0FDQ2xDLFdBQVU7d0NBQ1ZHLFNBQVM7NENBQ1B0RDs0Q0FDQWIsa0JBQWtCOzRDQUNsQmQsT0FBT3lDLElBQUksQ0FBQzs0Q0FDWnpELHlDQUFLQSxDQUFDK0csT0FBTyxDQUFDO3dDQUNoQjs7MERBRUEsOERBQUN2SCw0TEFBTUE7Z0RBQUNzRyxXQUFVOzs7Ozs7MERBQ2xCLDhEQUFDUzswREFBSzs7Ozs7Ozs7Ozs7Ozs2REFJVjs7a0RBQ0UsOERBQUM1RyxrREFBSUE7d0NBQ0gyRyxNQUFLO3dDQUNMUixXQUFVO3dDQUNWRyxTQUFTLElBQU1uRSxrQkFBa0I7OzBEQUVqQyw4REFBQ2hELDRMQUFJQTtnREFBQ2dILFdBQVU7Ozs7OzswREFDaEIsOERBQUNTOzBEQUFNcEQsRUFBRTs7Ozs7Ozs7Ozs7O2tEQUVYLDhEQUFDeEQsa0RBQUlBO3dDQUNIMkcsTUFBSzt3Q0FDTFIsV0FBVTt3Q0FDVkcsU0FBUyxJQUFNbkUsa0JBQWtCOzswREFFakMsOERBQUMvQyw0TEFBUUE7Z0RBQUMrRyxXQUFVOzs7Ozs7MERBQ3BCLDhEQUFDUzswREFBTXBELEVBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztZQVN0QjlCLGlDQUNDLDhEQUFDUCx1REFBV0E7Z0JBQ1Z1SCxlQUFlLENBQUNoQztvQkFDZG5ELGdCQUFnQm1EO29CQUNoQi9FLG1CQUFtQjtnQkFDckI7Z0JBQ0FnSCxTQUFTLElBQU1oSCxtQkFBbUI7Ozs7Ozs7Ozs7OztBQUs1QztHQXRtQmdCUDs7UUFDQ3JCLHNEQUFTQTtRQVlYSSwyREFBT0E7UUFDSEMsbUVBQVdBO1FBQ1NjLDREQUFPQTtRQUM4REQsb0VBQVdBOzs7S0FoQnZHRyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFx5aHlhc29mdFxcRG93bmxvYWRzXFxlY1xcLk5FVCA4IFZlcnNpb24gLSBMYXRlc3RcXHByb2plY3RcXGNvZGVtZWRpY2FsXFxwcm9qZWN0M1xcY29tcG9uZW50c1xcdWlcXGhlYWRlci50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xyXG5cclxuaW1wb3J0IHtcclxuICBQaG9uZSxcclxuICBNYWlsLFxyXG4gIENsb2NrLFxyXG4gIE1lc3NhZ2VDaXJjbGUsXHJcbiAgVXNlcixcclxuICBVc2VyUGx1cyxcclxuICBIZWFydCxcclxuICBTaG9wcGluZ0NhcnQsXHJcbiAgTWVudSxcclxuICBTZWFyY2gsXHJcbiAgQ2hldnJvbkRvd24sXHJcbiAgU3VuLFxyXG4gIE1vb24sXHJcbiAgR2xvYmUsXHJcbiAgQ2hldnJvblJpZ2h0LFxyXG4gIFgsXHJcbiAgUGFja2FnZSxcclxuICBMb2dPdXQsXHJcbiAgTWFwUGluXHJcbn0gZnJvbSAnbHVjaWRlLXJlYWN0JztcclxuaW1wb3J0IHsgdXNlUm91dGVyLCB1c2VQYXRobmFtZSB9IGZyb20gJ25leHQvbmF2aWdhdGlvbic7XHJcbmltcG9ydCBMaW5rIGZyb20gJ25leHQvbGluayc7XHJcbmltcG9ydCB7IHVzZUVmZmVjdCwgdXNlU3RhdGUgfSBmcm9tICdyZWFjdCc7XHJcbmltcG9ydCB7IHVzZUNhcnQgfSBmcm9tICdAL2NvbnRleHRzL2NhcnQtY29udGV4dCc7XHJcbmltcG9ydCB7IHVzZVdpc2hsaXN0IH0gZnJvbSAnQC9jb250ZXh0cy93aXNobGlzdC1jb250ZXh0JztcclxuaW1wb3J0IHsgdG9hc3QgfSBmcm9tICdzb25uZXInO1xyXG5pbXBvcnQgeyBNYWtlQXBpQ2FsbEFzeW5jLCBDb25maWcgfSBmcm9tICdAL2xpYi9hcGktaGVscGVyJztcclxuaW1wb3J0IHtcclxuICBOYXZpZ2F0aW9uTWVudSxcclxuICBOYXZpZ2F0aW9uTWVudUNvbnRlbnQsXHJcbiAgTmF2aWdhdGlvbk1lbnVJdGVtLFxyXG4gIE5hdmlnYXRpb25NZW51TGluayxcclxuICBOYXZpZ2F0aW9uTWVudUxpc3QsXHJcbiAgTmF2aWdhdGlvbk1lbnVUcmlnZ2VyLFxyXG59IGZyb20gJ0AvY29tcG9uZW50cy91aS9uYXZpZ2F0aW9uLW1lbnUnO1xyXG5pbXBvcnQge1xyXG4gIFBvcG92ZXIsXHJcbiAgUG9wb3ZlckNvbnRlbnQsXHJcbiAgUG9wb3ZlclRyaWdnZXIsXHJcbn0gZnJvbSAnQC9jb21wb25lbnRzL3VpL3BvcG92ZXInO1xyXG5pbXBvcnQgeyBjbiB9IGZyb20gJ0AvbGliL3V0aWxzJztcclxuaW1wb3J0IHsgQnV0dG9uIH0gZnJvbSAnLi9idXR0b24nO1xyXG5pbXBvcnQgeyB1c2VTZXR0aW5ncyB9IGZyb20gJ0AvY29udGV4dHMvc2V0dGluZ3MtY29udGV4dCc7XHJcbmltcG9ydCB7IHVzZVVzZXIgfSBmcm9tICdAL2NvbnRleHRzL3VzZXItY29udGV4dCc7XHJcbmltcG9ydCB7IENvbG9yUGlja2VyIH0gZnJvbSAnLi9jb2xvci1waWNrZXInO1xyXG5cclxudHlwZSBDYXRlZ29yeSA9IHtcclxuICBpZDogbnVtYmVyO1xyXG4gIG5hbWU6IHN0cmluZztcclxuICBzdWJjYXRlZ29yaWVzOiBzdHJpbmdbXTtcclxufTtcclxuXHJcbmV4cG9ydCBmdW5jdGlvbiBIZWFkZXIoKSB7XHJcbiAgY29uc3Qgcm91dGVyID0gdXNlUm91dGVyKCk7XHJcbiAgY29uc3QgW2NhdGVnb3JpZXMsIHNldENhdGVnb3JpZXNdID0gdXNlU3RhdGU8Q2F0ZWdvcnlbXT4oW10pO1xyXG4gIGNvbnN0IFtpc0xvYWRpbmcsIHNldElzTG9hZGluZ10gPSB1c2VTdGF0ZSh0cnVlKTtcclxuICBjb25zdCBbc2hvd0NvbG9yUGlja2VyLCBzZXRTaG93Q29sb3JQaWNrZXJdID0gdXNlU3RhdGUoZmFsc2UpO1xyXG4gIGNvbnN0IFtzZWxlY3RlZENhdGVnb3J5LCBzZXRTZWxlY3RlZENhdGVnb3J5XSA9IHVzZVN0YXRlPHN0cmluZyB8IG51bGw+KG51bGwpO1xyXG4gIGNvbnN0IFtzZWxlY3RlZENhdGVnb3J5SWQsIHNldFNlbGVjdGVkQ2F0ZWdvcnlJZF0gPSB1c2VTdGF0ZTxudW1iZXIgfCBudWxsPihudWxsKTtcclxuICBjb25zdCBbc2VsZWN0ZWRTdWJjYXRlZ29yeSwgc2V0U2VsZWN0ZWRTdWJjYXRlZ29yeV0gPSB1c2VTdGF0ZTxzdHJpbmcgfCBudWxsPihudWxsKTtcclxuICBjb25zdCBbc2hvd01vYmlsZU1lbnUsIHNldFNob3dNb2JpbGVNZW51XSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuICBjb25zdCBbc2hvd01vYmlsZUNhdGVnb3JpZXMsIHNldFNob3dNb2JpbGVDYXRlZ29yaWVzXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuICBjb25zdCBbc2VhcmNoVGVybSwgc2V0U2VhcmNoVGVybV0gPSB1c2VTdGF0ZSgnJyk7XHJcbiAgY29uc3QgW2NhcnRDb3VudCwgc2V0Q2FydENvdW50XSA9IHVzZVN0YXRlKDApO1xyXG4gIGNvbnN0IFt3aXNobGlzdENvdW50LCBzZXRXaXNobGlzdENvdW50XSA9IHVzZVN0YXRlKDApO1xyXG4gIGNvbnN0IGNhcnQgPSB1c2VDYXJ0KCk7XHJcbiAgY29uc3Qgd2lzaGxpc3QgPSB1c2VXaXNobGlzdCgpO1xyXG4gIGNvbnN0IHsgdXNlciwgaXNMb2dnZWRJbiwgbG9nb3V0IH0gPSB1c2VVc2VyKCk7XHJcbiAgY29uc3QgeyB0aGVtZSwgbGFuZ3VhZ2UsIHByaW1hcnlDb2xvciwgcHJpbWFyeVRleHRDb2xvciwgdG9nZ2xlVGhlbWUsIHNldExhbmd1YWdlLCBzZXRQcmltYXJ5Q29sb3IsIHQgfSA9IHVzZVNldHRpbmdzKCk7XHJcblxyXG4gIGNvbnN0IGhhbmRsZVNlYXJjaCA9ICgpID0+IHtcclxuICAgIC8vIE5hdmlnYXRlIHRvIHByb2R1Y3RzIHBhZ2Ugd2l0aCBzZWFyY2ggcGFyYW1ldGVyc1xyXG4gICAgY29uc3QgcGFyYW1zID0gbmV3IFVSTFNlYXJjaFBhcmFtcygpO1xyXG5cclxuICAgIGlmIChzZWFyY2hUZXJtKSB7XHJcbiAgICAgIHBhcmFtcy5hcHBlbmQoJ3NlYXJjaCcsIHNlYXJjaFRlcm0pO1xyXG4gICAgfVxyXG5cclxuICAgIGlmIChzZWxlY3RlZENhdGVnb3J5SWQpIHtcclxuICAgICAgcGFyYW1zLmFwcGVuZCgnY2F0ZWdvcnknLCBzZWxlY3RlZENhdGVnb3J5SWQudG9TdHJpbmcoKSk7XHJcbiAgICB9XHJcblxyXG4gICAgcm91dGVyLnB1c2goYC9wcm9kdWN0cz8ke3BhcmFtcy50b1N0cmluZygpfWApO1xyXG4gIH07XHJcblxyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBjb25zdCBmZXRjaENhdGVnb3JpZXMgPSBhc3luYyAoKSA9PiB7XHJcbiAgICAgIHRyeSB7XHJcbiAgICAgICAgY29uc3QgcGFyYW0gPSB7XHJcbiAgICAgICAgICBcIlBhZ2VOdW1iZXJcIjogMSxcclxuICAgICAgICAgIFwiUGFnZVNpemVcIjogMTAwLFxyXG4gICAgICAgICAgXCJTb3J0Q29sdW1uXCI6IFwiTmFtZVwiLFxyXG4gICAgICAgICAgXCJTb3J0T3JkZXJcIjogXCJBU0NcIlxyXG4gICAgICAgIH07XHJcbiAgICAgICAgY29uc3QgaGVhZGVycyA9IHtcclxuICAgICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXHJcbiAgICAgICAgICAnQWNjZXB0JzogJ2FwcGxpY2F0aW9uL2pzb24nLFxyXG4gICAgICAgICAgJ0F1dGhvcml6YXRpb24nOiAnQmVhcmVyICcgKyBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgndG9rZW4nKVxyXG4gICAgICAgIH07XHJcbiAgICAgICAgY29uc3QgY2F0ZWdvcmllc1Jlc3BvbnNlID0gYXdhaXQgTWFrZUFwaUNhbGxBc3luYyhDb25maWcuRU5EX1BPSU5UX05BTUVTLkdFVF9DQVRFR09SSUVTX0xJU1QsIG51bGwsIHBhcmFtLCBoZWFkZXJzLCBcIlBPU1RcIiwgdHJ1ZSk7XHJcblxyXG4gICAgICAgIGlmIChjYXRlZ29yaWVzUmVzcG9uc2U/LmRhdGE/LmRhdGEpIHtcclxuICAgICAgICAgIHRyeSB7XHJcbiAgICAgICAgICAgIGNvbnN0IHBhcnNlZERhdGEgPSBKU09OLnBhcnNlKGNhdGVnb3JpZXNSZXNwb25zZS5kYXRhLmRhdGEpO1xyXG4gICAgICAgICAgICBpZiAoQXJyYXkuaXNBcnJheShwYXJzZWREYXRhKSkge1xyXG4gICAgICAgICAgICAgIC8vIENyZWF0ZSBhIG1hcCBvZiBwYXJlbnQgY2F0ZWdvcmllc1xyXG4gICAgICAgICAgICAgIGNvbnN0IHBhcmVudENhdGVnb3JpZXMgPSBwYXJzZWREYXRhLmZpbHRlcihjYXQgPT4gIWNhdC5QYXJlbnRDYXRlZ29yeUlEKTtcclxuICAgICAgICAgICAgICBjb25zdCBjaGlsZENhdGVnb3JpZXMgPSBwYXJzZWREYXRhLmZpbHRlcihjYXQgPT4gY2F0LlBhcmVudENhdGVnb3J5SUQpO1xyXG5cclxuICAgICAgICAgICAgICAvLyBGb3JtYXQgcGFyZW50IGNhdGVnb3JpZXMgd2l0aCB0aGVpciBjaGlsZHJlblxyXG4gICAgICAgICAgICAgIGNvbnN0IGZvcm1hdHRlZENhdGVnb3JpZXMgPSBwYXJlbnRDYXRlZ29yaWVzLm1hcChwYXJlbnQgPT4gKHtcclxuICAgICAgICAgICAgICAgIGlkOiBwYXJlbnQuQ2F0ZWdvcnlJRCxcclxuICAgICAgICAgICAgICAgIG5hbWU6IHBhcmVudC5OYW1lLFxyXG4gICAgICAgICAgICAgICAgc3ViY2F0ZWdvcmllczogY2hpbGRDYXRlZ29yaWVzXHJcbiAgICAgICAgICAgICAgICAgIC5maWx0ZXIoY2hpbGQgPT4gY2hpbGQuUGFyZW50Q2F0ZWdvcnlJRCA9PT0gcGFyZW50LkNhdGVnb3J5SUQpXHJcbiAgICAgICAgICAgICAgICAgIC5tYXAoY2hpbGQgPT4gY2hpbGQuTmFtZSlcclxuICAgICAgICAgICAgICB9KSk7XHJcblxyXG4gICAgICAgICAgICAgIHNldENhdGVnb3JpZXMoZm9ybWF0dGVkQ2F0ZWdvcmllcyk7XHJcbiAgICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgICAgY29uc29sZS5lcnJvcignQ2F0ZWdvcmllcyBkYXRhIGlzIG5vdCBhbiBhcnJheTonLCBwYXJzZWREYXRhKTtcclxuICAgICAgICAgICAgICBzZXRDYXRlZ29yaWVzKFtdKTtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgfSBjYXRjaCAocGFyc2VFcnJvcikge1xyXG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBwYXJzaW5nIGNhdGVnb3JpZXMgZGF0YTonLCBwYXJzZUVycm9yKTtcclxuICAgICAgICAgICAgc2V0Q2F0ZWdvcmllcyhbXSk7XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfSBlbHNlIGlmIChjYXRlZ29yaWVzUmVzcG9uc2U/LmRhdGE/LmVycm9yTWVzc2FnZSkge1xyXG4gICAgICAgICAgY29uc29sZS5lcnJvcignQVBJIEVycm9yOicsIGNhdGVnb3JpZXNSZXNwb25zZS5kYXRhLmVycm9yTWVzc2FnZSk7XHJcbiAgICAgICAgICBzZXRDYXRlZ29yaWVzKFtdKTtcclxuICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgY29uc29sZS5lcnJvcignSW52YWxpZCBvciBlbXB0eSByZXNwb25zZSBmcm9tIEFQSScpO1xyXG4gICAgICAgICAgc2V0Q2F0ZWdvcmllcyhbXSk7XHJcbiAgICAgICAgfVxyXG4gICAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGZldGNoaW5nIGNhdGVnb3JpZXM6JywgZXJyb3IpO1xyXG4gICAgICAgIHNldENhdGVnb3JpZXMoW10pO1xyXG4gICAgICB9IGZpbmFsbHkge1xyXG4gICAgICAgIHNldElzTG9hZGluZyhmYWxzZSk7XHJcbiAgICAgIH1cclxuICAgIH07XHJcblxyXG4gICAgZmV0Y2hDYXRlZ29yaWVzKCk7XHJcbiAgfSwgW10pO1xyXG5cclxuICAvLyBVcGRhdGUgY2FydCBjb3VudCB3aGVuIGNhcnQgaXRlbXMgY2hhbmdlXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIGlmIChjYXJ0ICYmIGNhcnQuaXRlbXMpIHtcclxuICAgICAgc2V0Q2FydENvdW50KGNhcnQudG90YWxJdGVtcyk7XHJcbiAgICB9XHJcbiAgfSwgW2NhcnQsIGNhcnQ/Lml0ZW1zXSk7XHJcblxyXG4gIC8vIFVwZGF0ZSB3aXNobGlzdCBjb3VudCB3aGVuIHdpc2hsaXN0IGl0ZW1zIGNoYW5nZVxyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBpZiAod2lzaGxpc3QpIHtcclxuICAgICAgc2V0V2lzaGxpc3RDb3VudCh3aXNobGlzdC50b3RhbEl0ZW1zKTtcclxuICAgIH1cclxuICB9LCBbd2lzaGxpc3QsIHdpc2hsaXN0Py53aXNobGlzdEl0ZW1zXSk7XHJcblxyXG4gIHJldHVybiAoXHJcbiAgICA8aGVhZGVyIGNsYXNzTmFtZT1cInctZnVsbFwiPlxyXG4gICAgICA8QnV0dG9uXHJcbiAgICAgICAgdmFyaWFudD1cImdob3N0XCJcclxuICAgICAgICBzaXplPVwic21cIlxyXG4gICAgICAgIGNsYXNzTmFtZT1cImZpeGVkIGJvdHRvbS0zNiByaWdodC00IG1kOmJvdHRvbS0yNCBtZDpyaWdodC02IG1kOmxlZnQtYXV0byB6LTUwIGJnLWdyYWRpZW50LXRvLXIgZnJvbS1wdXJwbGUtNTAwIHZpYS1waW5rLTUwMCB0by1yZWQtNTAwIHNoYWRvdy0yeGwgcm91bmRlZC0yeGwgYm9yZGVyLTIgYm9yZGVyLXdoaXRlLzUwIGZsZXggaG92ZXI6c2NhbGUtMTEwIGhvdmVyOi1yb3RhdGUtNiB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDAgaG92ZXI6c2hhZG93LXB1cnBsZS01MDAvNTAgZ3JvdXAgaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHctMTQgaC0xNCBtZDp3LTE2IG1kOmgtMTYgaG92ZXI6cm91bmRlZC1mdWxsXCJcclxuICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRTaG93Q29sb3JQaWNrZXIodHJ1ZSl9XHJcbiAgICAgID5cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlXCI+XHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImgtOCB3LTggbWQ6aC0xMCBtZDp3LTEwIHJvdW5kZWQteGwgZ3JvdXAtaG92ZXI6cm91bmRlZC1mdWxsIHJpbmctMiByaW5nLXdoaXRlLzgwIGdyb3VwLWhvdmVyOnJpbmctNCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDAgc2hhZG93LWlubmVyXCIgc3R5bGU9e3sgYmFja2dyb3VuZENvbG9yOiBwcmltYXJ5Q29sb3IgfX0gLz5cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgLXRvcC0yIC1yaWdodC0yIHctNSBoLTUgYmctZ3JhZGllbnQtdG8tYnIgZnJvbS1jeWFuLTQwMCB0by1ibHVlLTUwMCByb3VuZGVkLWZ1bGwgc2hhZG93LWxnIGFuaW1hdGUtc3BpbiBncm91cC1ob3ZlcjphbmltYXRlLXB1bHNlXCIgLz5cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgLWJvdHRvbS0xIC1sZWZ0LTEgdy0zIGgtMyBiZy1ncmFkaWVudC10by1iciBmcm9tLWdyZWVuLTQwMCB0by1lbWVyYWxkLTUwMCByb3VuZGVkLWZ1bGwgYW5pbWF0ZS1ib3VuY2VcIiAvPlxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICA8L0J1dHRvbj5cclxuXHJcbiAgICAgIHsvKiBUb3AgQmFyIC0gRGVza3RvcCBPbmx5ICovfVxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImhpZGRlbiBtZDpibG9jayBweS0yLjVcIiBzdHlsZT17eyBiYWNrZ3JvdW5kQ29sb3I6IHByaW1hcnlDb2xvciwgY29sb3I6IHByaW1hcnlUZXh0Q29sb3IgfX0+XHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb250YWluZXIgbXgtYXV0byBmbGV4IGZsZXgtY29sIG1kOmZsZXgtcm93IGp1c3RpZnktYmV0d2VlbiBpdGVtcy1jZW50ZXIgdGV4dC1zbSBweC00XCI+XHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggbWQ6ZmxleC1yb3cgaXRlbXMtc3RhcnQganVzdGlmeS1zdGFydCBnYXAtNCBtZDpnYXAtOFwiPlxyXG4gICAgICAgICAgICA8TGluayBocmVmPVwidGVsOjAwOTY0NzgzNjA3MTY4NlwiIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yIGhvdmVyOnRleHQtd2hpdGUvODBcIj5cclxuICAgICAgICAgICAgICA8UGhvbmUgY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+XHJcbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC14cyBtZDp0ZXh0LXNtXCI+e3QoJ3Bob25lJyl9PC9zcGFuPlxyXG4gICAgICAgICAgICA8L0xpbms+XHJcbiAgICAgICAgICAgIDxMaW5rIGhyZWY9XCJtYWlsdG86aW5mb0Bjb2RlbWVkaWNhbGFwcHMuY29tXCIgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTIgaG92ZXI6dGV4dC13aGl0ZS84MFwiPlxyXG4gICAgICAgICAgICAgIDxNYWlsIGNsYXNzTmFtZT1cImgtNCB3LTRcIiAvPlxyXG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQteHMgbWQ6dGV4dC1zbVwiPnt0KCdlbWFpbCcpfTwvc3Bhbj5cclxuICAgICAgICAgICAgPC9MaW5rPlxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yIG1kOmdhcC00XCI+XHJcbiAgICAgICAgICAgIDxCdXR0b24gdmFyaWFudD1cImdob3N0XCIgc2l6ZT1cInNtXCIgY2xhc3NOYW1lPVwidGV4dC13aGl0ZSBob3Zlcjp0ZXh0LXdoaXRlLzgwIHAtMSBtZDpwLTIgZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIiBvbkNsaWNrPXsoKSA9PiBzZXRMYW5ndWFnZShsYW5ndWFnZSA9PT0gJ2VuJyA/ICdhcicgOiAnZW4nKX0+XHJcbiAgICAgICAgICAgICAgPEdsb2JlIGNsYXNzTmFtZT1cImgtNCB3LTRcIiAvPlxyXG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc21cIj57bGFuZ3VhZ2UgPT09ICdlbicgPyAn2KfZhNi52LHYqNmK2KknIDogJ0VuZ2xpc2gnfTwvc3Bhbj5cclxuICAgICAgICAgICAgPC9CdXR0b24+XHJcbiAgICAgICAgICAgIDxCdXR0b25cclxuICAgICAgICAgICAgICB2YXJpYW50PVwiZ2hvc3RcIlxyXG4gICAgICAgICAgICAgIHNpemU9XCJzbVwiXHJcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC13aGl0ZSBob3Zlcjp0ZXh0LXdoaXRlLzgwIGZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCJcclxuICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB3aW5kb3cub3BlbihgaHR0cHM6Ly93YS5tZS85NjQ3ODM2MDcxNjg2P3RleHQ9JHtlbmNvZGVVUklDb21wb25lbnQoJ0hlbGxvISBJIHdvdWxkIGxpa2UgdG8gY2hhdCB3aXRoIHlvdSByZWdhcmRpbmcgeW91ciBzZXJ2aWNlcy4nKX1gLCAnX2JsYW5rJyl9XHJcbiAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICA8TWVzc2FnZUNpcmNsZSBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cclxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtXCI+e3QoJ2xpdmVDaGF0Jyl9PC9zcGFuPlxyXG4gICAgICAgICAgICA8L0J1dHRvbj5cclxuICAgICAgICAgICAge2lzTG9nZ2VkSW4gPyAoXHJcbiAgICAgICAgICAgICAgPFBvcG92ZXI+XHJcbiAgICAgICAgICAgICAgICA8UG9wb3ZlclRyaWdnZXIgYXNDaGlsZD5cclxuICAgICAgICAgICAgICAgICAgPEJ1dHRvbiB2YXJpYW50PVwiZ2hvc3RcIiBzaXplPVwic21cIiBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlIGhvdmVyOnRleHQtd2hpdGUvODAgcC0xIG1kOnAtMiBmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxyXG4gICAgICAgICAgICAgICAgICAgIDxVc2VyIGNsYXNzTmFtZT1cImgtNCB3LTRcIiAvPlxyXG4gICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc21cIj5XZWxjb21lLCB7dXNlcj8uRmlyc3ROYW1lIHx8IHVzZXI/LlVzZXJOYW1lfTwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgICA8Q2hldnJvbkRvd24gY2xhc3NOYW1lPVwiaC0zIHctM1wiIC8+XHJcbiAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgPC9Qb3BvdmVyVHJpZ2dlcj5cclxuICAgICAgICAgICAgICAgIDxQb3BvdmVyQ29udGVudCBjbGFzc05hbWU9XCJ3LTQ4IHAtMiBiZy13aGl0ZSBib3JkZXIgYm9yZGVyLWdyYXktMjAwIHNoYWRvdy1sZ1wiIGFsaWduPVwiZW5kXCI+XHJcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0xXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgPExpbmsgaHJlZj1cIi9hY2NvdW50XCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8QnV0dG9uIHZhcmlhbnQ9XCJnaG9zdFwiIHNpemU9XCJzbVwiIGNsYXNzTmFtZT1cInctZnVsbCBqdXN0aWZ5LXN0YXJ0IGhvdmVyOmJnLWdyYXktNTBcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPFVzZXIgY2xhc3NOYW1lPVwiaC00IHctNCBtci0yXCIgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgTXkgQWNjb3VudFxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XHJcbiAgICAgICAgICAgICAgICAgICAgPC9MaW5rPlxyXG4gICAgICAgICAgICAgICAgICAgIDxMaW5rIGhyZWY9XCIvb3JkZXJzXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8QnV0dG9uIHZhcmlhbnQ9XCJnaG9zdFwiIHNpemU9XCJzbVwiIGNsYXNzTmFtZT1cInctZnVsbCBqdXN0aWZ5LXN0YXJ0IGhvdmVyOmJnLWdyYXktNTBcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPFBhY2thZ2UgY2xhc3NOYW1lPVwiaC00IHctNCBtci0yXCIgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgTXkgT3JkZXJzXHJcbiAgICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cclxuICAgICAgICAgICAgICAgICAgICA8L0xpbms+XHJcbiAgICAgICAgICAgICAgICAgICAgPExpbmsgaHJlZj1cIi9hZGRyZXNzZXNcIj5cclxuICAgICAgICAgICAgICAgICAgICAgIDxCdXR0b24gdmFyaWFudD1cImdob3N0XCIgc2l6ZT1cInNtXCIgY2xhc3NOYW1lPVwidy1mdWxsIGp1c3RpZnktc3RhcnQgaG92ZXI6YmctZ3JheS01MFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8TWFwUGluIGNsYXNzTmFtZT1cImgtNCB3LTQgbXItMlwiIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIE15IEFkZHJlc3Nlc1xyXG4gICAgICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XHJcbiAgICAgICAgICAgICAgICAgICAgPC9MaW5rPlxyXG4gICAgICAgICAgICAgICAgICAgIDxMaW5rIGhyZWY9XCIvd2lzaGxpc3RcIj5cclxuICAgICAgICAgICAgICAgICAgICAgIDxCdXR0b24gdmFyaWFudD1cImdob3N0XCIgc2l6ZT1cInNtXCIgY2xhc3NOYW1lPVwidy1mdWxsIGp1c3RpZnktc3RhcnQgaG92ZXI6YmctZ3JheS01MFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8SGVhcnQgY2xhc3NOYW1lPVwiaC00IHctNCBtci0yXCIgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgV2lzaGxpc3RcclxuICAgICAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgICAgIDwvTGluaz5cclxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJvcmRlci10IGJvcmRlci1ncmF5LTEwMCBteS0xXCI+PC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgPEJ1dHRvbiBcclxuICAgICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJnaG9zdFwiIFxyXG4gICAgICAgICAgICAgICAgICAgICAgc2l6ZT1cInNtXCIgXHJcbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwganVzdGlmeS1zdGFydCB0ZXh0LXJlZC02MDAgaG92ZXI6dGV4dC1yZWQtNzAwIGhvdmVyOmJnLXJlZC01MFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGxvZ291dCgpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICByb3V0ZXIucHVzaCgnLycpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICB0b2FzdC5zdWNjZXNzKCdMb2dnZWQgb3V0IHN1Y2Nlc3NmdWxseScpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8TG9nT3V0IGNsYXNzTmFtZT1cImgtNCB3LTQgbXItMlwiIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICBMb2dvdXRcclxuICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cclxuICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICA8L1BvcG92ZXJDb250ZW50PlxyXG4gICAgICAgICAgICAgIDwvUG9wb3Zlcj5cclxuICAgICAgICAgICAgKSA6IChcclxuICAgICAgICAgICAgICA8PlxyXG4gICAgICAgICAgICAgICAgPExpbmsgaHJlZj1cIi9sb2dpblwiPlxyXG4gICAgICAgICAgICAgICAgICA8QnV0dG9uIHZhcmlhbnQ9XCJnaG9zdFwiIHNpemU9XCJzbVwiIGNsYXNzTmFtZT1cInRleHQtd2hpdGUgaG92ZXI6dGV4dC13aGl0ZS84MCBwLTEgbWQ6cC0yIGZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgPFVzZXIgY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbVwiPnt0KCdsb2dpbicpfTwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XHJcbiAgICAgICAgICAgICAgICA8L0xpbms+XHJcbiAgICAgICAgICAgICAgICA8TGluayBocmVmPVwiL3NpZ251cFwiPlxyXG4gICAgICAgICAgICAgICAgICA8QnV0dG9uIHZhcmlhbnQ9XCJnaG9zdFwiIHNpemU9XCJzbVwiIGNsYXNzTmFtZT1cInRleHQtd2hpdGUgaG92ZXI6dGV4dC13aGl0ZS84MCBwLTEgbWQ6cC0yIGZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgPFVzZXJQbHVzIGNsYXNzTmFtZT1cImgtNCB3LTRcIiAvPlxyXG4gICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc21cIj57dCgnc2lnblVwJyl9PC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cclxuICAgICAgICAgICAgICAgIDwvTGluaz5cclxuICAgICAgICAgICAgICA8Lz5cclxuICAgICAgICAgICAgKX1cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICA8L2Rpdj5cclxuXHJcbiAgICAgIHsvKiBNYWluIEhlYWRlciAqL31cclxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb250YWluZXIgbXgtYXV0byBweS00IHB4LTRcIj5cclxuICAgICAgICB7LyogTW9iaWxlIExheW91dCAtIExvZ28gYW5kIFNlYXJjaCBvbiBzZXBhcmF0ZSBsaW5lcyAqL31cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1kOmhpZGRlblwiPlxyXG4gICAgICAgICAgey8qIExvZ28gUm93IHdpdGggTGFuZ3VhZ2UgU2VsZWN0b3IgKi99XHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBtYi00XCI+XHJcbiAgICAgICAgICAgIHsvKiBMZWZ0IHNwYWNlciBmb3IgcGVyZmVjdCBjZW50ZXJpbmcgKi99XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0yMFwiPjwvZGl2PlxyXG5cclxuICAgICAgICAgICAgey8qIENlbnRlcmVkIExvZ28gKi99XHJcbiAgICAgICAgICAgIDxMaW5rIGhyZWY9XCIvXCIgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIj5cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtWyMxQjM3NjRdIGZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XHJcbiAgICAgICAgICAgICAgICA8aW1nIHNyYz17YCR7cHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfQURNSU5fQkFTRV9VUkx9Y29udGVudC9jb21tb25JbWFnZXMvb3RoZXJJbWFnZXMvMThiX2xvZ28yeC5wbmdgfSBhbHQ9XCJMb2dvXCIgY2xhc3NOYW1lPVwiaC0xMiB3LWF1dG9cIiAvPlxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICA8L0xpbms+XHJcblxyXG4gICAgICAgICAgICB7LyogTGFuZ3VhZ2UgU2VsZWN0b3Igd2l0aCBGbGFnICovfVxyXG4gICAgICAgICAgICA8QnV0dG9uXHJcbiAgICAgICAgICAgICAgdmFyaWFudD1cImdob3N0XCJcclxuICAgICAgICAgICAgICBzaXplPVwic21cIlxyXG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yIHB4LTMgcHktMiByb3VuZGVkLWZ1bGwgYm9yZGVyIGJvcmRlci1ncmF5LTIwMCBob3ZlcjpiZy1ncmF5LTUwXCJcclxuICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRMYW5ndWFnZShsYW5ndWFnZSA9PT0gJ2VuJyA/ICdhcicgOiAnZW4nKX1cclxuICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtbGdcIj5cclxuICAgICAgICAgICAgICAgIHtsYW5ndWFnZSA9PT0gJ2VuJyA/ICfwn4e68J+HuCcgOiAn8J+HrvCfh7YnfVxyXG4gICAgICAgICAgICAgIDwvc3Bhbj5cclxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtXCI+XHJcbiAgICAgICAgICAgICAgICB7bGFuZ3VhZ2UgPT09ICdlbicgPyAnRU4nIDogJ0FSJ31cclxuICAgICAgICAgICAgICA8L3NwYW4+XHJcbiAgICAgICAgICAgIDwvQnV0dG9uPlxyXG4gICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgey8qIFNlYXJjaCBSb3cgLSBObyBjYXRlZ29yeSBkcm9wZG93biAqL31cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy1mdWxsXCI+XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTIgYm9yZGVyIHJvdW5kZWQtZnVsbCBweC00IHB5LTMgYmctYmFja2dyb3VuZCBzaGFkb3ctc21cIj5cclxuICAgICAgICAgICAgICA8aW5wdXRcclxuICAgICAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcclxuICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPXt0KCdwcm9kdWN0cycpIHx8ICfYp9mE2KjYrdirINi52YYg2KfZhNmF2YbYqtis2KfYqi4uLid9XHJcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJiZy10cmFuc3BhcmVudCBib3JkZXItbm9uZSBmb2N1czpvdXRsaW5lLW5vbmUgdGV4dC1zbSBmbGV4LTEgcGxhY2Vob2xkZXI6dGV4dC1ncmF5LTQwMFwiXHJcbiAgICAgICAgICAgICAgICB2YWx1ZT17c2VhcmNoVGVybX1cclxuICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0U2VhcmNoVGVybShlLnRhcmdldC52YWx1ZSl9XHJcbiAgICAgICAgICAgICAgICBvbktleURvd249eyhlKSA9PiBlLmtleSA9PT0gJ0VudGVyJyAmJiBoYW5kbGVTZWFyY2goKX1cclxuICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgIDxCdXR0b25cclxuICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJnaG9zdFwiXHJcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJoLTggdy04IHAtMCBob3ZlcjpiZy1hY2NlbnQvODAgdHJhbnNpdGlvbi1jb2xvcnNcIlxyXG4gICAgICAgICAgICAgICAgc3R5bGU9e3sgY29sb3I6IHByaW1hcnlDb2xvciB9fVxyXG4gICAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlU2VhcmNofVxyXG4gICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgIDxTZWFyY2ggY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+XHJcbiAgICAgICAgICAgICAgPC9CdXR0b24+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgey8qIERlc2t0b3AgTGF5b3V0ICovfVxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaGlkZGVuIG1kOmZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxyXG4gICAgICAgICAgey8qIExvZ28gYW5kIFNlYXJjaCAqL31cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTQgZmxleC0xXCI+XHJcbiAgICAgICAgICAgIHsvKiBMb2dvICovfVxyXG4gICAgICAgICAgICA8TGluayBocmVmPVwiL1wiIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LVsjMUIzNzY0XSBmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxyXG4gICAgICAgICAgICAgICAgPGltZyBzcmM9e2Ake3Byb2Nlc3MuZW52Lk5FWFRfUFVCTElDX0FETUlOX0JBU0VfVVJMfWNvbnRlbnQvY29tbW9uSW1hZ2VzL290aGVySW1hZ2VzLzE4Yl9sb2dvMngucG5nYH0gYWx0PVwiTG9nb1wiIGNsYXNzTmFtZT1cImgtMTYgdy1hdXRvXCIgLz5cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgPC9MaW5rPlxyXG5cclxuICAgICAgICAgICAgey8qIFNlYXJjaCBhbmQgQ2F0ZWdvcnkgLSBNb3ZlZCBuZXh0IHRvIGxvZ28gKi99XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTIgYm9yZGVyIHJvdW5kZWQtZnVsbCBweC0zIHB5LTEuNSBmbGV4LTEgbWF4LXctbWQgbWwtNFwiPlxyXG4gICAgICAgICAgICAgIDxQb3BvdmVyPlxyXG4gICAgICAgICAgICAgICAgPFBvcG92ZXJUcmlnZ2VyIGFzQ2hpbGQ+XHJcbiAgICAgICAgICAgICAgICAgIDxCdXR0b24gdmFyaWFudD1cImdob3N0XCIgY2xhc3NOYW1lPVwiaC04IGZsZXggaXRlbXMtY2VudGVyIGdhcC0xIHB4LTJcIj5cclxuICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LW11dGVkLWZvcmVncm91bmQgdGV4dC1zbVwiPntzZWxlY3RlZENhdGVnb3J5IHx8IHNlbGVjdGVkU3ViY2F0ZWdvcnkgfHwgdCgnY2F0ZWdvcnknKX08L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgICAgPENoZXZyb25Eb3duIGNsYXNzTmFtZT1cImgtNCB3LTQgdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCIgLz5cclxuICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XHJcbiAgICAgICAgICAgICAgICA8L1BvcG92ZXJUcmlnZ2VyPlxyXG4gICAgICAgICAgICAgICAgPFBvcG92ZXJDb250ZW50IGNsYXNzTmFtZT1cInctNjQgcC0wIGJnLXdoaXRlIGJvcmRlciBib3JkZXItZ3JheS0yMDAgc2hhZG93LWxnXCIgYWxpZ249XCJzdGFydFwiPlxyXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1heC1oLVszMDBweF0gb3ZlcmZsb3ctYXV0b1wiPlxyXG4gICAgICAgICAgICAgICAgICAgIHtpc0xvYWRpbmcgPyAoXHJcbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtNCB0ZXh0LWNlbnRlciB0ZXh0LW11dGVkLWZvcmVncm91bmRcIj57dCgnbG9hZGluZ0NhdGVnb3JpZXMnKX08L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICApIDogKFxyXG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHtjYXRlZ29yaWVzLm1hcCgoY2F0ZWdvcnkpID0+IChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGtleT17Y2F0ZWdvcnkuaWR9IGNsYXNzTmFtZT1cImdyb3VwXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uIGNsYXNzTmFtZT1cInctZnVsbCBweC00IHB5LTIgdGV4dC1sZWZ0IGhvdmVyOmJnLWdyYXktNTBcIiBvbkNsaWNrPXsoKSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNldFNlbGVjdGVkQ2F0ZWdvcnkoY2F0ZWdvcnkubmFtZSk7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNldFNlbGVjdGVkQ2F0ZWdvcnlJZChjYXRlZ29yeS5pZCk7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNldFNlbGVjdGVkU3ViY2F0ZWdvcnkobnVsbCk7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9fT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2NhdGVnb3J5Lm5hbWV9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaGlkZGVuIGdyb3VwLWhvdmVyOmJsb2NrIGFic29sdXRlIGxlZnQtZnVsbCB0b3AtMCB3LTQ4IGJnLXdoaXRlIHNoYWRvdy1sZyByb3VuZGVkLW1kIGJvcmRlciBib3JkZXItZ3JheS0yMDBcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2NhdGVnb3J5LnN1YmNhdGVnb3JpZXMubWFwKChzdWIsIGluZGV4KSA9PiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAga2V5PXtpbmRleH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBweC00IHB5LTIgdGV4dC1sZWZ0IGhvdmVyOmJnLWdyYXktNTBcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzZXRTZWxlY3RlZFN1YmNhdGVnb3J5KHN1Yik7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNldFNlbGVjdGVkQ2F0ZWdvcnkobnVsbCk7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8vIEtlZXAgdGhlIHBhcmVudCBjYXRlZ29yeSBJRCBmb3Igc2VhcmNoIHB1cnBvc2VzXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNldFNlbGVjdGVkQ2F0ZWdvcnlJZChjYXRlZ29yeS5pZCk7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtzdWJ9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICkpfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICkpfVxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICA8L1BvcG92ZXJDb250ZW50PlxyXG4gICAgICAgICAgICAgIDwvUG9wb3Zlcj5cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImgtNSB3LXB4IGJnLWJvcmRlciBteC0yXCIgLz5cclxuICAgICAgICAgICAgICA8aW5wdXRcclxuICAgICAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcclxuICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPXt0KCdwcm9kdWN0cycpfVxyXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmctdHJhbnNwYXJlbnQgYm9yZGVyLW5vbmUgZm9jdXM6b3V0bGluZS1ub25lIHRleHQtc20gZmxleC0xXCJcclxuICAgICAgICAgICAgICAgIHZhbHVlPXtzZWFyY2hUZXJtfVxyXG4gICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRTZWFyY2hUZXJtKGUudGFyZ2V0LnZhbHVlKX1cclxuICAgICAgICAgICAgICAgIG9uS2V5RG93bj17KGUpID0+IGUua2V5ID09PSAnRW50ZXInICYmIGhhbmRsZVNlYXJjaCgpfVxyXG4gICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgPEJ1dHRvbiB2YXJpYW50PVwiZ2hvc3RcIiBjbGFzc05hbWU9XCJoLTggdy04IHAtMFwiIG9uQ2xpY2s9e2hhbmRsZVNlYXJjaH0+XHJcbiAgICAgICAgICAgICAgICA8U2VhcmNoIGNsYXNzTmFtZT1cImgtNCB3LTRcIiAvPlxyXG4gICAgICAgICAgICAgIDwvQnV0dG9uPlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgIHsvKiBOYXZpZ2F0aW9uIC0gRGVza3RvcCAqL31cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaGlkZGVuIG1kOmJsb2NrXCI+XHJcbiAgICAgICAgICAgIDxOYXZpZ2F0aW9uTWVudT5cclxuICAgICAgICAgICAgICA8TmF2aWdhdGlvbk1lbnVMaXN0PlxyXG4gICAgICAgICAgICAgICAgPE5hdmlnYXRpb25NZW51SXRlbT5cclxuICAgICAgICAgICAgICAgICAgPE5hdmlnYXRpb25NZW51TGluayBhc0NoaWxkPlxyXG4gICAgICAgICAgICAgICAgICAgIDxMaW5rIGhyZWY9XCIvXCIgY2xhc3NOYW1lPXtjbihcclxuICAgICAgICAgICAgICAgICAgICAgIFwiZ3JvdXAgaW5saW5lLWZsZXggaC0xMCB3LW1heCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgcm91bmRlZC1tZCBiZy1iYWNrZ3JvdW5kIHB4LTQgcHktMiB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRyYW5zaXRpb24tY29sb3JzIGhvdmVyOmJnLWFjY2VudCBob3Zlcjp0ZXh0LWFjY2VudC1mb3JlZ3JvdW5kIGZvY3VzOmJnLWFjY2VudCBmb2N1czp0ZXh0LWFjY2VudC1mb3JlZ3JvdW5kIGZvY3VzOm91dGxpbmUtbm9uZSBkaXNhYmxlZDpwb2ludGVyLWV2ZW50cy1ub25lIGRpc2FibGVkOm9wYWNpdHktNTAgZGF0YS1bYWN0aXZlXTpiZy1hY2NlbnQvNTAgZGF0YS1bc3RhdGU9b3Blbl06YmctYWNjZW50LzUwXCJcclxuICAgICAgICAgICAgICAgICAgICApfT5cclxuICAgICAgICAgICAgICAgICAgICAgIHt0KCdob21lJyl9XHJcbiAgICAgICAgICAgICAgICAgICAgPC9MaW5rPlxyXG4gICAgICAgICAgICAgICAgICA8L05hdmlnYXRpb25NZW51TGluaz5cclxuICAgICAgICAgICAgICAgIDwvTmF2aWdhdGlvbk1lbnVJdGVtPlxyXG4gICAgICAgICAgICAgICAgPE5hdmlnYXRpb25NZW51SXRlbT5cclxuICAgICAgICAgICAgICAgICAgPE5hdmlnYXRpb25NZW51TGluayBhc0NoaWxkPlxyXG4gICAgICAgICAgICAgICAgICAgIDxMaW5rIGhyZWY9XCIvaG90LWRlYWxzXCIgY2xhc3NOYW1lPXtjbihcclxuICAgICAgICAgICAgICAgICAgICAgIFwiZ3JvdXAgaW5saW5lLWZsZXggaC0xMCB3LW1heCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgcm91bmRlZC1tZCBiZy1iYWNrZ3JvdW5kIHB4LTQgcHktMiB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRyYW5zaXRpb24tY29sb3JzIGhvdmVyOmJnLWFjY2VudCBob3Zlcjp0ZXh0LWFjY2VudC1mb3JlZ3JvdW5kIGZvY3VzOmJnLWFjY2VudCBmb2N1czp0ZXh0LWFjY2VudC1mb3JlZ3JvdW5kIGZvY3VzOm91dGxpbmUtbm9uZSBkaXNhYmxlZDpwb2ludGVyLWV2ZW50cy1ub25lIGRpc2FibGVkOm9wYWNpdHktNTAgZGF0YS1bYWN0aXZlXTpiZy1hY2NlbnQvNTAgZGF0YS1bc3RhdGU9b3Blbl06YmctYWNjZW50LzUwXCJcclxuICAgICAgICAgICAgICAgICAgICApfT5cclxuICAgICAgICAgICAgICAgICAgICAgIHt0KCdob3REZWFscycpfVxyXG4gICAgICAgICAgICAgICAgICAgIDwvTGluaz5cclxuICAgICAgICAgICAgICAgICAgPC9OYXZpZ2F0aW9uTWVudUxpbms+XHJcbiAgICAgICAgICAgICAgICA8L05hdmlnYXRpb25NZW51SXRlbT5cclxuICAgICAgICAgICAgICAgIDxOYXZpZ2F0aW9uTWVudUl0ZW0+XHJcbiAgICAgICAgICAgICAgICAgIDxOYXZpZ2F0aW9uTWVudUxpbmsgYXNDaGlsZD5cclxuICAgICAgICAgICAgICAgICAgICA8TGluayBocmVmPVwiL3Byb2R1Y3RzXCIgY2xhc3NOYW1lPXtjbihcclxuICAgICAgICAgICAgICAgICAgICAgIFwiZ3JvdXAgaW5saW5lLWZsZXggaC0xMCB3LW1heCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgcm91bmRlZC1tZCBiZy1iYWNrZ3JvdW5kIHB4LTQgcHktMiB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRyYW5zaXRpb24tY29sb3JzIGhvdmVyOmJnLWFjY2VudCBob3Zlcjp0ZXh0LWFjY2VudC1mb3JlZ3JvdW5kIGZvY3VzOmJnLWFjY2VudCBmb2N1czp0ZXh0LWFjY2VudC1mb3JlZ3JvdW5kIGZvY3VzOm91dGxpbmUtbm9uZSBkaXNhYmxlZDpwb2ludGVyLWV2ZW50cy1ub25lIGRpc2FibGVkOm9wYWNpdHktNTAgZGF0YS1bYWN0aXZlXTpiZy1hY2NlbnQvNTAgZGF0YS1bc3RhdGU9b3Blbl06YmctYWNjZW50LzUwXCJcclxuICAgICAgICAgICAgICAgICAgICApfT5cclxuICAgICAgICAgICAgICAgICAgICAgIHt0KCdwcm9kdWN0cycpIHx8ICdQcm9kdWN0cyd9XHJcbiAgICAgICAgICAgICAgICAgICAgPC9MaW5rPlxyXG4gICAgICAgICAgICAgICAgICA8L05hdmlnYXRpb25NZW51TGluaz5cclxuICAgICAgICAgICAgICAgIDwvTmF2aWdhdGlvbk1lbnVJdGVtPlxyXG4gICAgICAgICAgICAgICAgPE5hdmlnYXRpb25NZW51SXRlbT5cclxuICAgICAgICAgICAgICAgICAgPE5hdmlnYXRpb25NZW51TGluayBhc0NoaWxkPlxyXG4gICAgICAgICAgICAgICAgICAgIDxMaW5rIGhyZWY9XCIvcGF5bWVudC1tZXRob2RzXCIgY2xhc3NOYW1lPXtjbihcclxuICAgICAgICAgICAgICAgICAgICAgIFwiZ3JvdXAgaW5saW5lLWZsZXggaC0xMCB3LW1heCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgcm91bmRlZC1tZCBiZy1iYWNrZ3JvdW5kIHB4LTQgcHktMiB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRyYW5zaXRpb24tY29sb3JzIGhvdmVyOmJnLWFjY2VudCBob3Zlcjp0ZXh0LWFjY2VudC1mb3JlZ3JvdW5kIGZvY3VzOmJnLWFjY2VudCBmb2N1czp0ZXh0LWFjY2VudC1mb3JlZ3JvdW5kIGZvY3VzOm91dGxpbmUtbm9uZSBkaXNhYmxlZDpwb2ludGVyLWV2ZW50cy1ub25lIGRpc2FibGVkOm9wYWNpdHktNTAgZGF0YS1bYWN0aXZlXTpiZy1hY2NlbnQvNTAgZGF0YS1bc3RhdGU9b3Blbl06YmctYWNjZW50LzUwXCJcclxuICAgICAgICAgICAgICAgICAgICApfT5cclxuICAgICAgICAgICAgICAgICAgICAgIHt0KCdwYXltZW50TWV0aG9kcycpfVxyXG4gICAgICAgICAgICAgICAgICAgIDwvTGluaz5cclxuICAgICAgICAgICAgICAgICAgPC9OYXZpZ2F0aW9uTWVudUxpbms+XHJcbiAgICAgICAgICAgICAgICA8L05hdmlnYXRpb25NZW51SXRlbT5cclxuICAgICAgICAgICAgICAgIDxOYXZpZ2F0aW9uTWVudUl0ZW0+XHJcbiAgICAgICAgICAgICAgICAgIDxOYXZpZ2F0aW9uTWVudUxpbmsgYXNDaGlsZD5cclxuICAgICAgICAgICAgICAgICAgICA8TGluayBocmVmPVwiL2ZvbGxvdy11c1wiIGNsYXNzTmFtZT17Y24oXHJcbiAgICAgICAgICAgICAgICAgICAgICBcImdyb3VwIGlubGluZS1mbGV4IGgtMTAgdy1tYXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHJvdW5kZWQtbWQgYmctYmFja2dyb3VuZCBweC00IHB5LTIgdGV4dC1zbSBmb250LW1lZGl1bSB0cmFuc2l0aW9uLWNvbG9ycyBob3ZlcjpiZy1hY2NlbnQgaG92ZXI6dGV4dC1hY2NlbnQtZm9yZWdyb3VuZCBmb2N1czpiZy1hY2NlbnQgZm9jdXM6dGV4dC1hY2NlbnQtZm9yZWdyb3VuZCBmb2N1czpvdXRsaW5lLW5vbmUgZGlzYWJsZWQ6cG9pbnRlci1ldmVudHMtbm9uZSBkaXNhYmxlZDpvcGFjaXR5LTUwIGRhdGEtW2FjdGl2ZV06YmctYWNjZW50LzUwIGRhdGEtW3N0YXRlPW9wZW5dOmJnLWFjY2VudC81MFwiXHJcbiAgICAgICAgICAgICAgICAgICAgKX0+XHJcbiAgICAgICAgICAgICAgICAgICAgICB7dCgnZm9sbG93VXMnKX1cclxuICAgICAgICAgICAgICAgICAgICA8L0xpbms+XHJcbiAgICAgICAgICAgICAgICAgIDwvTmF2aWdhdGlvbk1lbnVMaW5rPlxyXG4gICAgICAgICAgICAgICAgPC9OYXZpZ2F0aW9uTWVudUl0ZW0+XHJcbiAgICAgICAgICAgICAgICA8TmF2aWdhdGlvbk1lbnVJdGVtPlxyXG4gICAgICAgICAgICAgICAgICA8TmF2aWdhdGlvbk1lbnVMaW5rIGFzQ2hpbGQ+XHJcbiAgICAgICAgICAgICAgICAgICAgPExpbmsgaHJlZj1cIi9hYm91dFwiIGNsYXNzTmFtZT17Y24oXHJcbiAgICAgICAgICAgICAgICAgICAgICBcImdyb3VwIGlubGluZS1mbGV4IGgtMTAgdy1tYXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHJvdW5kZWQtbWQgYmctYmFja2dyb3VuZCBweC00IHB5LTIgdGV4dC1zbSBmb250LW1lZGl1bSB0cmFuc2l0aW9uLWNvbG9ycyBob3ZlcjpiZy1hY2NlbnQgaG92ZXI6dGV4dC1hY2NlbnQtZm9yZWdyb3VuZCBmb2N1czpiZy1hY2NlbnQgZm9jdXM6dGV4dC1hY2NlbnQtZm9yZWdyb3VuZCBmb2N1czpvdXRsaW5lLW5vbmUgZGlzYWJsZWQ6cG9pbnRlci1ldmVudHMtbm9uZSBkaXNhYmxlZDpvcGFjaXR5LTUwIGRhdGEtW2FjdGl2ZV06YmctYWNjZW50LzUwIGRhdGEtW3N0YXRlPW9wZW5dOmJnLWFjY2VudC81MFwiXHJcbiAgICAgICAgICAgICAgICAgICAgKX0+XHJcbiAgICAgICAgICAgICAgICAgICAgICB7dCgnYWJvdXRVcycpfVxyXG4gICAgICAgICAgICAgICAgICAgIDwvTGluaz5cclxuICAgICAgICAgICAgICAgICAgPC9OYXZpZ2F0aW9uTWVudUxpbms+XHJcbiAgICAgICAgICAgICAgICA8L05hdmlnYXRpb25NZW51SXRlbT5cclxuICAgICAgICAgICAgICAgIDxOYXZpZ2F0aW9uTWVudUl0ZW0+XHJcbiAgICAgICAgICAgICAgICAgIDxOYXZpZ2F0aW9uTWVudUxpbmsgYXNDaGlsZD5cclxuICAgICAgICAgICAgICAgICAgICA8TGluayBocmVmPVwiL2NvbnRhY3RcIiBjbGFzc05hbWU9e2NuKFxyXG4gICAgICAgICAgICAgICAgICAgICAgXCJncm91cCBpbmxpbmUtZmxleCBoLTEwIHctbWF4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciByb3VuZGVkLW1kIGJnLWJhY2tncm91bmQgcHgtNCBweS0yIHRleHQtc20gZm9udC1tZWRpdW0gdHJhbnNpdGlvbi1jb2xvcnMgaG92ZXI6YmctYWNjZW50IGhvdmVyOnRleHQtYWNjZW50LWZvcmVncm91bmQgZm9jdXM6YmctYWNjZW50IGZvY3VzOnRleHQtYWNjZW50LWZvcmVncm91bmQgZm9jdXM6b3V0bGluZS1ub25lIGRpc2FibGVkOnBvaW50ZXItZXZlbnRzLW5vbmUgZGlzYWJsZWQ6b3BhY2l0eS01MCBkYXRhLVthY3RpdmVdOmJnLWFjY2VudC81MCBkYXRhLVtzdGF0ZT1vcGVuXTpiZy1hY2NlbnQvNTBcIlxyXG4gICAgICAgICAgICAgICAgICAgICl9PlxyXG4gICAgICAgICAgICAgICAgICAgICAge3QoJ2NvbnRhY3RVcycpfVxyXG4gICAgICAgICAgICAgICAgICAgIDwvTGluaz5cclxuICAgICAgICAgICAgICAgICAgPC9OYXZpZ2F0aW9uTWVudUxpbms+XHJcbiAgICAgICAgICAgICAgICA8L05hdmlnYXRpb25NZW51SXRlbT5cclxuICAgICAgICAgICAgICA8L05hdmlnYXRpb25NZW51TGlzdD5cclxuICAgICAgICAgICAgPC9OYXZpZ2F0aW9uTWVudT5cclxuICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgIHsvKiBDYXJ0LCBXaXNobGlzdCBJY29ucyBhbmQgTW9iaWxlIE1lbnUgQnV0dG9uICovfVxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtNFwiPlxyXG4gICAgICAgICAgICB7LyogQ2FydCBhbmQgV2lzaGxpc3QgLSBEZXNrdG9wIE9ubHkgKi99XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaGlkZGVuIG1kOmZsZXggaXRlbXMtY2VudGVyIGdhcC00XCI+XHJcbiAgICAgICAgICAgICAgPExpbmsgaHJlZj1cIi93aXNobGlzdFwiPlxyXG4gICAgICAgICAgICAgICAgPEJ1dHRvbiB2YXJpYW50PVwiZ2hvc3RcIiBzaXplPVwiaWNvblwiIGNsYXNzTmFtZT1cInJlbGF0aXZlXCI+XHJcbiAgICAgICAgICAgICAgICAgIDxIZWFydCBjbGFzc05hbWU9XCJoLTUgdy01XCIgc3R5bGU9e3sgY29sb3I6IHByaW1hcnlDb2xvciB9fSAvPlxyXG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJhYnNvbHV0ZSAtdG9wLTEgLXJpZ2h0LTEgYmctcHJpbWFyeSB0ZXh0LXdoaXRlIHRleHQteHMgcm91bmRlZC1mdWxsIGgtNCB3LTQgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cclxuICAgICAgICAgICAgICAgICAgICB7d2lzaGxpc3RDb3VudH1cclxuICAgICAgICAgICAgICAgICAgPC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgPC9CdXR0b24+XHJcbiAgICAgICAgICAgICAgPC9MaW5rPlxyXG4gICAgICAgICAgICAgIDxMaW5rIGhyZWY9XCIvY2FydFwiPlxyXG4gICAgICAgICAgICAgICAgPEJ1dHRvbiB2YXJpYW50PVwiZ2hvc3RcIiBzaXplPVwiaWNvblwiIGNsYXNzTmFtZT1cInJlbGF0aXZlXCI+XHJcbiAgICAgICAgICAgICAgICAgIDxTaG9wcGluZ0NhcnQgY2xhc3NOYW1lPVwiaC01IHctNVwiIHN0eWxlPXt7IGNvbG9yOiBwcmltYXJ5Q29sb3IgfX0gLz5cclxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiYWJzb2x1dGUgLXRvcC0xIC1yaWdodC0xIGJnLXByaW1hcnkgdGV4dC13aGl0ZSB0ZXh0LXhzIHJvdW5kZWQtZnVsbCBoLTQgdy00IGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XHJcbiAgICAgICAgICAgICAgICAgICAge2NhcnRDb3VudH1cclxuICAgICAgICAgICAgICAgICAgPC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgPC9CdXR0b24+XHJcbiAgICAgICAgICAgICAgPC9MaW5rPlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgXHJcbiAgICAgICAgICAgIHsvKiBNb2JpbGUgTWVudSBCdXR0b24gKi99XHJcbiAgICAgICAgICAgIDxCdXR0b25cclxuICAgICAgICAgICAgICB2YXJpYW50PVwiZ2hvc3RcIlxyXG4gICAgICAgICAgICAgIHNpemU9XCJpY29uXCJcclxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJtZDpoaWRkZW5cIlxyXG4gICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFNob3dNb2JpbGVNZW51KCFzaG93TW9iaWxlTWVudSl9XHJcbiAgICAgICAgICAgICAgYXJpYS1sYWJlbD1cIlRvZ2dsZSBtZW51XCJcclxuICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgIHtzaG93TW9iaWxlTWVudSA/IDxYIGNsYXNzTmFtZT1cImgtNiB3LTZcIiAvPiA6IDxNZW51IGNsYXNzTmFtZT1cImgtNiB3LTZcIiAvPn1cclxuICAgICAgICAgICAgPC9CdXR0b24+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICA8L2Rpdj5cclxuICAgICAgPC9kaXY+XHJcbiAgICAgIHsvKiBNb2JpbGUgTmF2aWdhdGlvbiBNZW51ICovfVxyXG4gICAgICB7c2hvd01vYmlsZU1lbnUgJiYgKFxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWQ6aGlkZGVuIGJnLXdoaXRlIGJvcmRlci10IGJvcmRlci1ncmF5LTIwMCBzaGFkb3ctbGdcIj5cclxuICAgICAgICAgIDxuYXYgY2xhc3NOYW1lPVwicHgtNCBweS00IHNwYWNlLXktMlwiPlxyXG4gICAgICAgICAgICA8TGlua1xyXG4gICAgICAgICAgICAgIGhyZWY9XCIvXCJcclxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJibG9jayBweC00IHB5LTMgdGV4dC1ncmF5LTcwMCBob3ZlcjpiZy1ncmF5LTUwIGhvdmVyOnRleHQtcHJpbWFyeSByb3VuZGVkLWxnIHRyYW5zaXRpb24tY29sb3JzXCJcclxuICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRTaG93TW9iaWxlTWVudShmYWxzZSl9XHJcbiAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICB7dCgnaG9tZScpfVxyXG4gICAgICAgICAgICA8L0xpbms+XHJcbiAgICAgICAgICAgIDxMaW5rXHJcbiAgICAgICAgICAgICAgaHJlZj1cIi9ob3QtZGVhbHNcIlxyXG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJsb2NrIHB4LTQgcHktMyB0ZXh0LWdyYXktNzAwIGhvdmVyOmJnLWdyYXktNTAgaG92ZXI6dGV4dC1wcmltYXJ5IHJvdW5kZWQtbGcgdHJhbnNpdGlvbi1jb2xvcnNcIlxyXG4gICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFNob3dNb2JpbGVNZW51KGZhbHNlKX1cclxuICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgIHt0KCdob3REZWFscycpfVxyXG4gICAgICAgICAgICA8L0xpbms+XHJcbiAgICAgICAgICAgIDxMaW5rXHJcbiAgICAgICAgICAgICAgaHJlZj1cIi9wcm9kdWN0c1wiXHJcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmxvY2sgcHgtNCBweS0zIHRleHQtZ3JheS03MDAgaG92ZXI6YmctZ3JheS01MCBob3Zlcjp0ZXh0LXByaW1hcnkgcm91bmRlZC1sZyB0cmFuc2l0aW9uLWNvbG9yc1wiXHJcbiAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0U2hvd01vYmlsZU1lbnUoZmFsc2UpfVxyXG4gICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAge3QoJ3Byb2R1Y3RzJykgfHwgJ1Byb2R1Y3RzJ31cclxuICAgICAgICAgICAgPC9MaW5rPlxyXG4gICAgICAgICAgICA8TGlua1xyXG4gICAgICAgICAgICAgIGhyZWY9XCIvcGF5bWVudC1tZXRob2RzXCJcclxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJibG9jayBweC00IHB5LTMgdGV4dC1ncmF5LTcwMCBob3ZlcjpiZy1ncmF5LTUwIGhvdmVyOnRleHQtcHJpbWFyeSByb3VuZGVkLWxnIHRyYW5zaXRpb24tY29sb3JzXCJcclxuICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRTaG93TW9iaWxlTWVudShmYWxzZSl9XHJcbiAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICB7dCgncGF5bWVudE1ldGhvZHMnKX1cclxuICAgICAgICAgICAgPC9MaW5rPlxyXG4gICAgICAgICAgICA8TGlua1xyXG4gICAgICAgICAgICAgIGhyZWY9XCIvZm9sbG93LXVzXCJcclxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJibG9jayBweC00IHB5LTMgdGV4dC1ncmF5LTcwMCBob3ZlcjpiZy1ncmF5LTUwIGhvdmVyOnRleHQtcHJpbWFyeSByb3VuZGVkLWxnIHRyYW5zaXRpb24tY29sb3JzXCJcclxuICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRTaG93TW9iaWxlTWVudShmYWxzZSl9XHJcbiAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICB7dCgnZm9sbG93VXMnKX1cclxuICAgICAgICAgICAgPC9MaW5rPlxyXG4gICAgICAgICAgICA8TGlua1xyXG4gICAgICAgICAgICAgIGhyZWY9XCIvYWJvdXRcIlxyXG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJsb2NrIHB4LTQgcHktMyB0ZXh0LWdyYXktNzAwIGhvdmVyOmJnLWdyYXktNTAgaG92ZXI6dGV4dC1wcmltYXJ5IHJvdW5kZWQtbGcgdHJhbnNpdGlvbi1jb2xvcnNcIlxyXG4gICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFNob3dNb2JpbGVNZW51KGZhbHNlKX1cclxuICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgIHt0KCdhYm91dFVzJyl9XHJcbiAgICAgICAgICAgIDwvTGluaz5cclxuICAgICAgICAgICAgPExpbmtcclxuICAgICAgICAgICAgICBocmVmPVwiL2NvbnRhY3RcIlxyXG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJsb2NrIHB4LTQgcHktMyB0ZXh0LWdyYXktNzAwIGhvdmVyOmJnLWdyYXktNTAgaG92ZXI6dGV4dC1wcmltYXJ5IHJvdW5kZWQtbGcgdHJhbnNpdGlvbi1jb2xvcnNcIlxyXG4gICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFNob3dNb2JpbGVNZW51KGZhbHNlKX1cclxuICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgIHt0KCdjb250YWN0VXMnKX1cclxuICAgICAgICAgICAgPC9MaW5rPlxyXG4gICAgICAgICAgICBcclxuICAgICAgICAgICAgey8qIE1vYmlsZSBDYXJ0IGFuZCBXaXNobGlzdCBMaW5rcyAqL31cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJib3JkZXItdCBib3JkZXItZ3JheS0yMDAgcHQtNCBtdC00XCI+XHJcbiAgICAgICAgICAgICAgPExpbmtcclxuICAgICAgICAgICAgICAgIGhyZWY9XCIvd2lzaGxpc3RcIlxyXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTMgcHgtNCBweS0zIHRleHQtZ3JheS03MDAgaG92ZXI6YmctZ3JheS01MCBob3Zlcjp0ZXh0LXByaW1hcnkgcm91bmRlZC1sZyB0cmFuc2l0aW9uLWNvbG9yc1wiXHJcbiAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRTaG93TW9iaWxlTWVudShmYWxzZSl9XHJcbiAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgPEhlYXJ0IGNsYXNzTmFtZT1cImgtNSB3LTVcIiAvPlxyXG4gICAgICAgICAgICAgICAgPHNwYW4+V2lzaGxpc3Q8L3NwYW4+XHJcbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJtbC1hdXRvIGJnLXByaW1hcnkgdGV4dC13aGl0ZSB0ZXh0LXhzIHJvdW5kZWQtZnVsbCBoLTUgdy01IGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XHJcbiAgICAgICAgICAgICAgICAgIHt3aXNobGlzdENvdW50fVxyXG4gICAgICAgICAgICAgICAgPC9zcGFuPlxyXG4gICAgICAgICAgICAgIDwvTGluaz5cclxuICAgICAgICAgICAgICA8TGlua1xyXG4gICAgICAgICAgICAgICAgaHJlZj1cIi9jYXJ0XCJcclxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0zIHB4LTQgcHktMyB0ZXh0LWdyYXktNzAwIGhvdmVyOmJnLWdyYXktNTAgaG92ZXI6dGV4dC1wcmltYXJ5IHJvdW5kZWQtbGcgdHJhbnNpdGlvbi1jb2xvcnNcIlxyXG4gICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0U2hvd01vYmlsZU1lbnUoZmFsc2UpfVxyXG4gICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgIDxTaG9wcGluZ0NhcnQgY2xhc3NOYW1lPVwiaC01IHctNVwiIC8+XHJcbiAgICAgICAgICAgICAgICA8c3Bhbj5DYXJ0PC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwibWwtYXV0byBiZy1wcmltYXJ5IHRleHQtd2hpdGUgdGV4dC14cyByb3VuZGVkLWZ1bGwgaC01IHctNSBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxyXG4gICAgICAgICAgICAgICAgICB7Y2FydENvdW50fVxyXG4gICAgICAgICAgICAgICAgPC9zcGFuPlxyXG4gICAgICAgICAgICAgIDwvTGluaz5cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgIFxyXG4gICAgICAgICAgICB7LyogTW9iaWxlIFVzZXIgTWVudSAqL31cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJib3JkZXItdCBib3JkZXItZ3JheS0yMDAgcHQtNCBtdC00XCI+XHJcbiAgICAgICAgICAgICAge2lzTG9nZ2VkSW4gPyAoXHJcbiAgICAgICAgICAgICAgICA8PlxyXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInB4LTQgcHktMiB0ZXh0LXNtIHRleHQtZ3JheS01MDBcIj5cclxuICAgICAgICAgICAgICAgICAgICBXZWxjb21lLCB7dXNlcj8uRmlyc3ROYW1lIHx8IHVzZXI/LlVzZXJOYW1lfVxyXG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgPExpbmtcclxuICAgICAgICAgICAgICAgICAgICBocmVmPVwiL2FjY291bnRcIlxyXG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0zIHB4LTQgcHktMyB0ZXh0LWdyYXktNzAwIGhvdmVyOmJnLWdyYXktNTAgaG92ZXI6dGV4dC1wcmltYXJ5IHJvdW5kZWQtbGcgdHJhbnNpdGlvbi1jb2xvcnNcIlxyXG4gICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFNob3dNb2JpbGVNZW51KGZhbHNlKX1cclxuICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgIDxVc2VyIGNsYXNzTmFtZT1cImgtNSB3LTVcIiAvPlxyXG4gICAgICAgICAgICAgICAgICAgIDxzcGFuPk15IEFjY291bnQ8L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgIDwvTGluaz5cclxuICAgICAgICAgICAgICAgICAgPExpbmtcclxuICAgICAgICAgICAgICAgICAgICBocmVmPVwiL29yZGVyc1wiXHJcbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTMgcHgtNCBweS0zIHRleHQtZ3JheS03MDAgaG92ZXI6YmctZ3JheS01MCBob3Zlcjp0ZXh0LXByaW1hcnkgcm91bmRlZC1sZyB0cmFuc2l0aW9uLWNvbG9yc1wiXHJcbiAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0U2hvd01vYmlsZU1lbnUoZmFsc2UpfVxyXG4gICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgPFBhY2thZ2UgY2xhc3NOYW1lPVwiaC01IHctNVwiIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgPHNwYW4+TXkgT3JkZXJzPC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICA8L0xpbms+XHJcbiAgICAgICAgICAgICAgICAgIDxMaW5rXHJcbiAgICAgICAgICAgICAgICAgICAgaHJlZj1cIi9hZGRyZXNzZXNcIlxyXG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0zIHB4LTQgcHktMyB0ZXh0LWdyYXktNzAwIGhvdmVyOmJnLWdyYXktNTAgaG92ZXI6dGV4dC1wcmltYXJ5IHJvdW5kZWQtbGcgdHJhbnNpdGlvbi1jb2xvcnNcIlxyXG4gICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFNob3dNb2JpbGVNZW51KGZhbHNlKX1cclxuICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgIDxNYXBQaW4gY2xhc3NOYW1lPVwiaC01IHctNVwiIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgPHNwYW4+TXkgQWRkcmVzc2VzPC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICA8L0xpbms+XHJcbiAgICAgICAgICAgICAgICAgIDxidXR0b25cclxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMyB3LWZ1bGwgcHgtNCBweS0zIHRleHQtcmVkLTYwMCBob3ZlcjpiZy1yZWQtNTAgcm91bmRlZC1sZyB0cmFuc2l0aW9uLWNvbG9yc1wiXHJcbiAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgbG9nb3V0KCk7XHJcbiAgICAgICAgICAgICAgICAgICAgICBzZXRTaG93TW9iaWxlTWVudShmYWxzZSk7XHJcbiAgICAgICAgICAgICAgICAgICAgICByb3V0ZXIucHVzaCgnLycpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgdG9hc3Quc3VjY2VzcygnTG9nZ2VkIG91dCBzdWNjZXNzZnVsbHknKTtcclxuICAgICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgPExvZ091dCBjbGFzc05hbWU9XCJoLTUgdy01XCIgLz5cclxuICAgICAgICAgICAgICAgICAgICA8c3Bhbj5Mb2dvdXQ8L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgPC8+XHJcbiAgICAgICAgICAgICAgKSA6IChcclxuICAgICAgICAgICAgICAgIDw+XHJcbiAgICAgICAgICAgICAgICAgIDxMaW5rXHJcbiAgICAgICAgICAgICAgICAgICAgaHJlZj1cIi9sb2dpblwiXHJcbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTMgcHgtNCBweS0zIHRleHQtZ3JheS03MDAgaG92ZXI6YmctZ3JheS01MCBob3Zlcjp0ZXh0LXByaW1hcnkgcm91bmRlZC1sZyB0cmFuc2l0aW9uLWNvbG9yc1wiXHJcbiAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0U2hvd01vYmlsZU1lbnUoZmFsc2UpfVxyXG4gICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgPFVzZXIgY2xhc3NOYW1lPVwiaC01IHctNVwiIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgPHNwYW4+e3QoJ2xvZ2luJyl9PC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICA8L0xpbms+XHJcbiAgICAgICAgICAgICAgICAgIDxMaW5rXHJcbiAgICAgICAgICAgICAgICAgICAgaHJlZj1cIi9zaWdudXBcIlxyXG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0zIHB4LTQgcHktMyB0ZXh0LWdyYXktNzAwIGhvdmVyOmJnLWdyYXktNTAgaG92ZXI6dGV4dC1wcmltYXJ5IHJvdW5kZWQtbGcgdHJhbnNpdGlvbi1jb2xvcnNcIlxyXG4gICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFNob3dNb2JpbGVNZW51KGZhbHNlKX1cclxuICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgIDxVc2VyUGx1cyBjbGFzc05hbWU9XCJoLTUgdy01XCIgLz5cclxuICAgICAgICAgICAgICAgICAgICA8c3Bhbj57dCgnc2lnblVwJyl9PC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICA8L0xpbms+XHJcbiAgICAgICAgICAgICAgICA8Lz5cclxuICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIDwvbmF2PlxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICApfVxyXG4gICAgICBcclxuICAgICAge3Nob3dDb2xvclBpY2tlciAmJiAoXHJcbiAgICAgICAgPENvbG9yUGlja2VyXHJcbiAgICAgICAgICBvbkNvbG9yU2VsZWN0PXsoY29sb3IpID0+IHtcclxuICAgICAgICAgICAgc2V0UHJpbWFyeUNvbG9yKGNvbG9yKTtcclxuICAgICAgICAgICAgc2V0U2hvd0NvbG9yUGlja2VyKGZhbHNlKTtcclxuICAgICAgICAgIH19XHJcbiAgICAgICAgICBvbkNsb3NlPXsoKSA9PiBzZXRTaG93Q29sb3JQaWNrZXIoZmFsc2UpfVxyXG4gICAgICAgIC8+XHJcbiAgICAgICl9XHJcbiAgICA8L2hlYWRlcj5cclxuICApO1xyXG59Il0sIm5hbWVzIjpbIlBob25lIiwiTWFpbCIsIk1lc3NhZ2VDaXJjbGUiLCJVc2VyIiwiVXNlclBsdXMiLCJIZWFydCIsIlNob3BwaW5nQ2FydCIsIk1lbnUiLCJTZWFyY2giLCJDaGV2cm9uRG93biIsIkdsb2JlIiwiWCIsIlBhY2thZ2UiLCJMb2dPdXQiLCJNYXBQaW4iLCJ1c2VSb3V0ZXIiLCJMaW5rIiwidXNlRWZmZWN0IiwidXNlU3RhdGUiLCJ1c2VDYXJ0IiwidXNlV2lzaGxpc3QiLCJ0b2FzdCIsIk1ha2VBcGlDYWxsQXN5bmMiLCJDb25maWciLCJOYXZpZ2F0aW9uTWVudSIsIk5hdmlnYXRpb25NZW51SXRlbSIsIk5hdmlnYXRpb25NZW51TGluayIsIk5hdmlnYXRpb25NZW51TGlzdCIsIlBvcG92ZXIiLCJQb3BvdmVyQ29udGVudCIsIlBvcG92ZXJUcmlnZ2VyIiwiY24iLCJCdXR0b24iLCJ1c2VTZXR0aW5ncyIsInVzZVVzZXIiLCJDb2xvclBpY2tlciIsIkhlYWRlciIsInJvdXRlciIsImNhdGVnb3JpZXMiLCJzZXRDYXRlZ29yaWVzIiwiaXNMb2FkaW5nIiwic2V0SXNMb2FkaW5nIiwic2hvd0NvbG9yUGlja2VyIiwic2V0U2hvd0NvbG9yUGlja2VyIiwic2VsZWN0ZWRDYXRlZ29yeSIsInNldFNlbGVjdGVkQ2F0ZWdvcnkiLCJzZWxlY3RlZENhdGVnb3J5SWQiLCJzZXRTZWxlY3RlZENhdGVnb3J5SWQiLCJzZWxlY3RlZFN1YmNhdGVnb3J5Iiwic2V0U2VsZWN0ZWRTdWJjYXRlZ29yeSIsInNob3dNb2JpbGVNZW51Iiwic2V0U2hvd01vYmlsZU1lbnUiLCJzaG93TW9iaWxlQ2F0ZWdvcmllcyIsInNldFNob3dNb2JpbGVDYXRlZ29yaWVzIiwic2VhcmNoVGVybSIsInNldFNlYXJjaFRlcm0iLCJjYXJ0Q291bnQiLCJzZXRDYXJ0Q291bnQiLCJ3aXNobGlzdENvdW50Iiwic2V0V2lzaGxpc3RDb3VudCIsImNhcnQiLCJ3aXNobGlzdCIsInVzZXIiLCJpc0xvZ2dlZEluIiwibG9nb3V0IiwidGhlbWUiLCJsYW5ndWFnZSIsInByaW1hcnlDb2xvciIsInByaW1hcnlUZXh0Q29sb3IiLCJ0b2dnbGVUaGVtZSIsInNldExhbmd1YWdlIiwic2V0UHJpbWFyeUNvbG9yIiwidCIsImhhbmRsZVNlYXJjaCIsInBhcmFtcyIsIlVSTFNlYXJjaFBhcmFtcyIsImFwcGVuZCIsInRvU3RyaW5nIiwicHVzaCIsImZldGNoQ2F0ZWdvcmllcyIsImNhdGVnb3JpZXNSZXNwb25zZSIsInBhcmFtIiwiaGVhZGVycyIsImxvY2FsU3RvcmFnZSIsImdldEl0ZW0iLCJFTkRfUE9JTlRfTkFNRVMiLCJHRVRfQ0FURUdPUklFU19MSVNUIiwiZGF0YSIsInBhcnNlZERhdGEiLCJKU09OIiwicGFyc2UiLCJBcnJheSIsImlzQXJyYXkiLCJwYXJlbnRDYXRlZ29yaWVzIiwiZmlsdGVyIiwiY2F0IiwiUGFyZW50Q2F0ZWdvcnlJRCIsImNoaWxkQ2F0ZWdvcmllcyIsImZvcm1hdHRlZENhdGVnb3JpZXMiLCJtYXAiLCJwYXJlbnQiLCJpZCIsIkNhdGVnb3J5SUQiLCJuYW1lIiwiTmFtZSIsInN1YmNhdGVnb3JpZXMiLCJjaGlsZCIsImNvbnNvbGUiLCJlcnJvciIsInBhcnNlRXJyb3IiLCJlcnJvck1lc3NhZ2UiLCJpdGVtcyIsInRvdGFsSXRlbXMiLCJ3aXNobGlzdEl0ZW1zIiwiaGVhZGVyIiwiY2xhc3NOYW1lIiwidmFyaWFudCIsInNpemUiLCJvbkNsaWNrIiwiZGl2Iiwic3R5bGUiLCJiYWNrZ3JvdW5kQ29sb3IiLCJjb2xvciIsImhyZWYiLCJzcGFuIiwid2luZG93Iiwib3BlbiIsImVuY29kZVVSSUNvbXBvbmVudCIsImFzQ2hpbGQiLCJGaXJzdE5hbWUiLCJVc2VyTmFtZSIsImFsaWduIiwic3VjY2VzcyIsImltZyIsInNyYyIsInByb2Nlc3MiLCJlbnYiLCJORVhUX1BVQkxJQ19BRE1JTl9CQVNFX1VSTCIsImFsdCIsImlucHV0IiwidHlwZSIsInBsYWNlaG9sZGVyIiwidmFsdWUiLCJvbkNoYW5nZSIsImUiLCJ0YXJnZXQiLCJvbktleURvd24iLCJrZXkiLCJjYXRlZ29yeSIsImJ1dHRvbiIsInN1YiIsImluZGV4IiwiYXJpYS1sYWJlbCIsIm5hdiIsIm9uQ29sb3JTZWxlY3QiLCJvbkNsb3NlIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/ui/header.tsx\n"));

/***/ })

});