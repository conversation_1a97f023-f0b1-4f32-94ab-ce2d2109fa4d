!function(t){"use strict";var e={enabled:!0,endpoints:["https://noembed.com/embed?nowrap=on","https://api.maxmade.nl/url2iframe/embed"]};t.extend(!0,t.trumbowyg,{plugins:{pasteEmbed:{init:function(n){n.o.plugins.pasteEmbed=t.extend(!0,{},e,n.o.plugins.pasteEmbed||{}),n.o.plugins.pasteEmbed.enabled&&n.pasteHandlers.push(function(e){try{var a=(e.originalEvent||e).clipboardData,r=a.getData("Text"),s=n.o.plugins.pasteEmbed.endpoints,i=null;if(r.startsWith("http")){e.stopPropagation(),e.preventDefault();var o={url:r.trim()},p="",l=0;i&&i.transport&&i.transport.abort(),i=t.ajax({crossOrigin:!0,url:s[l],type:"GET",data:o,cache:!1,dataType:"jsonp",success:function(t){t.html?(l=0,p=t.html):l+=1},error:function(){l+=1},complete:function(){0===p.length&&l<s.length-1&&(this.url=s[l],this.data=o,t.ajax(this)),l===s.length-1&&(p=t("<a>",{href:r,text:r}).prop("outerHTML")),p.length>0&&(l=0,n.execCmd("insertHTML",p))}})}}catch(u){}})}}}})}(jQuery);