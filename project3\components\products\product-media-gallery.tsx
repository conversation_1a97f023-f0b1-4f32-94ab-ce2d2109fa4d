'use client'

import { useState, useRef, useEffect } from 'react'
import { Play, Pause, ChevronLeft, ChevronRight, Image as ImageIcon, Video as VideoIcon, Search, ZoomIn, ZoomOut } from 'lucide-react'
import { cn } from '@/lib/utils'

// Component for generating video thumbnails
function VideoThumbnail({ src, alt, className }: { src: string; alt?: string; className?: string }) {
  const [thumbnailSrc, setThumbnailSrc] = useState<string | null>(null)
  const [error, setError] = useState(false)
  const videoRef = useRef<HTMLVideoElement>(null)
  const canvasRef = useRef<HTMLCanvasElement>(null)

  useEffect(() => {
    const generateThumbnail = () => {
      const video = videoRef.current
      const canvas = canvasRef.current

      if (!video || !canvas) return

      const context = canvas.getContext('2d')
      if (!context) return

      // Set canvas dimensions to match video
      canvas.width = video.videoWidth || 320
      canvas.height = video.videoHeight || 240

      try {
        // Draw the current frame to canvas
        context.drawImage(video, 0, 0, canvas.width, canvas.height)

        // Convert canvas to data URL
        const dataURL = canvas.toDataURL('image/jpeg', 0.8)
        setThumbnailSrc(dataURL)
      } catch (err) {
        console.error('Error generating video thumbnail:', err)
        setError(true)
      }
    }

    const handleLoadedData = () => {
      const video = videoRef.current
      if (video) {
        // Seek to 1 second or 10% of video duration, whichever is smaller
        const seekTime = Math.min(1, video.duration * 0.1)
        video.currentTime = seekTime
      }
    }

    const handleSeeked = () => {
      // Small delay to ensure frame is rendered
      setTimeout(generateThumbnail, 100)
    }

    const video = videoRef.current
    if (video) {
      video.addEventListener('loadeddata', handleLoadedData)
      video.addEventListener('seeked', handleSeeked)
      video.addEventListener('error', () => setError(true))

      return () => {
        video.removeEventListener('loadeddata', handleLoadedData)
        video.removeEventListener('seeked', handleSeeked)
        video.removeEventListener('error', () => setError(true))
      }
    }
  }, [src])

  if (error) {
    return (
      <div className={cn("bg-gray-200 flex items-center justify-center", className)}>
        <VideoIcon size={16} className="text-gray-500" />
      </div>
    )
  }

  return (
    <div className={cn("relative", className)}>
      {/* Hidden video element for thumbnail generation */}
      <video
        ref={videoRef}
        src={src}
        className="hidden"
        muted
        playsInline
        preload="metadata"
      />

      {/* Hidden canvas for thumbnail generation */}
      <canvas ref={canvasRef} className="hidden" />

      {/* Display thumbnail or fallback */}
      {thumbnailSrc ? (
        <img
          src={thumbnailSrc}
          alt={alt || 'Video thumbnail'}
          className="w-full h-full object-cover"
        />
      ) : (
        <div className="w-full h-full bg-gray-200 flex items-center justify-center animate-pulse">
          <VideoIcon size={16} className="text-gray-400" />
        </div>
      )}
    </div>
  )
}

interface MediaItem {
  type: 'image' | 'video'
  url: string
  thumbnail?: string
  alt?: string
}

interface ProductMediaGalleryProps {
  media: MediaItem[]
  className?: string
}

export function ProductMediaGallery({ media, className }: ProductMediaGalleryProps) {
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isZoomed, setIsZoomed] = useState(false)
  const [zoomLevel, setZoomLevel] = useState(1.5)
  const [zoomPosition, setZoomPosition] = useState({ x: 50, y: 50 })
  const [isPlaying, setIsPlaying] = useState(false)
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [activeMediaType, setActiveMediaType] = useState<'all' | 'image' | 'video'>('all')
  const [isMobile, setIsMobile] = useState(false)
  
  const imageRef = useRef<HTMLImageElement>(null)
  const videoRef = useRef<HTMLVideoElement>(null)
  const fullscreenContainerRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    // Check if device is mobile
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768)
    }
    
    // Initial check
    checkMobile()
    
    // Add event listener for window resize
    window.addEventListener('resize', checkMobile)
    
    // Cleanup
    return () => window.removeEventListener('resize', checkMobile)
  }, [])
  
  // Filter media based on active type
  const filteredMedia = media.filter(item => 
    activeMediaType === 'all' || item.type === activeMediaType
  )
  
  const currentItem = filteredMedia[currentIndex] || media[0]
  const hasMultipleItems = filteredMedia.length > 1
  
  // Reset index when media changes
  useEffect(() => {
    setCurrentIndex(0)
    setIsPlaying(false)
  }, [activeMediaType, media])
  
  const goToPrev = () => {
    setCurrentIndex((prev) => 
      prev === 0 ? filteredMedia.length - 1 : prev - 1
    )
    setIsPlaying(false)
  }
  
  const goToNext = () => {
    setCurrentIndex((prev) => 
      prev === filteredMedia.length - 1 ? 0 : prev + 1
    )
    setIsPlaying(false)
  }
  
  const togglePlayPause = () => {
    if (currentItem.type === 'video') {
      if (videoRef.current) {
        if (isPlaying) {
          videoRef.current.pause()
        } else {
          videoRef.current.play()
        }
        setIsPlaying(!isPlaying)
      }
    }
  }
  
  const handleVideoEnded = () => {
    setIsPlaying(false)
    if (hasMultipleItems) {
      goToNext()
    }
  }
  
  const handleThumbnailClick = (index: number) => {
    const selectedItem = filteredMedia[index]
    setCurrentIndex(index)
    setIsZoomed(false)

    // Auto-play video if the selected item is a video
    if (selectedItem?.type === 'video') {
      setIsPlaying(true)
      // Small delay to ensure video element is ready
      setTimeout(() => {
        if (videoRef.current) {
          videoRef.current.play().catch(console.error)
        }
      }, 100)
    } else {
      setIsPlaying(false)
    }
  }

  const handleImageMouseMove = (e: React.MouseEvent<HTMLImageElement>) => {
    if (!isZoomed || !imageRef.current) return
    
    const rect = imageRef.current.getBoundingClientRect()
    const x = ((e.clientX - rect.left) / rect.width) * 100
    const y = ((e.clientY - rect.top) / rect.height) * 100
    
    setZoomPosition({ x, y })
  }

  const handleImageClick = () => {
    // Only enable zoom on non-mobile devices
    if (currentItem.type === 'image' && !isMobile) {
      setIsZoomed(!isZoomed)
    } else if (currentItem.type === 'video') {
      togglePlayPause()
    }
  }

  const handleImageDoubleClick = () => {
    if (currentItem.type === 'image' && !isMobile) {
      setIsFullscreen(true)
    }
  }

  const increaseZoom = () => {
    setZoomLevel(prev => Math.min(prev + 0.5, 4))
    setIsZoomed(true)
  }

  const decreaseZoom = () => {
    setZoomLevel(prev => {
      const newLevel = Math.max(prev - 0.5, 1)
      if (newLevel === 1) {
        setIsZoomed(false)
      }
      return newLevel
    })
  }

  const resetZoom = () => {
    setZoomLevel(1.5)
    setIsZoomed(false)
  }
  
  // Group media by type for filter buttons
  const mediaCounts = media.reduce((acc, item) => {
    acc[item.type] = (acc[item.type] || 0) + 1
    return acc
  }, {} as Record<string, number>)

  return (
    <div className={cn('flex flex-col gap-4', className)}>
      {/* Main Media Display */}
      <div className="relative aspect-square w-full bg-gray-100 rounded-lg overflow-hidden">
        {currentItem.type === 'image' ? (
          <div className="relative w-full h-full overflow-hidden group">
            <style jsx>{`
              .cursor-zoom-in {
                cursor: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="11" cy="11" r="8"/><path d="m21 21-4.35-4.35"/><line x1="11" y1="8" x2="11" y2="14"/><line x1="8" y1="11" x2="14" y2="11"/></svg>') 12 12, auto;
              }
              .cursor-zoom-out {
                cursor: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="11" cy="11" r="8"/><path d="m21 21-4.35-4.35"/><line x1="8" y1="11" x2="14" y2="11"/></svg>') 12 12, auto;
              }
            `}</style>
            <img
              ref={imageRef}
              src={currentItem.url}
              alt={currentItem.alt || 'Product image'}
              className={cn(
                "w-full h-full object-contain transition-transform duration-300",
                !isMobile && (isZoomed ? "cursor-zoom-out" : "cursor-zoom-in")
              )}
              style={
                isZoomed
                  ? {
                      transform: `scale(${zoomLevel})`,
                      transformOrigin: `${zoomPosition.x}% ${zoomPosition.y}%`,
                    }
                  : { transform: 'scale(1)' }
              }
              onClick={handleImageClick}
              onDoubleClick={handleImageDoubleClick}
              onMouseMove={handleImageMouseMove}
              onMouseLeave={() => setIsZoomed(false)}
            />
            {/* Magnifying glass overlay - shows on hover (hidden on mobile) */}
            <div className="absolute inset-0 flex items-center justify-center pointer-events-none opacity-0 group-hover:opacity-100 transition-opacity duration-300 hidden md:flex">
              <div className="bg-white/95 rounded-full p-4 shadow-xl border-2 border-gray-200">
                {isZoomed ? (
                  <ZoomOut className="h-8 w-8 text-gray-700" />
                ) : (
                  <ZoomIn className="h-8 w-8 text-gray-700" />
                )}
              </div>
            </div>
            {/* Zoom controls - hidden on mobile */}
            <div className="absolute top-2 left-2 flex flex-col gap-2 hidden md:flex">
              <div className="bg-black/70 text-white text-xs px-2 py-1 rounded flex items-center gap-1">
                {isZoomed ? (
                  <>
                    <ZoomOut className="h-3 w-3" />
                    <span>Zoom: {Math.round(zoomLevel * 100)}%</span>
                  </>
                ) : (
                  <>
                    <ZoomIn className="h-3 w-3" />
                    <span>Click to zoom</span>
                  </>
                )}
              </div>
              <div className="flex flex-col gap-1">
                <button
                  onClick={increaseZoom}
                  className="bg-white/90 hover:bg-white text-gray-700 rounded p-1 shadow-sm transition-all"
                  title="Zoom in"
                >
                  <ZoomIn className="h-4 w-4" />
                </button>
                <button
                  onClick={decreaseZoom}
                  className="bg-white/90 hover:bg-white text-gray-700 rounded p-1 shadow-sm transition-all"
                  title="Zoom out"
                >
                  <ZoomOut className="h-4 w-4" />
                </button>
                <button
                  onClick={resetZoom}
                  className="bg-white/90 hover:bg-white text-gray-700 rounded p-1 shadow-sm transition-all text-xs"
                  title="Reset zoom"
                >
                  1:1
                </button>
              </div>
            </div>
            
            {/* Fullscreen button - hidden on mobile */}
            <button
              onClick={() => setIsFullscreen(true)}
              className="absolute top-2 right-12 bg-white/90 hover:bg-white text-gray-700 rounded p-2 shadow-sm transition-all hidden md:block"
              title="View fullscreen"
            >
              <Search className="h-4 w-4" />
            </button>
          </div>
        ) : (
          <div className="relative w-full h-full">
            <video
              ref={videoRef}
              src={currentItem.url}
              className="w-full h-full object-contain"
              controls={false}
              onEnded={handleVideoEnded}
              onPlay={() => setIsPlaying(true)}
              onPause={() => setIsPlaying(false)}
              onLoadedData={() => setIsVideoLoaded(true)}
              playsInline
            />
            {!isVideoLoaded && (
              <div className="absolute inset-0 flex items-center justify-center bg-gray-200">
                <div className="animate-pulse">Loading video...</div>
              </div>
            )}
            <button
              onClick={togglePlayPause}
              className={cn(
                'absolute inset-0 flex items-center justify-center transition-opacity',
                isPlaying ? 'opacity-0 hover:opacity-100' : 'opacity-80',
                !isVideoLoaded && 'hidden'
              )}
              aria-label={isPlaying ? 'Pause' : 'Play'}
            >
              <div className="bg-black/50 text-white rounded-full p-3">
                {isPlaying ? <Pause size={24} /> : <Play size={24} />}
              </div>
            </button>
          </div>
        )}
        
        {/* Media Type Indicator */}
        <div className="absolute top-2 right-2 bg-black/70 text-white text-xs px-2 py-1 rounded flex items-center gap-1">
          {currentItem.type === 'image' ? (
            <ImageIcon size={12} />
          ) : (
            <VideoIcon size={12} />
          )}
          <span>{currentItem.type === 'image' ? 'Image' : 'Video'}</span>
        </div>
        
        {/* Navigation Arrows */}
        {hasMultipleItems && (
          <>
            <button
              onClick={goToPrev}
              className="absolute left-2 top-1/2 -translate-y-1/2 bg-white/80 hover:bg-white text-gray-900 rounded-full p-2 shadow-md transition-all"
              aria-label="Previous media"
            >
              <ChevronLeft size={20} />
            </button>
            <button
              onClick={goToNext}
              className="absolute right-2 top-1/2 -translate-y-1/2 bg-white/80 hover:bg-white text-gray-900 rounded-full p-2 shadow-md transition-all"
              aria-label="Next media"
            >
              <ChevronRight size={20} />
            </button>
          </>
        )}
      </div>
      
      {/* Media Type Filter Buttons */}
      <div className="flex gap-2">
        <button
          onClick={() => setActiveMediaType('all')}
          className={cn(
            'px-3 py-1 text-sm rounded-full border',
            activeMediaType === 'all' 
              ? 'bg-blue-600 text-white border-blue-600' 
              : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
          )}
        >
          All ({media.length})
        </button>
        {mediaCounts.image > 0 && (
          <button
            onClick={() => setActiveMediaType('image')}
            className={cn(
              'px-3 py-1 text-sm rounded-full border flex items-center gap-1',
              activeMediaType === 'image' 
                ? 'bg-blue-600 text-white border-blue-600' 
                : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
            )}
          >
            <ImageIcon size={14} />
            <span>{mediaCounts.image}</span>
          </button>
        )}
        {mediaCounts.video > 0 && (
          <button
            onClick={() => setActiveMediaType('video')}
            className={cn(
              'px-3 py-1 text-sm rounded-full border flex items-center gap-1',
              activeMediaType === 'video' 
                ? 'bg-blue-600 text-white border-blue-600' 
                : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
            )}
          >
            <VideoIcon size={14} />
            <span>{mediaCounts.video}</span>
          </button>
        )}
      </div>
      
      {/* Thumbnails */}
      {hasMultipleItems && (
        <div className="flex gap-2 sm:gap-3 overflow-x-auto pb-2 -mx-2 px-2">
          {filteredMedia.map((item, index) => (
            <button
              key={index}
              onClick={() => handleThumbnailClick(index)}
              className={cn(
                'relative flex-shrink-0 w-20 h-20 sm:w-16 sm:h-16 md:w-20 md:h-20 rounded-md overflow-hidden border-2 transition-all',
                index === currentIndex
                  ? 'border-blue-600 ring-2 ring-blue-400'
                  : 'border-gray-200 hover:border-gray-400'
              )}
              aria-label={`View ${item.type} ${index + 1}`}
            >
              {item.type === 'image' ? (
                <img
                  src={item.thumbnail || item.url}
                  alt={item.alt || ''}
                  className="w-full h-full object-cover"
                />
              ) : (
                <div className="relative w-full h-full overflow-hidden">
                  {item.thumbnail ? (
                    // Use provided thumbnail if available
                    <img
                      src={item.thumbnail}
                      alt={item.alt || 'Video thumbnail'}
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    // Generate thumbnail from video
                    <VideoThumbnail
                      src={item.url}
                      alt={item.alt}
                      className="w-full h-full"
                    />
                  )}
                  {/* Video play icon overlay */}
                  <div className="absolute inset-0 flex items-center justify-center bg-black/30">
                    <div className="bg-white/90 rounded-full p-1 sm:p-1">
                      <Play size={14} className="text-gray-700 ml-0.5 sm:w-3 sm:h-3" />
                    </div>
                  </div>
                </div>
              )}
            </button>
          ))}
        </div>
      )}
      
      {/* Fullscreen Modal */}
      {isFullscreen && currentItem.type === 'image' && (
        <div className="fixed inset-0 z-50 bg-black/95 flex items-center justify-center">
          <div className="relative w-full h-full flex items-center justify-center p-4">
            <img
              src={currentItem.url}
              alt={currentItem.alt || 'Product image'}
              className="max-w-full max-h-full object-contain"
              style={{ maxWidth: '95vw', maxHeight: '95vh' }}
            />
            
            {/* Close button */}
            <button
              onClick={() => setIsFullscreen(false)}
              className="absolute top-4 right-4 bg-white/20 hover:bg-white/30 text-white rounded-full p-3 transition-all"
              title="Close fullscreen"
            >
              <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
            
            {/* Navigation in fullscreen */}
            {hasMultipleItems && (
              <>
                <button
                  onClick={goToPrev}
                  className="absolute left-4 top-1/2 -translate-y-1/2 bg-white/20 hover:bg-white/30 text-white rounded-full p-3 transition-all"
                  title="Previous image"
                >
                  <ChevronLeft className="h-6 w-6" />
                </button>
                <button
                  onClick={goToNext}
                  className="absolute right-4 top-1/2 -translate-y-1/2 bg-white/20 hover:bg-white/30 text-white rounded-full p-3 transition-all"
                  title="Next image"
                >
                  <ChevronRight className="h-6 w-6" />
                </button>
              </>
            )}
            
            {/* Image counter */}
            <div className="absolute bottom-4 left-1/2 -translate-x-1/2 bg-black/70 text-white px-3 py-1 rounded-full text-sm">
              {currentIndex + 1} of {filteredMedia.length}
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
