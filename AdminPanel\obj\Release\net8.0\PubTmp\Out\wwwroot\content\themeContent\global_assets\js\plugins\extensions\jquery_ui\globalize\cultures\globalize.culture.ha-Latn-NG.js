/*
 * Globalize Culture ha-Latn-NG
 *
 * http://github.com/jquery/globalize
 *
 * Copyright Software Freedom Conservancy, Inc.
 * Dual licensed under the MIT or GPL Version 2 licenses.
 * http://jquery.org/license
 *
 * This file was generated by the Globalize Culture Generator
 * Translation: bugs found in this file need to be fixed in the generator
 */

(function( window, undefined ) {

var Globalize;

if ( typeof require !== "undefined" &&
	typeof exports !== "undefined" &&
	typeof module !== "undefined" ) {
	// Assume CommonJS
	Globalize = require( "globalize" );
} else {
	// Global variable
	Globalize = window.Globalize;
}

Globalize.addCultureInfo( "ha-Latn-NG", "default", {
	name: "ha-Latn-NG",
	englishName: "Hausa (Latin, Nigeria)",
	nativeName: "Hausa (Nigeria)",
	language: "ha-Latn",
	numberFormat: {
		currency: {
			pattern: ["$-n","$ n"],
			symbol: "N"
		}
	},
	calendars: {
		standard: {
			days: {
				names: ["<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON>'<PERSON>","<PERSON><PERSON>"],
				namesAbbr: ["Lah","Lit","Tal","Lar","<PERSON>h","Jum","Asa"],
				namesShort: ["L","L","T","L","A","J","A"]
			},
			months: {
				names: ["Januwaru","Febreru","Maris","Afrilu","Mayu","Yuni","Yuli","Agusta","Satumba","Oktocba","Nuwamba","Disamba",""],
				namesAbbr: ["Jan","Feb","Mar","Afr","May","Yun","Yul","Agu","Sat","Okt","Nuw","Dis",""]
			},
			AM: ["Safe","safe","SAFE"],
			PM: ["Yamma","yamma","YAMMA"],
			eras: [{"name":"AD","start":null,"offset":0}],
			patterns: {
				d: "d/M/yyyy"
			}
		}
	}
});

}( this ));
