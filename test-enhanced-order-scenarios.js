/**
 * Test script for Enhanced Order Processing with Points and Coupons
 * Tests all four scenarios:
 * 1. Normal order without points and coupon
 * 2. Normal order with points only
 * 3. Normal order with coupon only
 * 4. Normal order with both points and coupon
 */

const axios = require('axios');

// Configuration
const BACKEND_URL = 'https://admin.codemedicalapps.com';
const TEST_USER_ID = 1; // Replace with actual test user ID
const TEST_COUPON_CODE = 'A9804F'; // Replace with actual test coupon code
const JWT_TOKEN = 'your-jwt-token-here'; // Replace with actual JWT token

// Sample cart data
const sampleCartData = [
  {
    ProductId: 15,
    ProductName: "Test Product 1",
    Price: 50.00,
    Quantity: 1,
    ItemPriceTotal: 50.00,
    OrderItemDiscountTotal: 0,
    ShippingChargesTotal: 0,
    OrderItemAttributeChargesTotal: 0,
    ItemSubTotal: 50.00,
    ProductAllSelectedAttributes: "[]"
  },
  {
    ProductId: 16,
    ProductName: "Test Product 2", 
    Price: 30.00,
    Quantity: 2,
    ItemPriceTotal: 60.00,
    OrderItemDiscountTotal: 0,
    ShippingChargesTotal: 0,
    OrderItemAttributeChargesTotal: 0,
    ItemSubTotal: 60.00,
    ProductAllSelectedAttributes: "[]"
  }
];

// Test scenarios
const testScenarios = [
  {
    name: "Scenario 1: Normal order without points and coupon",
    orderData: {
      OrderNote: "Test order - Scenario 1",
      cartJsonData: JSON.stringify(sampleCartData),
      OrderTotal: 110.00,
      CouponCode: "",
      Description: "Test order without points and coupon",
      StripeStatus: "",
      StripeResponseJson: "",
      StripeBalanceTransactionId: "",
      StripeChargeId: "",
      PayPalResponseJson: "",
      CurrencyCode: "USD",
      PaymentMethod: 6, // Cash on delivery
      Point: null,
      addressid: 1
    }
  },
  {
    name: "Scenario 2: Normal order with points only",
    orderData: {
      OrderNote: "Test order - Scenario 2",
      cartJsonData: JSON.stringify(sampleCartData),
      OrderTotal: 100.00, // Reduced by 10 points
      CouponCode: "",
      Description: "Test order with points only",
      StripeStatus: "",
      StripeResponseJson: "",
      StripeBalanceTransactionId: "",
      StripeChargeId: "",
      PayPalResponseJson: "",
      CurrencyCode: "USD",
      PaymentMethod: 6,
      Point: 10, // Using 10 points
      addressid: 1
    }
  },
  {
    name: "Scenario 3: Normal order with coupon only",
    orderData: {
      OrderNote: "Test order - Scenario 3",
      cartJsonData: JSON.stringify(sampleCartData),
      OrderTotal: 99.00, // Reduced by coupon discount
      CouponCode: TEST_COUPON_CODE,
      Description: "Test order with coupon only",
      StripeStatus: "",
      StripeResponseJson: "",
      StripeBalanceTransactionId: "",
      StripeChargeId: "",
      PayPalResponseJson: "",
      CurrencyCode: "USD",
      PaymentMethod: 6,
      Point: null,
      addressid: 1
    }
  },
  {
    name: "Scenario 4: Normal order with both points and coupon",
    orderData: {
      OrderNote: "Test order - Scenario 4",
      cartJsonData: JSON.stringify(sampleCartData),
      OrderTotal: 89.00, // Reduced by both points and coupon
      CouponCode: TEST_COUPON_CODE,
      Description: "Test order with both points and coupon",
      StripeStatus: "",
      StripeResponseJson: "",
      StripeBalanceTransactionId: "",
      StripeChargeId: "",
      PayPalResponseJson: "",
      CurrencyCode: "USD",
      PaymentMethod: 6,
      Point: 10, // Using 10 points
      addressid: 1
    }
  }
];

// API client setup
const api = axios.create({
  baseURL: BACKEND_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  }
});

// Add JWT token if provided
if (JWT_TOKEN && JWT_TOKEN !== 'your-jwt-token-here') {
  api.defaults.headers.common['Authorization'] = `Bearer ${JWT_TOKEN}`;
}

async function testOrderScenario(scenario) {
  console.log(`\n🧪 Testing: ${scenario.name}`);
  console.log('=' .repeat(60));
  
  try {
    console.log('📤 Request Data:', JSON.stringify(scenario.orderData, null, 2));
    
    const response = await api.post('/api/v1/common/post-order-direct', scenario.orderData);
    
    console.log('✅ Response Status:', response.status);
    console.log('📥 Response Data:', JSON.stringify(response.data, null, 2));
    
    if (response.data.statusCode === 200) {
      console.log('🎉 Order placed successfully!');
      if (response.data.data) {
        console.log(`📋 Order ID: ${response.data.data.OrderID}`);
        console.log(`📋 Order Number: ${response.data.data.OrderNumber}`);
      }
    } else {
      console.log('❌ Order failed:', response.data.errorMessage);
    }
    
    return {
      success: response.data.statusCode === 200,
      data: response.data
    };
    
  } catch (error) {
    console.log('❌ Error:', error.response?.data || error.message);
    return {
      success: false,
      error: error.response?.data || error.message
    };
  }
}

async function runAllTests() {
  console.log('🚀 Starting Enhanced Order Processing Tests');
  console.log('Backend URL:', BACKEND_URL);
  console.log('Test User ID:', TEST_USER_ID);
  console.log('Test Coupon Code:', TEST_COUPON_CODE);
  
  const results = [];
  
  for (const scenario of testScenarios) {
    const result = await testOrderScenario(scenario);
    results.push({
      scenario: scenario.name,
      success: result.success,
      data: result.data || result.error
    });
    
    // Wait between tests
    await new Promise(resolve => setTimeout(resolve, 2000));
  }
  
  // Summary
  console.log('\n📊 TEST SUMMARY');
  console.log('=' .repeat(60));
  
  results.forEach((result, index) => {
    const status = result.success ? '✅ PASSED' : '❌ FAILED';
    console.log(`${index + 1}. ${result.scenario}: ${status}`);
  });
  
  const passedTests = results.filter(r => r.success).length;
  const totalTests = results.length;
  
  console.log(`\n🎯 Overall Result: ${passedTests}/${totalTests} tests passed`);
  
  if (passedTests === totalTests) {
    console.log('🎉 All tests passed! Enhanced order processing is working correctly.');
  } else {
    console.log('⚠️  Some tests failed. Please review the implementation.');
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  runAllTests().catch(console.error);
}

module.exports = {
  testOrderScenario,
  runAllTests,
  testScenarios
};
