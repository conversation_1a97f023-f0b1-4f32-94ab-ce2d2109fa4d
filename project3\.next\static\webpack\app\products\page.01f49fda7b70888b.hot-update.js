"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/products/page",{

/***/ "(app-pages-browser)/./app/products/page.tsx":
/*!*******************************!*\
  !*** ./app/products/page.tsx ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProductsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Filter_Package_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Filter,Package!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Filter_Package_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Filter,Package!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Filter_Package_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Filter,Package!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/filter.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./components/ui/skeleton.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./hooks/use-toast.ts\");\n/* harmony import */ var _components_product_card__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/product-card */ \"(app-pages-browser)/./components/product-card.tsx\");\n/* harmony import */ var _components_ui_pagination__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/pagination */ \"(app-pages-browser)/./components/ui/pagination.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction SearchParamsWrapper(param) {\n    let { children } = param;\n    _s();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const searchTerm = searchParams.get(\"search\") || \"\";\n    const categoryId = searchParams.get(\"category\") || \"all\";\n    const productTypeId = searchParams.get(\"productType\") || \"all\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children({\n            searchTerm,\n            categoryId,\n            productTypeId\n        })\n    }, void 0, false);\n}\n_s(SearchParamsWrapper, \"a+DZx9DY26Zf8FVy1bxe3vp9l1w=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams\n    ];\n});\n_c = SearchParamsWrapper;\nfunction ProductsPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProductsPageSkeleton, {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n            lineNumber: 95,\n            columnNumber: 25\n        }, void 0),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SearchParamsWrapper, {\n            children: (param)=>{\n                let { searchTerm, categoryId, productTypeId } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProductsContent, {\n                    initialSearchTerm: searchTerm,\n                    initialCategoryId: categoryId,\n                    initialProductTypeId: productTypeId\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n                    lineNumber: 98,\n                    columnNumber: 11\n                }, this);\n            }\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n            lineNumber: 96,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n        lineNumber: 95,\n        columnNumber: 5\n    }, this);\n}\n_c1 = ProductsPage;\nfunction ProductsPageSkeleton() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto px-4 py-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"h-8 w-64 bg-gray-200 rounded mb-4 animate-pulse\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n                lineNumber: 112,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"h-4 w-96 bg-gray-200 rounded mb-6 animate-pulse\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n                lineNumber: 113,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col lg:flex-row gap-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:w-1/4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white p-4 rounded-lg shadow\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-6 w-32 bg-gray-200 rounded mb-4 animate-pulse\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n                                    lineNumber: 117,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: Array(4).fill(0).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-10 bg-gray-200 rounded animate-pulse\"\n                                        }, i, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n                                    lineNumber: 118,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n                            lineNumber: 116,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n                        lineNumber: 115,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:w-3/4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-2 gap-4 sm:gap-6 sm:grid-cols-2 lg:grid-cols-4\",\n                            children: Array(12).fill(0).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow overflow-hidden\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"aspect-square bg-gray-200 animate-pulse\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n                                            lineNumber: 139,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-4 space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-4 bg-gray-200 rounded animate-pulse\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n                                                    lineNumber: 141,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-4 w-3/4 bg-gray-200 rounded animate-pulse\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n                                                    lineNumber: 142,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-6 w-1/3 bg-gray-200 rounded animate-pulse\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n                                                    lineNumber: 143,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n                                            lineNumber: 140,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, i, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n                                    lineNumber: 135,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n                            lineNumber: 131,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n                        lineNumber: 130,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n                lineNumber: 114,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n        lineNumber: 111,\n        columnNumber: 5\n    }, this);\n}\n_c2 = ProductsPageSkeleton;\nfunction ProductsContent(param) {\n    let { initialSearchTerm, initialCategoryId, initialProductTypeId } = param;\n    var _categories_find, _categories_find1, _productTypes_find, _categories_find2, _productTypes_find1;\n    _s1();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast)();\n    const [products, setProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [allProducts, setAllProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]); // Store all products for attribute filtering\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalPages, setTotalPages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [sortOrder, setSortOrder] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"Newest\");\n    const [categoryFilter, setCategoryFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialCategoryId !== \"all\" ? Number.parseInt(initialCategoryId) : null);\n    const [productTypeFilter, setProductTypeFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialProductTypeId !== \"all\" ? Number.parseInt(initialProductTypeId) : null);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialSearchTerm);\n    const [searchInput, setSearchInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialSearchTerm);\n    const [categories, setCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [productTypes, setProductTypes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [priceRange, setPriceRange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        min: null,\n        max: null\n    });\n    const [filtersLoading, setFiltersLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [apiError, setApiError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [attributeFilters, setAttributeFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedAttributes, setSelectedAttributes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [expandedAttributes, setExpandedAttributes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const pageSize = 12;\n    // Debounce function\n    function debounce(func, timeout) {\n        let timer;\n        return function() {\n            for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){\n                args[_key] = arguments[_key];\n            }\n            clearTimeout(timer);\n            timer = setTimeout(()=>{\n                func(...args);\n            }, timeout);\n        };\n    }\n    const debouncedSearch = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(debounce({\n        \"ProductsContent.useCallback[debouncedSearch]\": (searchValue)=>{\n            setSearchTerm(searchValue);\n            setCurrentPage(1);\n        }\n    }[\"ProductsContent.useCallback[debouncedSearch]\"], 500), []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProductsContent.useEffect\": ()=>{\n            fetchProducts();\n            fetchCategories();\n            fetchProductTypes();\n        }\n    }[\"ProductsContent.useEffect\"], [\n        currentPage,\n        sortOrder,\n        categoryFilter,\n        productTypeFilter,\n        priceRange,\n        searchTerm\n    ]);\n    // Apply attribute filters to products\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProductsContent.useEffect\": ()=>{\n            if (allProducts.length > 0) {\n                applyAttributeFilters();\n            }\n        }\n    }[\"ProductsContent.useEffect\"], [\n        selectedAttributes,\n        allProducts\n    ]);\n    const applyAttributeFilters = ()=>{\n        let filteredProducts = [\n            ...allProducts\n        ];\n        // Apply attribute filters\n        Object.entries(selectedAttributes).forEach((param)=>{\n            let [attributeName, selectedValueIds] = param;\n            if (selectedValueIds.length > 0) {\n                filteredProducts = filteredProducts.filter((product)=>{\n                    if (!product.Attributes || product.Attributes.length === 0) return false;\n                    return product.Attributes.some((attr)=>attr.AttributeName === attributeName && selectedValueIds.includes(attr.AttributeValueID));\n                });\n            }\n        });\n        setProducts(filteredProducts);\n        setTotalPages(Math.ceil(filteredProducts.length / pageSize));\n    };\n    const buildAttributeFilters = (products)=>{\n        const attributeMap = new Map();\n        products.forEach((product)=>{\n            if (product.Attributes && product.Attributes.length > 0) {\n                product.Attributes.forEach((attr)=>{\n                    const key = \"\".concat(attr.AttributeName, \"|\").concat(attr.DisplayName);\n                    if (!attributeMap.has(key)) {\n                        attributeMap.set(key, new Map());\n                    }\n                    const valueMap = attributeMap.get(key);\n                    const existing = valueMap.get(attr.AttributeValueID);\n                    if (existing) {\n                        existing.count++;\n                    } else {\n                        valueMap.set(attr.AttributeValueID, {\n                            text: attr.AttributeValueText,\n                            count: 1\n                        });\n                    }\n                });\n            }\n        });\n        const filters = [];\n        attributeMap.forEach((valueMap, key)=>{\n            const [attributeName, displayName] = key.split(\"|\");\n            const values = Array.from(valueMap.entries()).map((param)=>{\n                let [id, data] = param;\n                return {\n                    id,\n                    text: data.text,\n                    count: data.count\n                };\n            });\n            filters.push({\n                attributeName,\n                displayName,\n                values: values.sort((a, b)=>a.text.localeCompare(b.text))\n            });\n        });\n        setAttributeFilters(filters.sort((a, b)=>a.displayName.localeCompare(b.displayName)));\n    };\n    const fetchProducts = async ()=>{\n        setLoading(currentPage === 1);\n        setFiltersLoading(currentPage > 1);\n        setApiError(null);\n        try {\n            // Map sort order to numeric values\n            const getOrderByValue = (sortOrder)=>{\n                switch(sortOrder){\n                    case \"Newest\":\n                        return 5; // New value for newest first (ProductId DESC)\n                    case \"Price ASC\":\n                        return 1;\n                    case \"Price DESC\":\n                        return 0;\n                    case \"ProductName ASC\":\n                        return 3;\n                    case \"ProductName DESC\":\n                        return 2;\n                    case \"Rating DESC\":\n                        return 4;\n                    default:\n                        return 5; // Default to newest\n                }\n            };\n            const requestData = {\n                requestParameters: {\n                    SearchTerm: searchTerm || \"\",\n                    SizeID: null,\n                    ColorID: null,\n                    CategoryID: categoryFilter,\n                    TagID: null,\n                    ManufacturerID: null,\n                    producttypeId: productTypeFilter,\n                    MinPrice: priceRange.min,\n                    MaxPrice: priceRange.max,\n                    Rating: null,\n                    OrderBy: getOrderByValue(sortOrder),\n                    PageNo: 1,\n                    PageSize: 1000,\n                    recordValueJson: \"[]\"\n                }\n            };\n            console.log(\"Sending request to Next.js API route:\", requestData);\n            // Use Next.js API route instead of direct external API call\n            const response = await fetch(\"/api/products/get-products\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(requestData)\n            });\n            console.log(\"API route response status:\", response.status);\n            if (!response.ok) {\n                const errorData = await response.json();\n                throw new Error(errorData.error || \"HTTP error! status: \".concat(response.status));\n            }\n            const data = await response.json();\n            console.log(\"API route response data:\", data);\n            if (data && data.data) {\n                let productsData = [];\n                try {\n                    if (typeof data.data === \"string\") {\n                        console.log(\"Parsing string data:\", data.data);\n                        productsData = JSON.parse(data.data);\n                    } else if (Array.isArray(data.data)) {\n                        console.log(\"Using array data directly\");\n                        productsData = data.data;\n                    } else {\n                        console.log(\"Converting object to array:\", data.data);\n                        productsData = [\n                            data.data\n                        ];\n                    }\n                    console.log(\"Processed products data:\", productsData);\n                    if (!Array.isArray(productsData)) {\n                        throw new Error(\"Parsed data is not an array\");\n                    }\n                    if (productsData.length === 0) {\n                        console.log(\"No products found in API response\");\n                        setProducts([]);\n                        setAllProducts([]);\n                        setTotalPages(0);\n                        return;\n                    }\n                    console.log(\"First product in response:\", productsData[0]);\n                    console.log(\"Available fields:\", Object.keys(productsData[0]));\n                    const processedProducts = productsData.map((product)=>{\n                        const rawUrl = product.ProductImagesUrl || product.ProductImageUrl;\n                        let imageUrl = null;\n                        try {\n                            if (rawUrl) {\n                                let cleanUrl = rawUrl;\n                                if (typeof rawUrl === \"string\" && (rawUrl.startsWith(\"[\") || rawUrl.startsWith('\"'))) {\n                                    try {\n                                        const parsed = JSON.parse(rawUrl);\n                                        if (Array.isArray(parsed) && parsed.length > 0) {\n                                            cleanUrl = parsed[0].AttachmentURL || parsed[0];\n                                        } else if (typeof parsed === \"string\") {\n                                            cleanUrl = parsed;\n                                        }\n                                    } catch (jsonError) {\n                                        cleanUrl = rawUrl.replace(/^\"|\"/g, \"\");\n                                    }\n                                }\n                                if (typeof cleanUrl === \"string\" && cleanUrl.trim() !== \"\") {\n                                    cleanUrl = cleanUrl.replace(/^\"|\"$/g, \"\").trim();\n                                    if (cleanUrl) {\n                                        const decodedUrl = decodeURIComponent(cleanUrl);\n                                        if (decodedUrl.startsWith(\"http\")) {\n                                            imageUrl = decodedUrl;\n                                        } else {\n                                            // Normalize path (ensure it starts with exactly one slash)\n                                            let normalizedPath = decodedUrl.startsWith(\"/\") ? decodedUrl : \"/\".concat(decodedUrl);\n                                            // Remove any double slashes in the path\n                                            normalizedPath = normalizedPath.replace(/\\/+/g, '/');\n                                            imageUrl = \"https://admin.codemedicalapps.com\".concat(normalizedPath);\n                                        }\n                                    }\n                                }\n                            }\n                        } catch (error) {\n                            console.error(\"Error processing URL for product\", product.ProductID || product.ProductId, \":\", error);\n                        }\n                        const productId = product.ProductID || product.ProductId || product.Id || product.ID || product.id;\n                        const normalizedProduct = {\n                            ...product,\n                            ProductId: productId,\n                            ProductID: productId,\n                            ProductName: product.ProductName || \"Unnamed Product\",\n                            Price: Number.parseFloat(product.Price) || 0,\n                            OldPrice: product.OldPrice ? Number.parseFloat(product.OldPrice) : undefined,\n                            IQDPrice: Number.parseFloat(product.IQDPrice) || 0,\n                            ProductTypeID: product.ProductTypeID,\n                            ProductTypeName: product.ProductTypeName,\n                            CategoryName: product.CategoryName || \"Uncategorized\",\n                            Rating: Number.parseFloat(product.Rating) || 0,\n                            StockQuantity: Number.parseInt(product.StockQuantity, 10) || 0,\n                            ProductImageUrl: imageUrl,\n                            IsDiscountAllowed: Boolean(product.IsDiscountAllowed),\n                            MarkAsNew: Boolean(product.MarkAsNew),\n                            SellStartDatetimeUTC: product.SellStartDatetimeUTC || undefined,\n                            SellEndDatetimeUTC: product.SellEndDatetimeUTC || undefined,\n                            Attributes: product.Attributes || [],\n                            ...product.DiscountPrice && {\n                                DiscountPrice: Number.parseFloat(product.DiscountPrice)\n                            }\n                        };\n                        return normalizedProduct;\n                    });\n                    console.log(\"Processed products:\", processedProducts);\n                    setAllProducts(processedProducts);\n                    setProducts(processedProducts);\n                    buildAttributeFilters(processedProducts);\n                    if (productsData.length > 0 && (productsData[0].TotalRecords || productsData[0].totalRecords)) {\n                        const totalRecords = productsData[0].TotalRecords || productsData[0].totalRecords;\n                        setTotalPages(Math.ceil(Number.parseInt(totalRecords, 10) / pageSize));\n                    }\n                } catch (parseError) {\n                    console.error(\"Error parsing product data:\", parseError);\n                    console.error(\"Raw response data:\", data);\n                    const err = parseError;\n                    setApiError(\"Error parsing data: \".concat(err.message || \"Unknown\"));\n                    throw parseError;\n                }\n            } else {\n                console.warn(\"No data field in API response:\", data);\n                setApiError(\"API response missing data field\");\n                setProducts([]);\n                setAllProducts([]);\n            }\n        } catch (error) {\n            console.error(\"Error fetching products:\", error);\n            const err = error;\n            setApiError(\"API Error: \".concat(err.message || \"Unknown\"));\n            toast({\n                description: \"Failed to load products. Please try again.\",\n                type: \"error\"\n            });\n            // Don't use sample data - just show empty state\n            setProducts([]);\n            setAllProducts([]);\n        } finally{\n            setFiltersLoading(false);\n            setLoading(false);\n        }\n    };\n    const fetchCategories = async ()=>{\n        try {\n            const requestData = {\n                requestParameters: {\n                    recordValueJson: \"[]\"\n                }\n            };\n            // Use Next.js API route instead of direct external API call\n            const response = await fetch(\"/api/categories\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(requestData)\n            });\n            if (!response.ok) {\n                throw new Error(\"HTTP error! status: \".concat(response.status));\n            }\n            const data = await response.json();\n            if (data && data.data) {\n                const parsedData = JSON.parse(data.data);\n                if (Array.isArray(parsedData)) {\n                    const formattedCategories = parsedData.map((cat)=>({\n                            id: cat.CategoryID,\n                            name: cat.Name\n                        }));\n                    setCategories(formattedCategories);\n                }\n            }\n        } catch (error) {\n            console.error(\"Error fetching categories:\", error);\n            // Set some default categories as fallback\n            setCategories([\n                {\n                    id: 1063,\n                    name: \"Surgery and its subspecialties\"\n                },\n                {\n                    id: 1026,\n                    name: \"Nursing\"\n                },\n                {\n                    id: 1025,\n                    name: \"Dentistry\"\n                },\n                {\n                    id: 1043,\n                    name: \"Internal medicine\"\n                },\n                {\n                    id: 1024,\n                    name: \"Pharmacology\"\n                },\n                {\n                    id: 1058,\n                    name: \"Radiology\"\n                }\n            ]);\n        }\n    };\n    const fetchProductTypes = async ()=>{\n        try {\n            const requestData = {\n                requestParameters: {\n                    recordValueJson: \"[]\"\n                }\n            };\n            const response = await fetch(\"/api/product-types\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(requestData)\n            });\n            if (!response.ok) {\n                throw new Error(\"HTTP error! status: \".concat(response.status));\n            }\n            const data = await response.json();\n            if (data && data.data) {\n                const parsedData = JSON.parse(data.data);\n                if (Array.isArray(parsedData)) {\n                    setProductTypes(parsedData);\n                }\n            }\n        } catch (error) {\n            console.error(\"Error fetching product types:\", error);\n            // Set fallback product types based on your database\n            setProductTypes([\n                {\n                    producttypeID: 1,\n                    Name: \"Courses\"\n                },\n                {\n                    producttypeID: 2,\n                    Name: \"Books\"\n                },\n                {\n                    producttypeID: 3,\n                    Name: \"Journals\"\n                },\n                {\n                    producttypeID: 4,\n                    Name: \"Medical Apps\"\n                }\n            ]);\n        }\n    };\n    const updateURLParams = ()=>{\n        const params = new URLSearchParams();\n        if (searchTerm) params.append(\"search\", searchTerm);\n        if (categoryFilter !== null) params.append(\"category\", categoryFilter.toString());\n        if (productTypeFilter !== null) params.append(\"productType\", productTypeFilter.toString());\n        window.history.pushState({}, \"\", \"\".concat(window.location.pathname, \"?\").concat(params.toString()));\n    };\n    const handleAttributeChange = (attributeName, valueId, checked)=>{\n        setSelectedAttributes((prev)=>{\n            const current = prev[attributeName] || [];\n            if (checked) {\n                return {\n                    ...prev,\n                    [attributeName]: [\n                        ...current,\n                        valueId\n                    ]\n                };\n            } else {\n                return {\n                    ...prev,\n                    [attributeName]: current.filter((id)=>id !== valueId)\n                };\n            }\n        });\n    };\n    const toggleAttributeExpansion = (attributeName)=>{\n        setExpandedAttributes((prev)=>({\n                ...prev,\n                [attributeName]: !prev[attributeName]\n            }));\n    };\n    const handlePageChange = (page)=>{\n        setCurrentPage(page);\n        window.scrollTo(0, 0);\n    };\n    const renderPagination = ()=>{\n        const pages = [];\n        const maxVisiblePages = 5;\n        let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));\n        const endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);\n        if (endPage - startPage + 1 < maxVisiblePages) {\n            startPage = Math.max(1, endPage - maxVisiblePages + 1);\n        }\n        for(let i = startPage; i <= endPage; i++){\n            pages.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_pagination__WEBPACK_IMPORTED_MODULE_8__.PaginationItem, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_pagination__WEBPACK_IMPORTED_MODULE_8__.PaginationLink, {\n                    onClick: ()=>handlePageChange(i),\n                    isActive: currentPage === i,\n                    children: i\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n                    lineNumber: 699,\n                    columnNumber: 11\n                }, this)\n            }, i, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n                lineNumber: 698,\n                columnNumber: 9\n            }, this));\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_pagination__WEBPACK_IMPORTED_MODULE_8__.Pagination, {\n            className: \"mt-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_pagination__WEBPACK_IMPORTED_MODULE_8__.PaginationContent, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_pagination__WEBPACK_IMPORTED_MODULE_8__.PaginationItem, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_pagination__WEBPACK_IMPORTED_MODULE_8__.PaginationPrevious, {\n                            onClick: ()=>currentPage > 1 && handlePageChange(currentPage - 1),\n                            className: currentPage === 1 ? \"pointer-events-none opacity-50\" : \"cursor-pointer\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n                            lineNumber: 713,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n                        lineNumber: 712,\n                        columnNumber: 11\n                    }, this),\n                    startPage > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_pagination__WEBPACK_IMPORTED_MODULE_8__.PaginationItem, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_pagination__WEBPACK_IMPORTED_MODULE_8__.PaginationLink, {\n                                    onClick: ()=>handlePageChange(1),\n                                    children: \"1\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n                                    lineNumber: 728,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n                                lineNumber: 727,\n                                columnNumber: 15\n                            }, this),\n                            startPage > 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_pagination__WEBPACK_IMPORTED_MODULE_8__.PaginationEllipsis, {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n                                lineNumber: 732,\n                                columnNumber: 33\n                            }, this)\n                        ]\n                    }, void 0, true),\n                    pages,\n                    endPage < totalPages && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            endPage < totalPages - 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_pagination__WEBPACK_IMPORTED_MODULE_8__.PaginationEllipsis, {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n                                lineNumber: 740,\n                                columnNumber: 44\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_pagination__WEBPACK_IMPORTED_MODULE_8__.PaginationItem, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_pagination__WEBPACK_IMPORTED_MODULE_8__.PaginationLink, {\n                                    onClick: ()=>handlePageChange(totalPages),\n                                    children: totalPages\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n                                    lineNumber: 742,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n                                lineNumber: 741,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_pagination__WEBPACK_IMPORTED_MODULE_8__.PaginationItem, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_pagination__WEBPACK_IMPORTED_MODULE_8__.PaginationNext, {\n                            onClick: ()=>currentPage < totalPages && handlePageChange(currentPage + 1),\n                            className: currentPage === totalPages ? \"pointer-events-none opacity-50\" : \"cursor-pointer\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n                            lineNumber: 750,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n                        lineNumber: 749,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n                lineNumber: 711,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n            lineNumber: 710,\n            columnNumber: 7\n        }, this);\n    };\n    const renderProductSkeleton = ()=>{\n        return Array(pageSize).fill(0).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"aspect-square\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_4__.Skeleton, {\n                            className: \"h-full w-full\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n                            lineNumber: 775,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n                        lineNumber: 774,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_4__.Skeleton, {\n                                className: \"h-4 w-full\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n                                lineNumber: 778,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_4__.Skeleton, {\n                                className: \"h-4 w-3/4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n                                lineNumber: 779,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_4__.Skeleton, {\n                                className: \"h-6 w-1/3\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n                                lineNumber: 780,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n                        lineNumber: 777,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 pt-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex w-full gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_4__.Skeleton, {\n                                    className: \"h-10 flex-1\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n                                    lineNumber: 784,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_4__.Skeleton, {\n                                    className: \"h-10 w-10\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n                                    lineNumber: 785,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n                            lineNumber: 783,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n                        lineNumber: 782,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, \"skeleton-\".concat(index), true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n                lineNumber: 770,\n                columnNumber: 9\n            }, this));\n    };\n    // Get current page products\n    // Sort helper\n    const sortProductsList = (list, order)=>{\n        const sorted = [\n            ...list\n        ];\n        switch(order){\n            case \"Newest\":\n                return sorted.sort((a, b)=>b.ProductId - a.ProductId); // Sort by ID descending (newest first)\n            case \"ProductName ASC\":\n                return sorted.sort((a, b)=>a.ProductName.localeCompare(b.ProductName));\n            case \"ProductName DESC\":\n                return sorted.sort((a, b)=>b.ProductName.localeCompare(a.ProductName));\n            case \"Price ASC\":\n                return sorted.sort((a, b)=>a.Price - b.Price);\n            case \"Price DESC\":\n                return sorted.sort((a, b)=>b.Price - a.Price);\n            case \"Rating DESC\":\n                return sorted.sort((a, b)=>b.Rating - a.Rating);\n            default:\n                return sorted.sort((a, b)=>b.ProductId - a.ProductId); // Default to newest\n        }\n    };\n    const getCurrentPageProducts = ()=>{\n        const sorted = sortProductsList(products, sortOrder);\n        const startIndex = (currentPage - 1) * pageSize;\n        const endIndex = startIndex + pageSize;\n        return sorted.slice(startIndex, endIndex);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto px-4 py-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: \"text-3xl font-bold mb-6\",\n                children: categoryFilter !== null && categories.length > 0 ? \"\".concat(((_categories_find = categories.find((c)=>c.id === categoryFilter)) === null || _categories_find === void 0 ? void 0 : _categories_find.name) || \"Category\", \" Products\") : \"All Products\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n                lineNumber: 827,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white p-6 rounded-lg shadow mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center gap-2 mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Filter_Package_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"h-5 w-5 text-gray-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n                                    lineNumber: 840,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-medium text-gray-700\",\n                                    children: \"Filter by Product Type\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n                                    lineNumber: 841,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n                            lineNumber: 839,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center gap-3 flex-wrap\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: productTypeFilter === null ? \"default\" : \"outline\",\n                                    size: \"lg\",\n                                    onClick: ()=>{\n                                        setProductTypeFilter(null);\n                                        setCurrentPage(1);\n                                        updateURLParams();\n                                    },\n                                    className: \"min-w-[120px]\",\n                                    children: \"All Types\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n                                    lineNumber: 846,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: productTypeFilter === 1 ? \"default\" : \"outline\",\n                                    size: \"lg\",\n                                    onClick: ()=>{\n                                        setProductTypeFilter(1);\n                                        setCurrentPage(1);\n                                        updateURLParams();\n                                    },\n                                    className: \"min-w-[120px]\",\n                                    children: \"Courses\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n                                    lineNumber: 858,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: productTypeFilter === 2 ? \"default\" : \"outline\",\n                                    size: \"lg\",\n                                    onClick: ()=>{\n                                        setProductTypeFilter(2);\n                                        setCurrentPage(1);\n                                        updateURLParams();\n                                    },\n                                    className: \"min-w-[120px]\",\n                                    children: \"Books\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n                                    lineNumber: 870,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: productTypeFilter === 3 ? \"default\" : \"outline\",\n                                    size: \"lg\",\n                                    onClick: ()=>{\n                                        setProductTypeFilter(3);\n                                        setCurrentPage(1);\n                                        updateURLParams();\n                                    },\n                                    className: \"min-w-[120px]\",\n                                    children: \"Journals\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n                                    lineNumber: 882,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: productTypeFilter === 4 ? \"default\" : \"outline\",\n                                    size: \"lg\",\n                                    onClick: ()=>{\n                                        setProductTypeFilter(4);\n                                        setCurrentPage(1);\n                                        updateURLParams();\n                                    },\n                                    className: \"min-w-[120px]\",\n                                    children: \"Medical Apps\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n                                    lineNumber: 894,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n                            lineNumber: 845,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n                    lineNumber: 838,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n                lineNumber: 837,\n                columnNumber: 7\n            }, this),\n            apiError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-yellow-50 border border-yellow-200 text-yellow-700 p-4 mb-6 rounded-md\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Filter_Package_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: \"h-5 w-5 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n                                lineNumber: 914,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"font-semibold\",\n                                children: \"API Notice\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n                                lineNumber: 915,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n                        lineNumber: 913,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm\",\n                        children: apiError\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n                        lineNumber: 917,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-3 flex gap-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            size: \"sm\",\n                            onClick: ()=>fetchProducts(),\n                            children: \"Retry API Call\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n                            lineNumber: 919,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n                        lineNumber: 918,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n                lineNumber: 912,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: [\n                    searchTerm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-lg\",\n                        children: [\n                            \"Search results for:\",\n                            \" \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-semibold\",\n                                children: [\n                                    '\"',\n                                    searchTerm,\n                                    '\"'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n                                lineNumber: 930,\n                                columnNumber: 13\n                            }, this),\n                            categoryFilter !== null && categories.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    \" \",\n                                    \"in\",\n                                    \" \",\n                                    ((_categories_find1 = categories.find((c)=>c.id === categoryFilter)) === null || _categories_find1 === void 0 ? void 0 : _categories_find1.name) || \"selected category\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n                                lineNumber: 932,\n                                columnNumber: 15\n                            }, this),\n                            productTypeFilter !== null && productTypes.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    \" \",\n                                    \"-\",\n                                    \" \",\n                                    ((_productTypes_find = productTypes.find((t)=>t.producttypeID === productTypeFilter)) === null || _productTypes_find === void 0 ? void 0 : _productTypes_find.Name) || \"selected type\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n                                lineNumber: 940,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n                        lineNumber: 928,\n                        columnNumber: 11\n                    }, this),\n                    !searchTerm && (categoryFilter !== null || productTypeFilter !== null) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-lg\",\n                        children: [\n                            \"Browsing:\",\n                            categoryFilter !== null && categories.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-semibold ml-1\",\n                                children: ((_categories_find2 = categories.find((c)=>c.id === categoryFilter)) === null || _categories_find2 === void 0 ? void 0 : _categories_find2.name) || \"selected category\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n                                lineNumber: 954,\n                                columnNumber: 17\n                            }, this),\n                            productTypeFilter !== null && productTypes.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-semibold ml-1\",\n                                children: [\n                                    categoryFilter !== null ? \" - \" : \" \",\n                                    ((_productTypes_find1 = productTypes.find((t)=>t.producttypeID === productTypeFilter)) === null || _productTypes_find1 === void 0 ? void 0 : _productTypes_find1.Name) || \"selected type\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n                                lineNumber: 960,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n                        lineNumber: 951,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n                lineNumber: 926,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col lg:flex-row gap-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:w-1/4 space-y-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white p-4 rounded-lg shadow\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-lg font-semibold mb-4 flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Filter_Package_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"mr-2 h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n                                            lineNumber: 975,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Filters\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n                                    lineNumber: 974,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-medium mb-2\",\n                                                    children: \"Category\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n                                                    lineNumber: 982,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                                    value: (categoryFilter === null || categoryFilter === void 0 ? void 0 : categoryFilter.toString()) || \"all\",\n                                                    onValueChange: (value)=>{\n                                                        const newCategoryFilter = value === \"all\" ? null : Number(value);\n                                                        setCategoryFilter(newCategoryFilter);\n                                                        setCurrentPage(1);\n                                                        updateURLParams();\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectTrigger, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectValue, {\n                                                                placeholder: \"All Categories\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n                                                                lineNumber: 994,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n                                                            lineNumber: 993,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectContent, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                    value: \"all\",\n                                                                    children: \"All Categories\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n                                                                    lineNumber: 997,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                        value: category.id.toString(),\n                                                                        children: category.name\n                                                                    }, category.id, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n                                                                        lineNumber: 999,\n                                                                        columnNumber: 23\n                                                                    }, this))\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n                                                            lineNumber: 996,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n                                                    lineNumber: 983,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n                                            lineNumber: 981,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"hidden\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-medium mb-2\",\n                                                    children: \"Price Range\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n                                                    lineNumber: 1014,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"number\",\n                                                            placeholder: \"Min\",\n                                                            className: \"w-full p-2 border rounded\",\n                                                            onChange: (e)=>setPriceRange({\n                                                                    ...priceRange,\n                                                                    min: e.target.value ? Number.parseFloat(e.target.value) : null\n                                                                })\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n                                                            lineNumber: 1016,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"-\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n                                                            lineNumber: 1029,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"number\",\n                                                            placeholder: \"Max\",\n                                                            className: \"w-full p-2 border rounded\",\n                                                            onChange: (e)=>setPriceRange({\n                                                                    ...priceRange,\n                                                                    max: e.target.value ? Number.parseFloat(e.target.value) : null\n                                                                })\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n                                                            lineNumber: 1030,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n                                                    lineNumber: 1015,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n                                            lineNumber: 1013,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-medium mb-2\",\n                                                    children: \"Sort By\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n                                                    lineNumber: 1047,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                                    value: sortOrder,\n                                                    onValueChange: setSortOrder,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectTrigger, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectValue, {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n                                                                lineNumber: 1050,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n                                                            lineNumber: 1049,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectContent, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                    value: \"Newest\",\n                                                                    children: \"Newest\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n                                                                    lineNumber: 1053,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                    value: \"ProductName ASC\",\n                                                                    children: \"Name: A to Z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n                                                                    lineNumber: 1054,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                    value: \"ProductName DESC\",\n                                                                    children: \"Name: Z to A\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n                                                                    lineNumber: 1057,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                    value: \"Rating DESC\",\n                                                                    children: \"Rating: High to Low\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n                                                                    lineNumber: 1060,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n                                                            lineNumber: 1052,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n                                                    lineNumber: 1048,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n                                            lineNumber: 1046,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            className: \"w-full\",\n                                            onClick: ()=>{\n                                                setCategoryFilter(null);\n                                                setProductTypeFilter(null);\n                                                setPriceRange({\n                                                    min: null,\n                                                    max: null\n                                                });\n                                                setSortOrder(\"Newest\");\n                                                setCurrentPage(1);\n                                                setSearchTerm(\"\");\n                                                setSearchInput(\"\");\n                                                setSelectedAttributes({});\n                                                setExpandedAttributes({});\n                                                window.history.pushState({}, \"\", window.location.pathname);\n                                            },\n                                            disabled: filtersLoading,\n                                            children: \"Reset Filters\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n                                            lineNumber: 1067,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n                                    lineNumber: 978,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n                            lineNumber: 973,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n                        lineNumber: 972,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:w-3/4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 gap-4 sm:gap-6 sm:grid-cols-2 lg:grid-cols-4\",\n                                children: loading ? renderProductSkeleton() : getCurrentPageProducts().map((product)=>{\n                                    if (!product.ProductId) {\n                                        return null;\n                                    }\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_product_card__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        product: product\n                                    }, product.ProductId, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n                                        lineNumber: 1098,\n                                        columnNumber: 21\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n                                lineNumber: 1090,\n                                columnNumber: 11\n                            }, this),\n                            !loading && products.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-semibold mb-2\",\n                                        children: apiError ? \"Failed to load products\" : \"No products found\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n                                        lineNumber: 1105,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-500\",\n                                        children: apiError ? \"Please check your connection and try again\" : \"Try adjusting your filters or search criteria\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n                                        lineNumber: 1108,\n                                        columnNumber: 15\n                                    }, this),\n                                    apiError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        className: \"mt-4\",\n                                        onClick: ()=>fetchProducts(),\n                                        children: \"Retry\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n                                        lineNumber: 1114,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n                                lineNumber: 1104,\n                                columnNumber: 13\n                            }, this),\n                            !loading && products.length > 0 && renderPagination()\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n                        lineNumber: 1089,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n                lineNumber: 971,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\",\n        lineNumber: 826,\n        columnNumber: 5\n    }, this);\n}\n_s1(ProductsContent, \"TY5V9RYT62rdE3iLYYxczb4Ipxc=\", false, function() {\n    return [\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast\n    ];\n});\n_c3 = ProductsContent;\nvar _c, _c1, _c2, _c3;\n$RefreshReg$(_c, \"SearchParamsWrapper\");\n$RefreshReg$(_c1, \"ProductsPage\");\n$RefreshReg$(_c2, \"ProductsPageSkeleton\");\n$RefreshReg$(_c3, \"ProductsContent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/products/page.tsx\n"));

/***/ })

});