!function(r){"use strict";function e(r,a){var o=a.shift(),l=a;if(null!==r){if(0===l.length)return r[o];if("object"==typeof r)return e(r[o],l)}return r}function a(){if(!r.trumbowyg.addedXhrProgressEvent){var e=r.ajaxSettings.xhr;r.ajaxSetup({xhr:function(){var r=e(),a=this;return r&&"object"==typeof r.upload&&void 0!==a.progressUpload&&r.upload.addEventListener("progress",function(r){a.progressUpload(r)},!1),r}}),r.trumbowyg.addedXhrProgressEvent=!0}}var o={serverPath:"",fileFieldName:"fileToUpload",data:[],headers:{},xhrFields:{},urlPropertyName:"file",statusPropertyName:"success",success:void 0,error:void 0,imageWidthModalEdit:!1};a(),r.extend(!0,r.trumbowyg,{langs:{en:{upload:"Upload",file:"File",uploadError:"Error"},da:{upload:"Upload",file:"Fil",uploadError:"Fejl"},de:{upload:"Hochladen",file:"Datei",uploadError:"Fehler"},sk:{upload:"Nahrať",file:"Súbor",uploadError:"Chyba"},fr:{upload:"Envoi",file:"Fichier",uploadError:"Erreur"},cs:{upload:"Nahrát obrázek",file:"Soubor",uploadError:"Chyba"},zh_cn:{upload:"上传",file:"文件",uploadError:"错误"},zh_tw:{upload:"上傳",file:"文件",uploadError:"錯誤"},ru:{upload:"Загрузка",file:"Файл",uploadError:"Ошибка"},ja:{upload:"アップロード",file:"ファイル",uploadError:"エラー"},pt_br:{upload:"Enviar do local",file:"Arquivo",uploadError:"Erro"},tr:{upload:"Yükle",file:"Dosya",uploadError:"Hata"}},plugins:{upload:{init:function(a){a.o.plugins.upload=r.extend(!0,{},o,a.o.plugins.upload||{});var l={fn:function(){a.saveRange();var o,l=a.o.prefix,t={file:{type:"file",required:!0,attributes:{accept:"image/*"}},alt:{label:"description",value:a.getRangeText()}};a.o.plugins.upload.imageWidthModalEdit&&(t.width={value:""});var d=a.openModalInsert(a.lang.upload,t,function(t){var i=new FormData;i.append(a.o.plugins.upload.fileFieldName,o),a.o.plugins.upload.data.map(function(r){i.append(r.name,r.value)}),r.map(t,function(r,e){"file"!==e&&i.append(e,r)}),0===r("."+l+"progress",d).length&&r("."+l+"modal-title",d).after(r("<div/>",{"class":l+"progress"}).append(r("<div/>",{"class":l+"progress-bar"}))),r.ajax({url:a.o.plugins.upload.serverPath,headers:a.o.plugins.upload.headers,xhrFields:a.o.plugins.upload.xhrFields,type:"POST",data:i,cache:!1,dataType:"json",processData:!1,contentType:!1,progressUpload:function(e){r("."+l+"progress-bar").css("width",Math.round(100*e.loaded/e.total)+"%")},success:function(o){if(a.o.plugins.upload.success)a.o.plugins.upload.success(o,a,d,t);else if(e(o,a.o.plugins.upload.statusPropertyName.split("."))){var l=e(o,a.o.plugins.upload.urlPropertyName.split("."));a.execCmd("insertImage",l,!1,!0);var i=r('img[src="'+l+'"]:not([alt])',a.$box);i.attr("alt",t.alt),a.o.imageWidthModalEdit&&parseInt(t.width)>0&&i.attr({width:t.width}),setTimeout(function(){a.closeModal()},250),a.$c.trigger("tbwuploadsuccess",[a,o,l])}else a.addErrorOnModalField(r("input[type=file]",d),a.lang[o.message]),a.$c.trigger("tbwuploaderror",[a,o])},error:a.o.plugins.upload.error||function(){a.addErrorOnModalField(r("input[type=file]",d),a.lang.uploadError),a.$c.trigger("tbwuploaderror",[a])}})});r("input[type=file]").on("change",function(r){try{o=r.target.files[0]}catch(e){o=r.target.value}})}};a.addBtnDef("upload",l)}}}})}(jQuery);