/*
 * Globalize Culture rw-RW
 *
 * http://github.com/jquery/globalize
 *
 * Copyright Software Freedom Conservancy, Inc.
 * Dual licensed under the MIT or GPL Version 2 licenses.
 * http://jquery.org/license
 *
 * This file was generated by the Globalize Culture Generator
 * Translation: bugs found in this file need to be fixed in the generator
 */

(function( window, undefined ) {

var Globalize;

if ( typeof require !== "undefined" &&
	typeof exports !== "undefined" &&
	typeof module !== "undefined" ) {
	// Assume CommonJS
	Globalize = require( "globalize" );
} else {
	// Global variable
	Globalize = window.Globalize;
}

Globalize.addCultureInfo( "rw-RW", "default", {
	name: "rw-RW",
	englishName: "Kinyarwanda (Rwanda)",
	nativeName: "Kinyarwanda (Rwanda)",
	language: "rw",
	numberFormat: {
		",": " ",
		".": ",",
		percent: {
			",": " ",
			".": ","
		},
		currency: {
			pattern: ["$-n","$ n"],
			",": " ",
			".": ",",
			symbol: "RWF"
		}
	},
	calendars: {
		standard: {
			days: {
				names: ["<PERSON> wa mbere","<PERSON> wa kabiri","<PERSON> wa gatatu","<PERSON> wa kane","Ku wa gatanu","Ku wa gatandatu","Ku cyumweru"],
				namesAbbr: ["mbe.","kab.","gat.","kan.","gat.","gat.","cyu."],
				namesShort: ["mb","ka","ga","ka","ga","ga","cy"]
			},
			months: {
				names: ["Mutarama","Gashyantare","Werurwe","Mata","Gicurasi","Kamena","Nyakanga","Kanama","Nzeli","Ukwakira","Ugushyingo","Ukuboza",""],
				namesAbbr: ["Mut","Gas","Wer","Mat","Gic","Kam","Nya","Kan","Nze","Ukwa","Ugu","Uku",""]
			},
			AM: ["saa moya z.m.","saa moya z.m.","SAA MOYA Z.M."],
			PM: ["saa moya z.n.","saa moya z.n.","SAA MOYA Z.N."],
			eras: [{"name":"AD","start":null,"offset":0}]
		}
	}
});

}( this ));
