/*
 * Globalize Culture ha
 *
 * http://github.com/jquery/globalize
 *
 * Copyright Software Freedom Conservancy, Inc.
 * Dual licensed under the MIT or GPL Version 2 licenses.
 * http://jquery.org/license
 *
 * This file was generated by the Globalize Culture Generator
 * Translation: bugs found in this file need to be fixed in the generator
 */

(function( window, undefined ) {

var Globalize;

if ( typeof require !== "undefined" &&
	typeof exports !== "undefined" &&
	typeof module !== "undefined" ) {
	// Assume CommonJS
	Globalize = require( "globalize" );
} else {
	// Global variable
	Globalize = window.Globalize;
}

Globalize.addCultureInfo( "ha", "default", {
	name: "ha",
	englishName: "Hausa",
	nativeName: "Hausa",
	language: "ha",
	numberFormat: {
		currency: {
			pattern: ["$-n","$ n"],
			symbol: "N"
		}
	},
	calendars: {
		standard: {
			days: {
				names: ["<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON>"],
				namesAbbr: ["<PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON>","<PERSON>h","Ju<PERSON>","<PERSON><PERSON>"],
				namesShort: ["L","L","T","L","A","J","A"]
			},
			months: {
				names: ["<PERSON>uwaru","Febreru","<PERSON>","Afrilu","Mayu","<PERSON>i","<PERSON>li","Agusta","Satumba","Oktocba","Nuwamba","<PERSON><PERSON>mba",""],
				names<PERSON>bbr: ["<PERSON>","<PERSON>","<PERSON>","Afr","May","<PERSON>","Yul","Agu","Sat","Okt","Nuw","Dis",""]
			},
			AM: ["Safe","safe","SAFE"],
			PM: ["Yamma","yamma","YAMMA"],
			eras: [{"name":"AD","start":null,"offset":0}],
			patterns: {
				d: "d/M/yyyy"
			}
		}
	}
});

}( this ));
