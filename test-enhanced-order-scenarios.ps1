# Enhanced Order Processing Test Script
# Tests all four scenarios for points and coupons integration

param(
    [string]$BackendUrl = "https://admin.codemedicalapps.com",
    [int]$TestUserId = 1,
    [string]$TestCouponCode = "A9804F",
    [string]$JwtToken = ""
)

# Sample cart data
$sampleCartData = @(
    @{
        ProductId = 15
        ProductName = "Test Product 1"
        Price = 50.00
        Quantity = 1
        ItemPriceTotal = 50.00
        OrderItemDiscountTotal = 0
        ShippingChargesTotal = 0
        OrderItemAttributeChargesTotal = 0
        ItemSubTotal = 50.00
        ProductAllSelectedAttributes = "[]"
    },
    @{
        ProductId = 16
        ProductName = "Test Product 2"
        Price = 30.00
        Quantity = 2
        ItemPriceTotal = 60.00
        OrderItemDiscountTotal = 0
        ShippingChargesTotal = 0
        OrderItemAttributeChargesTotal = 0
        ItemSubTotal = 60.00
        ProductAllSelectedAttributes = "[]"
    }
)

# Test scenarios
$testScenarios = @(
    @{
        Name = "Scenario 1: Normal order without points and coupon"
        OrderData = @{
            OrderNote = "Test order - Scenario 1"
            cartJsonData = ($sampleCartData | ConvertTo-Json -Depth 10 -Compress)
            OrderTotal = 110.00
            CouponCode = ""
            Description = "Test order without points and coupon"
            StripeStatus = ""
            StripeResponseJson = ""
            StripeBalanceTransactionId = ""
            StripeChargeId = ""
            PayPalResponseJson = ""
            CurrencyCode = "USD"
            PaymentMethod = 6
            Point = $null
            addressid = 1
        }
    },
    @{
        Name = "Scenario 2: Normal order with points only"
        OrderData = @{
            OrderNote = "Test order - Scenario 2"
            cartJsonData = ($sampleCartData | ConvertTo-Json -Depth 10 -Compress)
            OrderTotal = 100.00
            CouponCode = ""
            Description = "Test order with points only"
            StripeStatus = ""
            StripeResponseJson = ""
            StripeBalanceTransactionId = ""
            StripeChargeId = ""
            PayPalResponseJson = ""
            CurrencyCode = "USD"
            PaymentMethod = 6
            Point = 10
            addressid = 1
        }
    },
    @{
        Name = "Scenario 3: Normal order with coupon only"
        OrderData = @{
            OrderNote = "Test order - Scenario 3"
            cartJsonData = ($sampleCartData | ConvertTo-Json -Depth 10 -Compress)
            OrderTotal = 99.00
            CouponCode = $TestCouponCode
            Description = "Test order with coupon only"
            StripeStatus = ""
            StripeResponseJson = ""
            StripeBalanceTransactionId = ""
            StripeChargeId = ""
            PayPalResponseJson = ""
            CurrencyCode = "USD"
            PaymentMethod = 6
            Point = $null
            addressid = 1
        }
    },
    @{
        Name = "Scenario 4: Normal order with both points and coupon"
        OrderData = @{
            OrderNote = "Test order - Scenario 4"
            cartJsonData = ($sampleCartData | ConvertTo-Json -Depth 10 -Compress)
            OrderTotal = 89.00
            CouponCode = $TestCouponCode
            Description = "Test order with both points and coupon"
            StripeStatus = ""
            StripeResponseJson = ""
            StripeBalanceTransactionId = ""
            StripeChargeId = ""
            PayPalResponseJson = ""
            CurrencyCode = "USD"
            PaymentMethod = 6
            Point = 10
            addressid = 1
        }
    }
)

function Test-OrderScenario {
    param(
        [hashtable]$Scenario,
        [string]$BaseUrl,
        [string]$Token
    )
    
    Write-Host "`n🧪 Testing: $($Scenario.Name)" -ForegroundColor Green
    Write-Host ("=" * 60) -ForegroundColor Gray
    
    try {
        # Prepare headers
        $headers = @{
            "Content-Type" = "application/json"
            "Accept" = "application/json"
        }
        
        if ($Token -and $Token -ne "") {
            $headers["Authorization"] = "Bearer $Token"
        }
        
        # Convert order data to JSON
        $jsonBody = $Scenario.OrderData | ConvertTo-Json -Depth 10
        
        Write-Host "📤 Request Data:" -ForegroundColor Yellow
        Write-Host $jsonBody
        
        # Make API call
        $response = Invoke-RestMethod -Uri "$BaseUrl/api/v1/common/post-order-direct" `
                                    -Method Post `
                                    -ContentType "application/json" `
                                    -Headers $headers `
                                    -Body $jsonBody `
                                    -ErrorAction Stop
        
        Write-Host "✅ Response Status: Success" -ForegroundColor Green
        Write-Host "📥 Response Data:" -ForegroundColor Yellow
        $response | ConvertTo-Json -Depth 10
        
        if ($response.statusCode -eq 200) {
            Write-Host "🎉 Order placed successfully!" -ForegroundColor Green
            if ($response.data) {
                Write-Host "📋 Order ID: $($response.data.OrderID)" -ForegroundColor Cyan
                Write-Host "📋 Order Number: $($response.data.OrderNumber)" -ForegroundColor Cyan
            }
            return @{ Success = $true; Data = $response }
        } else {
            Write-Host "❌ Order failed: $($response.errorMessage)" -ForegroundColor Red
            return @{ Success = $false; Data = $response }
        }
        
    } catch {
        Write-Host "❌ Error: $($_.Exception.Message)" -ForegroundColor Red
        if ($_.Exception.Response) {
            $errorResponse = $_.Exception.Response.GetResponseStream()
            $reader = New-Object System.IO.StreamReader($errorResponse)
            $errorContent = $reader.ReadToEnd()
            Write-Host "Error Details: $errorContent" -ForegroundColor Red
        }
        return @{ Success = $false; Error = $_.Exception.Message }
    }
}

function Start-AllTests {
    Write-Host "🚀 Starting Enhanced Order Processing Tests" -ForegroundColor Cyan
    Write-Host "Backend URL: $BackendUrl" -ForegroundColor Gray
    Write-Host "Test User ID: $TestUserId" -ForegroundColor Gray
    Write-Host "Test Coupon Code: $TestCouponCode" -ForegroundColor Gray
    
    $results = @()
    
    foreach ($scenario in $testScenarios) {
        $result = Test-OrderScenario -Scenario $scenario -BaseUrl $BackendUrl -Token $JwtToken
        $results += @{
            Scenario = $scenario.Name
            Success = $result.Success
            Data = $result.Data
            Error = $result.Error
        }
        
        # Wait between tests
        Start-Sleep -Seconds 2
    }
    
    # Summary
    Write-Host "`n📊 TEST SUMMARY" -ForegroundColor Cyan
    Write-Host ("=" * 60) -ForegroundColor Gray
    
    for ($i = 0; $i -lt $results.Count; $i++) {
        $result = $results[$i]
        $status = if ($result.Success) { "✅ PASSED" } else { "❌ FAILED" }
        $color = if ($result.Success) { "Green" } else { "Red" }
        Write-Host "$($i + 1). $($result.Scenario): $status" -ForegroundColor $color
    }
    
    $passedTests = ($results | Where-Object { $_.Success }).Count
    $totalTests = $results.Count
    
    Write-Host "`n🎯 Overall Result: $passedTests/$totalTests tests passed" -ForegroundColor Cyan
    
    if ($passedTests -eq $totalTests) {
        Write-Host "🎉 All tests passed! Enhanced order processing is working correctly." -ForegroundColor Green
    } else {
        Write-Host "⚠️  Some tests failed. Please review the implementation." -ForegroundColor Yellow
    }
}

# Run tests
Start-AllTests
