"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"136400ba59ad\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9nbG9iYWxzLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLElBQVUsSUFBSSxpQkFBaUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xceWh5YXNvZnRcXERvd25sb2Fkc1xcZWNcXC5ORVQgOCBWZXJzaW9uIC0gTGF0ZXN0XFxwcm9qZWN0XFxjb2RlbWVkaWNhbFxccHJvamVjdDNcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIxMzY0MDBiYTU5YWRcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/ui/header.tsx":
/*!**********************************!*\
  !*** ./components/ui/header.tsx ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Header: () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Globe,Heart,LogOut,Mail,MapPin,Menu,MessageCircle,Package,Phone,Search,ShoppingCart,User,UserPlus,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Globe,Heart,LogOut,Mail,MapPin,Menu,MessageCircle,Package,Phone,Search,ShoppingCart,User,UserPlus,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Globe,Heart,LogOut,Mail,MapPin,Menu,MessageCircle,Package,Phone,Search,ShoppingCart,User,UserPlus,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Globe,Heart,LogOut,Mail,MapPin,Menu,MessageCircle,Package,Phone,Search,ShoppingCart,User,UserPlus,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Globe,Heart,LogOut,Mail,MapPin,Menu,MessageCircle,Package,Phone,Search,ShoppingCart,User,UserPlus,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Globe,Heart,LogOut,Mail,MapPin,Menu,MessageCircle,Package,Phone,Search,ShoppingCart,User,UserPlus,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Globe,Heart,LogOut,Mail,MapPin,Menu,MessageCircle,Package,Phone,Search,ShoppingCart,User,UserPlus,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Globe,Heart,LogOut,Mail,MapPin,Menu,MessageCircle,Package,Phone,Search,ShoppingCart,User,UserPlus,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Globe,Heart,LogOut,Mail,MapPin,Menu,MessageCircle,Package,Phone,Search,ShoppingCart,User,UserPlus,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Globe,Heart,LogOut,Mail,MapPin,Menu,MessageCircle,Package,Phone,Search,ShoppingCart,User,UserPlus,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Globe,Heart,LogOut,Mail,MapPin,Menu,MessageCircle,Package,Phone,Search,ShoppingCart,User,UserPlus,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-plus.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Globe,Heart,LogOut,Mail,MapPin,Menu,MessageCircle,Package,Phone,Search,ShoppingCart,User,UserPlus,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Globe,Heart,LogOut,Mail,MapPin,Menu,MessageCircle,Package,Phone,Search,ShoppingCart,User,UserPlus,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shopping-cart.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Globe,Heart,LogOut,Mail,MapPin,Menu,MessageCircle,Package,Phone,Search,ShoppingCart,User,UserPlus,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Globe,Heart,LogOut,Mail,MapPin,Menu,MessageCircle,Package,Phone,Search,ShoppingCart,User,UserPlus,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _contexts_cart_context__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/cart-context */ \"(app-pages-browser)/./contexts/cart-context.tsx\");\n/* harmony import */ var _contexts_wishlist_context__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/wishlist-context */ \"(app-pages-browser)/./contexts/wishlist-context.tsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _lib_api_helper__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/api-helper */ \"(app-pages-browser)/./lib/api-helper.ts\");\n/* harmony import */ var _components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/navigation-menu */ \"(app-pages-browser)/./components/ui/navigation-menu.tsx\");\n/* harmony import */ var _components_ui_popover__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/popover */ \"(app-pages-browser)/./components/ui/popover.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./lib/utils.ts\");\n/* harmony import */ var _button__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _contexts_settings_context__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/contexts/settings-context */ \"(app-pages-browser)/./contexts/settings-context.tsx\");\n/* harmony import */ var _contexts_user_context__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/contexts/user-context */ \"(app-pages-browser)/./contexts/user-context.tsx\");\n/* harmony import */ var _color_picker__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./color-picker */ \"(app-pages-browser)/./components/ui/color-picker.tsx\");\n/* __next_internal_client_entry_do_not_use__ Header auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction Header() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.usePathname)();\n    const [categories, setCategories] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(true);\n    const [showColorPicker, setShowColorPicker] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);\n    const [selectedCategoryId, setSelectedCategoryId] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);\n    const [selectedSubcategory, setSelectedSubcategory] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);\n    const [showMobileMenu, setShowMobileMenu] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [showMobileCategories, setShowMobileCategories] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)('');\n    const [cartCount, setCartCount] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(0);\n    const [wishlistCount, setWishlistCount] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(0);\n    const cart = (0,_contexts_cart_context__WEBPACK_IMPORTED_MODULE_4__.useCart)();\n    const wishlist = (0,_contexts_wishlist_context__WEBPACK_IMPORTED_MODULE_5__.useWishlist)();\n    const { user, isLoggedIn, logout } = (0,_contexts_user_context__WEBPACK_IMPORTED_MODULE_13__.useUser)();\n    const { theme, language, primaryColor, primaryTextColor, toggleTheme, setLanguage, setPrimaryColor, t } = (0,_contexts_settings_context__WEBPACK_IMPORTED_MODULE_12__.useSettings)();\n    const handleSearch = ()=>{\n        // Check if we're already on the products page\n        const isOnProductsPage = pathname === '/products' || pathname.startsWith('/products?');\n        const params = new URLSearchParams();\n        if (searchTerm) {\n            params.append('search', searchTerm);\n        }\n        if (selectedCategoryId) {\n            params.append('category', selectedCategoryId.toString());\n        }\n        if (isOnProductsPage) {\n            // If on products page, update URL without navigation (stay on same page)\n            const newUrl = \"/products?\".concat(params.toString());\n            window.history.pushState({}, '', newUrl);\n            // Trigger a page refresh to update the search results\n            window.location.reload();\n        } else {\n            // If not on products page, navigate to products page\n            router.push(\"/products?\".concat(params.toString()));\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"Header.useEffect\": ()=>{\n            const fetchCategories = {\n                \"Header.useEffect.fetchCategories\": async ()=>{\n                    try {\n                        var _categoriesResponse_data, _categoriesResponse_data1;\n                        const param = {\n                            \"PageNumber\": 1,\n                            \"PageSize\": 100,\n                            \"SortColumn\": \"Name\",\n                            \"SortOrder\": \"ASC\"\n                        };\n                        const headers = {\n                            'Content-Type': 'application/json',\n                            'Accept': 'application/json',\n                            'Authorization': 'Bearer ' + localStorage.getItem('token')\n                        };\n                        const categoriesResponse = await (0,_lib_api_helper__WEBPACK_IMPORTED_MODULE_7__.MakeApiCallAsync)(_lib_api_helper__WEBPACK_IMPORTED_MODULE_7__.Config.END_POINT_NAMES.GET_CATEGORIES_LIST, null, param, headers, \"POST\", true);\n                        if (categoriesResponse === null || categoriesResponse === void 0 ? void 0 : (_categoriesResponse_data = categoriesResponse.data) === null || _categoriesResponse_data === void 0 ? void 0 : _categoriesResponse_data.data) {\n                            try {\n                                const parsedData = JSON.parse(categoriesResponse.data.data);\n                                if (Array.isArray(parsedData)) {\n                                    // Create a map of parent categories\n                                    const parentCategories = parsedData.filter({\n                                        \"Header.useEffect.fetchCategories.parentCategories\": (cat)=>!cat.ParentCategoryID\n                                    }[\"Header.useEffect.fetchCategories.parentCategories\"]);\n                                    const childCategories = parsedData.filter({\n                                        \"Header.useEffect.fetchCategories.childCategories\": (cat)=>cat.ParentCategoryID\n                                    }[\"Header.useEffect.fetchCategories.childCategories\"]);\n                                    // Format parent categories with their children\n                                    const formattedCategories = parentCategories.map({\n                                        \"Header.useEffect.fetchCategories.formattedCategories\": (parent)=>({\n                                                id: parent.CategoryID,\n                                                name: parent.Name,\n                                                subcategories: childCategories.filter({\n                                                    \"Header.useEffect.fetchCategories.formattedCategories\": (child)=>child.ParentCategoryID === parent.CategoryID\n                                                }[\"Header.useEffect.fetchCategories.formattedCategories\"]).map({\n                                                    \"Header.useEffect.fetchCategories.formattedCategories\": (child)=>child.Name\n                                                }[\"Header.useEffect.fetchCategories.formattedCategories\"])\n                                            })\n                                    }[\"Header.useEffect.fetchCategories.formattedCategories\"]);\n                                    setCategories(formattedCategories);\n                                } else {\n                                    console.error('Categories data is not an array:', parsedData);\n                                    setCategories([]);\n                                }\n                            } catch (parseError) {\n                                console.error('Error parsing categories data:', parseError);\n                                setCategories([]);\n                            }\n                        } else if (categoriesResponse === null || categoriesResponse === void 0 ? void 0 : (_categoriesResponse_data1 = categoriesResponse.data) === null || _categoriesResponse_data1 === void 0 ? void 0 : _categoriesResponse_data1.errorMessage) {\n                            console.error('API Error:', categoriesResponse.data.errorMessage);\n                            setCategories([]);\n                        } else {\n                            console.error('Invalid or empty response from API');\n                            setCategories([]);\n                        }\n                    } catch (error) {\n                        console.error('Error fetching categories:', error);\n                        setCategories([]);\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"Header.useEffect.fetchCategories\"];\n            fetchCategories();\n        }\n    }[\"Header.useEffect\"], []);\n    // Update cart count when cart items change\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"Header.useEffect\": ()=>{\n            if (cart && cart.items) {\n                setCartCount(cart.totalItems);\n            }\n        }\n    }[\"Header.useEffect\"], [\n        cart,\n        cart === null || cart === void 0 ? void 0 : cart.items\n    ]);\n    // Update wishlist count when wishlist items change\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"Header.useEffect\": ()=>{\n            if (wishlist) {\n                setWishlistCount(wishlist.totalItems);\n            }\n        }\n    }[\"Header.useEffect\"], [\n        wishlist,\n        wishlist === null || wishlist === void 0 ? void 0 : wishlist.wishlistItems\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"w-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                variant: \"ghost\",\n                size: \"sm\",\n                className: \"fixed bottom-36 right-4 md:bottom-24 md:right-6 md:left-auto z-50 bg-gradient-to-r from-purple-500 via-pink-500 to-red-500 shadow-2xl rounded-2xl border-2 border-white/50 flex hover:scale-110 hover:-rotate-6 transition-all duration-300 hover:shadow-purple-500/50 group items-center justify-center w-14 h-14 md:w-16 md:h-16 hover:rounded-full\",\n                onClick: ()=>setShowColorPicker(true),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-8 w-8 md:h-10 md:w-10 rounded-xl group-hover:rounded-full ring-2 ring-white/80 group-hover:ring-4 transition-all duration-300 shadow-inner\",\n                            style: {\n                                backgroundColor: primaryColor\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                            lineNumber: 184,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute -top-2 -right-2 w-5 h-5 bg-gradient-to-br from-cyan-400 to-blue-500 rounded-full shadow-lg animate-spin group-hover:animate-pulse\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                            lineNumber: 185,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute -bottom-1 -left-1 w-3 h-3 bg-gradient-to-br from-green-400 to-emerald-500 rounded-full animate-bounce\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                            lineNumber: 186,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                    lineNumber: 183,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                lineNumber: 177,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hidden md:block py-2.5\",\n                style: {\n                    backgroundColor: primaryColor,\n                    color: primaryTextColor\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto flex flex-col md:flex-row justify-between items-center text-sm px-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex md:flex-row items-start justify-start gap-4 md:gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"tel:009647836071686\",\n                                    className: \"flex items-center gap-2 hover:text-white/80\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                            lineNumber: 195,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs md:text-sm\",\n                                            children: t('phone')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                            lineNumber: 196,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                    lineNumber: 194,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"mailto:<EMAIL>\",\n                                    className: \"flex items-center gap-2 hover:text-white/80\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                            lineNumber: 199,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs md:text-sm\",\n                                            children: t('email')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                            lineNumber: 200,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                    lineNumber: 198,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                            lineNumber: 193,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2 md:gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"sm\",\n                                    className: \"text-white hover:text-white/80 p-1 md:p-2 flex items-center gap-2\",\n                                    onClick: ()=>setLanguage(language === 'en' ? 'ar' : 'en'),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                            lineNumber: 205,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm\",\n                                            children: language === 'en' ? 'العربية' : 'English'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                            lineNumber: 206,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                    lineNumber: 204,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"sm\",\n                                    className: \"text-white hover:text-white/80 flex items-center gap-2\",\n                                    onClick: ()=>window.open(\"https://wa.me/9647836071686?text=\".concat(encodeURIComponent('Hello! I would like to chat with you regarding your services.')), '_blank'),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                            lineNumber: 214,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm\",\n                                            children: t('liveChat')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                            lineNumber: 215,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                    lineNumber: 208,\n                                    columnNumber: 13\n                                }, this),\n                                isLoggedIn ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_9__.Popover, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_9__.PopoverTrigger, {\n                                            asChild: true,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"sm\",\n                                                className: \"text-white hover:text-white/80 p-1 md:p-2 flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                        lineNumber: 221,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm\",\n                                                        children: [\n                                                            \"Welcome, \",\n                                                            (user === null || user === void 0 ? void 0 : user.FirstName) || (user === null || user === void 0 ? void 0 : user.UserName)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                        lineNumber: 222,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"h-3 w-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                        lineNumber: 223,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 220,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                            lineNumber: 219,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_9__.PopoverContent, {\n                                            className: \"w-48 p-2 bg-white border border-gray-200 shadow-lg\",\n                                            align: \"end\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/account\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                            variant: \"ghost\",\n                                                            size: \"sm\",\n                                                            className: \"w-full justify-start hover:bg-gray-50\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                    className: \"h-4 w-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                                    lineNumber: 230,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \"My Account\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                            lineNumber: 229,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                        lineNumber: 228,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/orders\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                            variant: \"ghost\",\n                                                            size: \"sm\",\n                                                            className: \"w-full justify-start hover:bg-gray-50\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    className: \"h-4 w-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                                    lineNumber: 236,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \"My Orders\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                            lineNumber: 235,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                        lineNumber: 234,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/addresses\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                            variant: \"ghost\",\n                                                            size: \"sm\",\n                                                            className: \"w-full justify-start hover:bg-gray-50\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                    className: \"h-4 w-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                                    lineNumber: 242,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \"My Addresses\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                            lineNumber: 241,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                        lineNumber: 240,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/wishlist\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                            variant: \"ghost\",\n                                                            size: \"sm\",\n                                                            className: \"w-full justify-start hover:bg-gray-50\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                    className: \"h-4 w-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                                    lineNumber: 248,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \"Wishlist\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                            lineNumber: 247,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                        lineNumber: 246,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"border-t border-gray-100 my-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                        lineNumber: 252,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                        variant: \"ghost\",\n                                                        size: \"sm\",\n                                                        className: \"w-full justify-start text-red-600 hover:text-red-700 hover:bg-red-50\",\n                                                        onClick: ()=>{\n                                                            logout();\n                                                            router.push('/');\n                                                            sonner__WEBPACK_IMPORTED_MODULE_6__.toast.success('Logged out successfully');\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                className: \"h-4 w-4 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                                lineNumber: 263,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \"Logout\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                        lineNumber: 253,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 227,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                            lineNumber: 226,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                    lineNumber: 218,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/login\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"sm\",\n                                                className: \"text-white hover:text-white/80 p-1 md:p-2 flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                        lineNumber: 273,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm\",\n                                                        children: t('login')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                        lineNumber: 274,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 272,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                            lineNumber: 271,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/signup\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"sm\",\n                                                className: \"text-white hover:text-white/80 p-1 md:p-2 flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                        lineNumber: 279,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm\",\n                                                        children: t('signUp')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                        lineNumber: 280,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 278,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                            lineNumber: 277,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                            lineNumber: 203,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                    lineNumber: 192,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                lineNumber: 191,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto py-4 px-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"md:hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-20\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                        lineNumber: 296,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/\",\n                                        className: \"flex items-center gap-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-[#1B3764] flex items-center gap-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: \"\".concat(\"https://admin.codemedicalapps.com/\", \"content/commonImages/otherImages/18b_logo2x.png\"),\n                                                alt: \"Logo\",\n                                                className: \"h-12 w-auto\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 301,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                            lineNumber: 300,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                        lineNumber: 299,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        className: \"flex items-center gap-2 px-3 py-2 rounded-full border border-gray-200 hover:bg-gray-50\",\n                                        onClick: ()=>setLanguage(language === 'en' ? 'ar' : 'en'),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-lg\",\n                                                children: language === 'en' ? '🇺🇸' : '🇮🇶'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 312,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium\",\n                                                children: language === 'en' ? 'EN' : 'AR'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 315,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                        lineNumber: 306,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                lineNumber: 294,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2 border rounded-full px-4 py-3 bg-background shadow-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            placeholder: t('products') || 'البحث عن المنتجات...',\n                                            className: \"bg-transparent border-none focus:outline-none text-sm flex-1 placeholder:text-gray-400\",\n                                            value: searchTerm,\n                                            onChange: (e)=>setSearchTerm(e.target.value),\n                                            onKeyDown: (e)=>e.key === 'Enter' && handleSearch()\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                            lineNumber: 324,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                            variant: \"ghost\",\n                                            className: \"h-8 w-8 p-0 hover:bg-accent/80 transition-colors\",\n                                            style: {\n                                                color: primaryColor\n                                            },\n                                            onClick: handleSearch,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 338,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                            lineNumber: 332,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                    lineNumber: 323,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                lineNumber: 322,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                        lineNumber: 292,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"hidden md:flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-4 flex-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/\",\n                                        className: \"flex items-center gap-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-[#1B3764] flex items-center gap-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: \"\".concat(\"https://admin.codemedicalapps.com/\", \"content/commonImages/otherImages/18b_logo2x.png\"),\n                                                alt: \"Logo\",\n                                                className: \"h-16 w-auto\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 350,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                            lineNumber: 349,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                        lineNumber: 348,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 border rounded-full px-3 py-1.5 flex-1 max-w-md ml-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_9__.Popover, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_9__.PopoverTrigger, {\n                                                        asChild: true,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                            variant: \"ghost\",\n                                                            className: \"h-8 flex items-center gap-1 px-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-muted-foreground text-sm\",\n                                                                    children: selectedCategory || selectedSubcategory || t('category')\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                                    lineNumber: 359,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-muted-foreground\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                                    lineNumber: 360,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                            lineNumber: 358,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                        lineNumber: 357,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_9__.PopoverContent, {\n                                                        className: \"w-64 p-0 bg-white border border-gray-200 shadow-lg\",\n                                                        align: \"start\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"max-h-[300px] overflow-auto\",\n                                                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-4 text-center text-muted-foreground\",\n                                                                children: t('loadingCategories')\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                                lineNumber: 366,\n                                                                columnNumber: 23\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid\",\n                                                                children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"group\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                className: \"w-full px-4 py-2 text-left hover:bg-gray-50\",\n                                                                                onClick: ()=>{\n                                                                                    setSelectedCategory(category.name);\n                                                                                    setSelectedCategoryId(category.id);\n                                                                                    setSelectedSubcategory(null);\n                                                                                },\n                                                                                children: category.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                                                lineNumber: 371,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"hidden group-hover:block absolute left-full top-0 w-48 bg-white shadow-lg rounded-md border border-gray-200\",\n                                                                                children: category.subcategories.map((sub, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        className: \"w-full px-4 py-2 text-left hover:bg-gray-50\",\n                                                                                        onClick: ()=>{\n                                                                                            setSelectedSubcategory(sub);\n                                                                                            setSelectedCategory(null);\n                                                                                            // Keep the parent category ID for search purposes\n                                                                                            setSelectedCategoryId(category.id);\n                                                                                        },\n                                                                                        children: sub\n                                                                                    }, index, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                                                        lineNumber: 380,\n                                                                                        columnNumber: 33\n                                                                                    }, this))\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                                                lineNumber: 378,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, category.id, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                                        lineNumber: 370,\n                                                                        columnNumber: 27\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                                lineNumber: 368,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                            lineNumber: 364,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                        lineNumber: 363,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 356,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-5 w-px bg-border mx-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 401,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                placeholder: t('products'),\n                                                className: \"bg-transparent border-none focus:outline-none text-sm flex-1\",\n                                                value: searchTerm,\n                                                onChange: (e)=>setSearchTerm(e.target.value),\n                                                onKeyDown: (e)=>e.key === 'Enter' && handleSearch()\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 402,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                variant: \"ghost\",\n                                                className: \"h-8 w-8 p-0\",\n                                                onClick: handleSearch,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                    lineNumber: 411,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 410,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                        lineNumber: 355,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                lineNumber: 346,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden md:block\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_8__.NavigationMenu, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_8__.NavigationMenuList, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_8__.NavigationMenuItem, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_8__.NavigationMenuLink, {\n                                                    asChild: true,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/\",\n                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_10__.cn)(\"group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50\"),\n                                                        children: t('home')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                        lineNumber: 422,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                    lineNumber: 421,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 420,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_8__.NavigationMenuItem, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_8__.NavigationMenuLink, {\n                                                    asChild: true,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/hot-deals\",\n                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_10__.cn)(\"group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50\"),\n                                                        children: t('hotDeals')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                        lineNumber: 431,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                    lineNumber: 430,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 429,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_8__.NavigationMenuItem, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_8__.NavigationMenuLink, {\n                                                    asChild: true,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/products\",\n                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_10__.cn)(\"group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50\"),\n                                                        children: t('products') || 'Products'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                        lineNumber: 440,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                    lineNumber: 439,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 438,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_8__.NavigationMenuItem, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_8__.NavigationMenuLink, {\n                                                    asChild: true,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/payment-methods\",\n                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_10__.cn)(\"group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50\"),\n                                                        children: t('paymentMethods')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                        lineNumber: 449,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                    lineNumber: 448,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 447,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_8__.NavigationMenuItem, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_8__.NavigationMenuLink, {\n                                                    asChild: true,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/follow-us\",\n                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_10__.cn)(\"group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50\"),\n                                                        children: t('followUs')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                        lineNumber: 458,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                    lineNumber: 457,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 456,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_8__.NavigationMenuItem, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_8__.NavigationMenuLink, {\n                                                    asChild: true,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/about\",\n                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_10__.cn)(\"group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50\"),\n                                                        children: t('aboutUs')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                        lineNumber: 467,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                    lineNumber: 466,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 465,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_8__.NavigationMenuItem, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_8__.NavigationMenuLink, {\n                                                    asChild: true,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/contact\",\n                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_10__.cn)(\"group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50\"),\n                                                        children: t('contactUs')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                        lineNumber: 476,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                    lineNumber: 475,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 474,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                        lineNumber: 419,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                    lineNumber: 418,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                lineNumber: 417,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"hidden md:flex items-center gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/wishlist\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                    variant: \"ghost\",\n                                                    size: \"icon\",\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                            className: \"h-5 w-5\",\n                                                            style: {\n                                                                color: primaryColor\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                            lineNumber: 493,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"absolute -top-1 -right-1 bg-primary text-white text-xs rounded-full h-4 w-4 flex items-center justify-center\",\n                                                            children: wishlistCount\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                            lineNumber: 494,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                    lineNumber: 492,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 491,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/cart\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                    variant: \"ghost\",\n                                                    size: \"icon\",\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                            className: \"h-5 w-5\",\n                                                            style: {\n                                                                color: primaryColor\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                            lineNumber: 501,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"absolute -top-1 -right-1 bg-primary text-white text-xs rounded-full h-4 w-4 flex items-center justify-center\",\n                                                            children: cartCount\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                            lineNumber: 502,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                    lineNumber: 500,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 499,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                        lineNumber: 490,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"icon\",\n                                        className: \"md:hidden\",\n                                        onClick: ()=>setShowMobileMenu(!showMobileMenu),\n                                        \"aria-label\": \"Toggle menu\",\n                                        children: showMobileMenu ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                            className: \"h-6 w-6\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                            lineNumber: 517,\n                                            columnNumber: 33\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                            className: \"h-6 w-6\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                            lineNumber: 517,\n                                            columnNumber: 61\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                        lineNumber: 510,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                lineNumber: 488,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                        lineNumber: 344,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                lineNumber: 290,\n                columnNumber: 7\n            }, this),\n            showMobileMenu && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"md:hidden bg-white border-t border-gray-200 shadow-lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: \"px-4 py-4 space-y-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/\",\n                            className: \"block px-4 py-3 text-gray-700 hover:bg-gray-50 hover:text-primary rounded-lg transition-colors\",\n                            onClick: ()=>setShowMobileMenu(false),\n                            children: t('home')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                            lineNumber: 526,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/hot-deals\",\n                            className: \"block px-4 py-3 text-gray-700 hover:bg-gray-50 hover:text-primary rounded-lg transition-colors\",\n                            onClick: ()=>setShowMobileMenu(false),\n                            children: t('hotDeals')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                            lineNumber: 533,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/products\",\n                            className: \"block px-4 py-3 text-gray-700 hover:bg-gray-50 hover:text-primary rounded-lg transition-colors\",\n                            onClick: ()=>setShowMobileMenu(false),\n                            children: t('products') || 'Products'\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                            lineNumber: 540,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/payment-methods\",\n                            className: \"block px-4 py-3 text-gray-700 hover:bg-gray-50 hover:text-primary rounded-lg transition-colors\",\n                            onClick: ()=>setShowMobileMenu(false),\n                            children: t('paymentMethods')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                            lineNumber: 547,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/follow-us\",\n                            className: \"block px-4 py-3 text-gray-700 hover:bg-gray-50 hover:text-primary rounded-lg transition-colors\",\n                            onClick: ()=>setShowMobileMenu(false),\n                            children: t('followUs')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                            lineNumber: 554,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/about\",\n                            className: \"block px-4 py-3 text-gray-700 hover:bg-gray-50 hover:text-primary rounded-lg transition-colors\",\n                            onClick: ()=>setShowMobileMenu(false),\n                            children: t('aboutUs')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                            lineNumber: 561,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/contact\",\n                            className: \"block px-4 py-3 text-gray-700 hover:bg-gray-50 hover:text-primary rounded-lg transition-colors\",\n                            onClick: ()=>setShowMobileMenu(false),\n                            children: t('contactUs')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                            lineNumber: 568,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-t border-gray-200 pt-4 mt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/wishlist\",\n                                    className: \"flex items-center gap-3 px-4 py-3 text-gray-700 hover:bg-gray-50 hover:text-primary rounded-lg transition-colors\",\n                                    onClick: ()=>setShowMobileMenu(false),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                            lineNumber: 583,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Wishlist\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                            lineNumber: 584,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"ml-auto bg-primary text-white text-xs rounded-full h-5 w-5 flex items-center justify-center\",\n                                            children: wishlistCount\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                            lineNumber: 585,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                    lineNumber: 578,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/cart\",\n                                    className: \"flex items-center gap-3 px-4 py-3 text-gray-700 hover:bg-gray-50 hover:text-primary rounded-lg transition-colors\",\n                                    onClick: ()=>setShowMobileMenu(false),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                            lineNumber: 594,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Cart\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                            lineNumber: 595,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"ml-auto bg-primary text-white text-xs rounded-full h-5 w-5 flex items-center justify-center\",\n                                            children: cartCount\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                            lineNumber: 596,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                    lineNumber: 589,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                            lineNumber: 577,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-t border-gray-200 pt-4 mt-4\",\n                            children: isLoggedIn ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"px-4 py-2 text-sm text-gray-500\",\n                                        children: [\n                                            \"Welcome, \",\n                                            (user === null || user === void 0 ? void 0 : user.FirstName) || (user === null || user === void 0 ? void 0 : user.UserName)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                        lineNumber: 606,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/account\",\n                                        className: \"flex items-center gap-3 px-4 py-3 text-gray-700 hover:bg-gray-50 hover:text-primary rounded-lg transition-colors\",\n                                        onClick: ()=>setShowMobileMenu(false),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 614,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"My Account\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 615,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                        lineNumber: 609,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/orders\",\n                                        className: \"flex items-center gap-3 px-4 py-3 text-gray-700 hover:bg-gray-50 hover:text-primary rounded-lg transition-colors\",\n                                        onClick: ()=>setShowMobileMenu(false),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 622,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"My Orders\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 623,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                        lineNumber: 617,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/addresses\",\n                                        className: \"flex items-center gap-3 px-4 py-3 text-gray-700 hover:bg-gray-50 hover:text-primary rounded-lg transition-colors\",\n                                        onClick: ()=>setShowMobileMenu(false),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 630,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"My Addresses\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 631,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                        lineNumber: 625,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"flex items-center gap-3 w-full px-4 py-3 text-red-600 hover:bg-red-50 rounded-lg transition-colors\",\n                                        onClick: ()=>{\n                                            logout();\n                                            setShowMobileMenu(false);\n                                            router.push('/');\n                                            sonner__WEBPACK_IMPORTED_MODULE_6__.toast.success('Logged out successfully');\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 642,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Logout\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 643,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                        lineNumber: 633,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/login\",\n                                        className: \"flex items-center gap-3 px-4 py-3 text-gray-700 hover:bg-gray-50 hover:text-primary rounded-lg transition-colors\",\n                                        onClick: ()=>setShowMobileMenu(false),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 653,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: t('login')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 654,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                        lineNumber: 648,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/signup\",\n                                        className: \"flex items-center gap-3 px-4 py-3 text-gray-700 hover:bg-gray-50 hover:text-primary rounded-lg transition-colors\",\n                                        onClick: ()=>setShowMobileMenu(false),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Heart_LogOut_Mail_MapPin_Menu_MessageCircle_Package_Phone_Search_ShoppingCart_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 661,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: t('signUp')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                                lineNumber: 662,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                                        lineNumber: 656,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                            lineNumber: 603,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                    lineNumber: 525,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                lineNumber: 524,\n                columnNumber: 9\n            }, this),\n            showColorPicker && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_color_picker__WEBPACK_IMPORTED_MODULE_14__.ColorPicker, {\n                onColorSelect: (color)=>{\n                    setPrimaryColor(color);\n                    setShowColorPicker(false);\n                },\n                onClose: ()=>setShowColorPicker(false)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n                lineNumber: 672,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\header.tsx\",\n        lineNumber: 176,\n        columnNumber: 5\n    }, this);\n}\n_s(Header, \"TB0htKxrnyj7Nsrnl0veiZ09D1k=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_1__.usePathname,\n        _contexts_cart_context__WEBPACK_IMPORTED_MODULE_4__.useCart,\n        _contexts_wishlist_context__WEBPACK_IMPORTED_MODULE_5__.useWishlist,\n        _contexts_user_context__WEBPACK_IMPORTED_MODULE_13__.useUser,\n        _contexts_settings_context__WEBPACK_IMPORTED_MODULE_12__.useSettings\n    ];\n});\n_c = Header;\nvar _c;\n$RefreshReg$(_c, \"Header\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/ui/header.tsx\n"));

/***/ })

});